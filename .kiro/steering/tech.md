# Technology Stack & Build System

## Core Technologies

- **Runtime**: Node.js with TypeScript
- **Framework**: NestJS (microservices architecture)
- **Package Manager**: pnpm
- **Build Tool**: Webpack with HMR support
- **Database ORM**: Drizzle ORM

## Architecture

- **Pattern**: Microservices with monorepo structure
- **Communication**: gRPC (sync), Redis transport (async)
- **State Management**: XState for workflow resilience

## Databases

- **PostgreSQL**: Primary relational database for master data
- **QuestDB**: Time-series database for market data (tick data, candles)
- **Redis/Dragonfly**: Caching, sessions, and message queuing
- **DuckDB**: Local analytics and temporary data processing

## Key Libraries & Tools

- **Validation**: Zod schemas with nestjs-zod
- **Logging**: Pino logger with structured logging
- **Caching**: cache-manager with Redis store
- **Sessions**: express-session with Redis store
- **Scheduling**: @nestjs/schedule for cron jobs
- **Queue**: BullMQ for background job processing
- **WebSockets**: socket.io for real-time communication
- **Security**: helmet for security headers

## Development Tools

- **Linting**: ESLint with TypeScript rules
- **Formatting**: Prettier
- **Git Hooks**: Husky with lint-staged
- **Testing**: Jest
- **Hot Reload**: Webpack HMR configuration

## Common Commands

### Development

```bash
# Start development server with HMR
pnpm run start:dev

# Start specific application
pnpm run start:dev:fast <app-name>

# Start with debugging
pnpm run start:debug
```

### Database Operations

```bash
# Generate migrations
pnpm run db:generate

# Run migrations
pnpm run db:migrate

# Push schema changes
pnpm run db:push

# Open Drizzle Studio
pnpm run db:studio

# Reset database with seed data
pnpm run db:reset
```

### Code Quality

```bash
# Format and lint
pnpm run lint:fix

# Run tests
pnpm run test

# Run tests with coverage
pnpm run test:cov

# Run e2e tests
pnpm run test:e2e
```

### Build & Deploy

```bash
# Build for production
pnpm run build

# Start production server
pnpm run start:prod
```

## Environment Configuration

- Uses dotenv for environment variables
- Environment-specific configurations in `env/` folder
- Supports multiple environments: local, docker, development, staging, production

## Code Style Preferences

- Strict TypeScript with `noImplicitAny: false` for gradual typing
- Consistent type imports with `@typescript-eslint/consistent-type-imports`
- Path aliases using `@app/*` for library imports
- Zod schemas for runtime validation and type generation
