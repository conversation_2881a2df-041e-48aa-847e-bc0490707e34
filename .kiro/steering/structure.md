# Project Structure & Organization

## Monorepo Architecture

This is a NestJS monorepo with microservices architecture, organized into applications and shared libraries.

## Top-Level Structure

```
├── apps/           # Microservice applications
├── libs/           # Shared libraries
├── docs/           # Documentation
├── env/            # Environment configurations
├── scripts/        # Utility scripts
├── .kiro/          # Kiro AI assistant configuration
├── .husky/         # Git hooks
└── drizzle/        # Database migrations (generated)
```

## Applications (`apps/`)

Each application is a deployable microservice:

- **api**: Main API gateway and web interface
- **oms**: Order Management System for trade execution
- **ticker**: Real-time market data ingestion and processing
- **analyser**: Strategy execution and signal generation
- **simulator**: Paper trading simulation environment

### Application Structure

```
apps/<app-name>/
├── src/
│   ├── main.ts           # Application entry point
│   ├── app.module.ts     # Root module
│   ├── app.controller.ts # Main controller
│   └── app.service.ts    # Main service
└── tsconfig.app.json     # App-specific TypeScript config
```

## Libraries (`libs/`)

Shared business logic and utilities:

### Core Libraries

- **core**: Fundamental services (database, cache, logging, health checks)
- **common**: Shared constants, enums, errors, filters, schemas
- **utils**: General utility functions and helpers

### Domain Libraries

- **broker**: Broker integration and management logic

### Library Structure

```
libs/<lib-name>/
├── src/
│   ├── index.ts          # Public API exports
│   └── <modules>/        # Feature modules
└── tsconfig.lib.json     # Library TypeScript config
```

## Path Aliases

Use TypeScript path aliases for clean imports:

```typescript
import { SomeService } from '@app/core';
import { CommonModule } from '@app/common';
import { UtilsService } from '@app/utils';
import { BrokerModule } from '@app/broker';
```

## File Naming Conventions

- **Modules**: `*.module.ts`
- **Services**: `*.service.ts`
- **Controllers**: `*.controller.ts`
- **Schemas**: `*.schema.ts`
- **Constants**: `*.constants.ts`
- **Errors**: `*.error.ts`
- **Types**: `*.types.ts`
- **Interfaces**: `*.interface.ts`

## Module Organization Patterns

- Group related functionality in feature modules
- Use barrel exports (`index.ts`) for clean public APIs
- Separate concerns: controllers, services, schemas, types
- Keep database schemas in dedicated schema files

## Configuration Management

- Environment variables in `env/` folder
- Drizzle database configuration in `drizzle.config.ts`
- Application-specific configs in respective app folders
- Global TypeScript config in root `tsconfig.json`

## Database Schema Location

- Shared database schemas: `libs/common/src/db-schema/`
- Drizzle configuration points to: `./libs/shared/src/db-schema/*`

## Testing Structure

- Unit tests: `*.spec.ts` alongside source files
- E2E tests: `apps/<app>/test/` directory
- Test configuration in `jest` section of `package.json`

## Import Guidelines

1. Use path aliases (`@app/*`) for internal libraries
2. Import types with `type` keyword for type-only imports
3. Group imports: external libraries, then internal modules
4. Use barrel exports for cleaner import statements

## Docker & Deployment

- `Dockerfile` for containerization
- `docker-compose.yml` for local development stack
- `Tiltfile` for development workflow automation
