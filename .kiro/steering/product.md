# Pattern Trade (PT) - Product Overview

Pattern Trade is a Personal Finance Assistant designed as an Algo Trading Platform for middle-class individuals. The platform enables autonomous income growth through systematic algorithmic trading.

## Core Purpose

- Empower users with customizable algorithmic trading strategies
- Bridge the gap between complex trading systems and accessible tools
- Automate trading decisions and execution to maximize returns
- Support multiple brokers and asset classes (Equity, F&O, Currency, Commodities)

## Key Features

- **Automated Trading**: Execute trades based on predefined strategies
- **Multi-Broker Integration**: Support for Zerodha, Alice Blue, Finvasia, Upstox, Dhan, Angel One
- **Real-time Data Processing**: Per-second tick data aggregated into 1-minute candles
- **Strategy Management**: Define, backtest, and optimize trading strategies
- **Order Management**: Advanced order types including trailing stop-loss, order slicing
- **Paper Trading**: Simulate trades without real money for testing
- **Comprehensive Analytics**: Performance tracking, reporting, and observability

## User Types

- **Admin**: System administrators who manage strategies, brokers, and user access
- **Normal User**: Traders who execute automated strategies on their broker accounts

## Trading Styles Supported

- Positional/Swing Trading (primary focus)
- Intraday Trading (planned)
- Long-term Investment (planned)
