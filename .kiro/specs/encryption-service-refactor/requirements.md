# Requirements Document

## Introduction

This feature involves refactoring the encryption service from the broker library into a standalone module within the common library, and moving the broker controller from the broker library to the API application with proper nestjs-zod integration. This refactoring will improve code organization, reusability, and maintainability by separating concerns and following established patterns.

## Requirements

### Requirement 1

**User Story:** As a developer, I want the encryption service to be available as a shared module in the common library, so that it can be reused across different parts of the application without being tied to the broker domain.

#### Acceptance Criteria

1. WHEN the encryption service is moved to the common library THEN it SHALL be located in `libs/common/src/encryption/`
2. WHEN the encryption service is moved THEN it SHALL maintain all existing functionality including encrypt, decrypt, encryptObject, and decryptObject methods
3. WHEN the encryption service is moved THEN it SHALL be properly exported from the common library index
4. WHEN the encryption service is moved THEN it SHALL be available as an injectable service in other modules
5. WHEN the encryption service is moved THEN all existing tests SHALL continue to pass

### Requirement 2

**User Story:** As a developer, I want the broker controller to be moved to the API application, so that it follows the established pattern of having controllers in the application layer rather than in library modules.

#### Acceptance Criteria

1. WHEN the broker controller is moved THEN it SHALL be located in `apps/api/src/broker/`
2. WHEN the broker controller is moved THEN it SHALL maintain all existing endpoints and functionality
3. WHEN the broker controller is moved THEN it SHALL use nestjs-zod integration following the same pattern as the user controller
4. WHEN the broker controller is moved THEN it SHALL properly import and use the moved encryption service from the common library
5. WHEN the broker controller is moved THEN all existing route handlers SHALL continue to work correctly

### Requirement 3

**User Story:** As a developer, I want the broker controller to use nestjs-zod for validation, so that it follows the established validation patterns and provides consistent error handling across the application.

#### Acceptance Criteria

1. WHEN the broker controller uses nestjs-zod THEN it SHALL replace the custom ZodValidationPipe with createZodDto from nestjs-zod
2. WHEN the broker controller uses nestjs-zod THEN it SHALL create DTO classes for all request schemas using createZodDto
3. WHEN the broker controller uses nestjs-zod THEN it SHALL maintain the same validation behavior as the current implementation
4. WHEN the broker controller uses nestjs-zod THEN it SHALL provide consistent error messages and validation responses
5. WHEN the broker controller uses nestjs-zod THEN it SHALL follow the same pattern established in the user controller

### Requirement 4

**User Story:** As a developer, I want proper module organization and imports, so that the refactored code maintains clean dependencies and follows NestJS best practices.

#### Acceptance Criteria

1. WHEN the encryption service is moved THEN the broker library SHALL update its imports to use the encryption service from the common library
2. WHEN the broker controller is moved THEN the API application SHALL have a proper broker module that imports necessary dependencies
3. WHEN the refactoring is complete THEN all circular dependencies SHALL be avoided
4. WHEN the refactoring is complete THEN all imports SHALL use the correct path aliases (@app/common, @app/broker, etc.)
5. WHEN the refactoring is complete THEN the broker library SHALL no longer contain the controller or custom validation pipe

### Requirement 5

**User Story:** As a developer, I want the refactored code to maintain backward compatibility, so that existing functionality continues to work without breaking changes.

#### Acceptance Criteria

1. WHEN the refactoring is complete THEN all existing API endpoints SHALL continue to work with the same URLs and behavior
2. WHEN the refactoring is complete THEN all existing service methods SHALL maintain their signatures and functionality
3. WHEN the refactoring is complete THEN all existing error handling SHALL continue to work as expected
4. WHEN the refactoring is complete THEN all existing authentication and authorization SHALL continue to work
5. WHEN the refactoring is complete THEN all existing tests SHALL pass without modification
