# Design Document

## Overview

This design outlines the refactoring of the encryption service from the broker library to the common library, and the migration of the broker controller from the broker library to the API application. The refactoring will improve code organization by separating concerns, making the encryption service reusable across the application, and following established patterns for controller placement and validation.

## Architecture

### Current Architecture

- Encryption service is located in `libs/broker/src/encryption.service.ts`
- Broker controller is located in `libs/broker/src/broker.controller.ts`
- Controller uses custom ZodValidationPipe for request validation
- Broker library contains both business logic and API layer components

### Target Architecture

- Encryption service will be moved to `libs/common/src/encryption/`
- Broker controller will be moved to `apps/api/src/broker/`
- Controller will use nestjs-zod createZodDto pattern for validation
- Clear separation between library (business logic) and application (API) layers

## Components and Interfaces

### 1. Encryption Module (New)

**Location:** `libs/common/src/encryption/`

**Files:**

- `encryption.service.ts` - Main encryption service (moved from broker)
- `encryption.module.ts` - NestJS module definition
- `encryption.interface.ts` - TypeScript interfaces for encryption operations
- `index.ts` - Barrel export file

**Key Interfaces:**

```typescript
export interface EncryptedObject {
  data: string;
  iv: string;
  authTag: string;
  algorithm: string;
  keyVersion: string;
}

export interface IEncryptionService {
  encrypt(data: string): string;
  decrypt(encryptedData: string): string;
  encryptObject<T>(obj: T): EncryptedObject;
  decryptObject<T>(encryptedObj: EncryptedObject): T;
  validateEncryption(data: string, encrypted: string): boolean;
  rotateKeys(): void;
}
```

### 2. Broker Controller (Moved)

**Location:** `apps/api/src/broker/`

**Files:**

- `broker.controller.ts` - Main controller (moved from broker lib)
- `broker.module.ts` - NestJS module for broker API endpoints
- `broker.dto.ts` - DTO classes created with nestjs-zod
- `index.ts` - Barrel export file

**Key Changes:**

- Replace custom ZodValidationPipe with nestjs-zod DTOs
- Import encryption service from common library
- Maintain all existing endpoints and functionality

### 3. Updated Broker Library

**Location:** `libs/broker/src/`

**Changes:**

- Remove `broker.controller.ts`
- Remove custom ZodValidationPipe
- Update imports to use encryption service from common library
- Update module exports to exclude controller

## Data Models

### Encryption Service Dependencies

```typescript
// Dependencies remain the same
- EnvService from @app/core/env
- BrokerError from @app/broker (for error handling)
- Node.js crypto module
```

### Controller Dependencies

```typescript
// Updated dependencies
- EncryptionService from @app/common/encryption (new import)
- BrokerService from @app/broker
- BrokerAuthService from @app/broker
- BrokerHealthService from @app/broker
- createZodDto from nestjs-zod (new import)
- All existing schemas from @app/broker/broker.schema
```

## Error Handling

### Encryption Service Error Handling

- Maintain existing BrokerError usage for consistency
- All error types and messages remain unchanged
- Error context and correlation IDs preserved

### Controller Error Handling

- Maintain existing error handling patterns
- Continue using BrokerError for business logic errors
- Preserve all HTTP status code mappings
- Keep audit logging and security event logging

## Testing Strategy

### Unit Tests

1. **Encryption Service Tests**
   - Move existing encryption service tests to common library
   - Ensure all test cases continue to pass
   - Add tests for module integration

2. **Controller Tests**
   - Move existing controller tests to API application
   - Update import paths in test files
   - Verify nestjs-zod integration works correctly
   - Test all endpoints maintain same behavior

### Integration Tests

1. **Module Integration**
   - Test encryption service can be imported and used by broker services
   - Verify controller can access all required dependencies
   - Test end-to-end API functionality

2. **Backward Compatibility**
   - Verify all existing API endpoints work unchanged
   - Test authentication and authorization flows
   - Validate error responses match existing format

## Implementation Phases

### Phase 1: Create Encryption Module in Common Library

1. Create encryption module structure in common library
2. Move encryption service with minimal changes
3. Create proper module definition and exports
4. Update common library index to export encryption module

### Phase 2: Update Broker Library Dependencies

1. Update broker library to import encryption service from common
2. Remove encryption service from broker library
3. Update broker module imports and providers
4. Run tests to ensure functionality is preserved

### Phase 3: Create Broker Module in API Application

1. Create broker module structure in API application
2. Move broker controller to API application
3. Create nestjs-zod DTOs to replace custom validation pipe
4. Update controller imports and dependencies

### Phase 4: Update API Application Module Structure

1. Update API application main module to include broker module
2. Ensure proper dependency injection configuration
3. Update any existing route configurations

### Phase 5: Cleanup and Testing

1. Remove controller and validation pipe from broker library
2. Update all import paths and module exports
3. Run comprehensive tests to verify functionality
4. Update documentation and type exports

## Migration Considerations

### Breaking Changes

- No breaking changes to public APIs
- Internal import paths will change but are not public interfaces
- All existing functionality preserved

### Dependencies

- Common library will have new dependency on @app/core for EnvService
- API application will have new dependency on @app/common/encryption
- Broker library dependency on encryption service changes from internal to external

### Configuration

- No configuration changes required
- Environment variables remain the same
- Module registration patterns follow existing conventions

## Validation Pattern Migration

### Current Pattern (Custom Pipe)

```typescript
@Injectable()
class ZodValidationPipe<T extends z.ZodSchema> implements PipeTransform {
  constructor(private schema: T) {}
  transform(value: unknown, _metadata: ArgumentMetadata): z.output<T> {
    // Custom validation logic
  }
}

// Usage
@Post('accounts')
async createBrokerAccount(
  @Body(new ZodValidationPipe(CreateBrokerAccountSchema)) createData: CreateBrokerAccountRequest,
) { }
```

### Target Pattern (nestjs-zod)

```typescript
// DTO creation
class CreateBrokerAccountDto extends createZodDto(CreateBrokerAccountSchema) {}

// Usage
@Post('accounts')
async createBrokerAccount(
  @Body() createData: CreateBrokerAccountDto,
) { }
```

This migration will provide better integration with NestJS validation pipeline and consistent error handling across the application.
