# Implementation Plan

- [x] 1. Create encryption module structure in common library
  - Create directory structure for encryption module in libs/common/src/encryption/
  - Create encryption.interface.ts with TypeScript interfaces for encryption operations
  - Create encryption.module.ts with NestJS module definition
  - Create index.ts barrel export file for the encryption module
  - _Requirements: 1.1, 1.3_

- [x] 2. Move encryption service to common library
  - Copy encryption.service.ts from libs/broker/src/ to libs/common/src/encryption/
  - Update import paths in encryption service to use correct dependencies
  - Implement IEncryptionService interface in the moved service
  - Update encryption service to maintain all existing functionality
  - _Requirements: 1.2, 1.4_

- [x] 3. Update common library exports and module registration
  - Update libs/common/src/index.ts to export encryption module
  - Register EncryptionModule in common library module structure
  - Ensure encryption service is properly injectable from common library
  - _Requirements: 1.3, 4.4_

- [x] 4. Update broker library to use encryption service from common
  - Update import statements in broker services to use @app/common/encryption
  - Remove encryption.service.ts from libs/broker/src/
  - Update broker.module.ts to import EncryptionModule from common library
  - Update broker service dependencies to inject encryption service from common
  - _Requirements: 4.1, 4.4_

- [x] 5. Create broker module structure in API application
  - Create apps/api/src/broker/ directory structure
  - Create broker.module.ts in API application with proper imports
  - Set up module to import required broker services and encryption service
  - Create index.ts barrel export file for broker API module
  - _Requirements: 2.1, 4.2_

- [x] 6. Create nestjs-zod DTOs for broker controller
  - Create broker.dto.ts file in apps/api/src/broker/
  - Implement DTO classes using createZodDto for all broker request schemas
  - Create DTOs for CreateBrokerAccountSchema, UpdateBrokerAccountSchema, BrokerAccountFiltersSchema
  - Create DTOs for OAuthCallbackSchema, AccountIdParamSchema, AccountIdStringParamSchema
  - _Requirements: 3.1, 3.2_

- [x] 7. Move and update broker controller
  - Copy broker.controller.ts from libs/broker/src/ to apps/api/src/broker/
  - Replace custom ZodValidationPipe usage with nestjs-zod DTOs
  - Update import statements to use correct paths for services and DTOs
  - Update controller to use encryption service from common library
  - _Requirements: 2.2, 2.4, 3.3_

- [x] 8. Update controller method signatures and validation
  - Replace all @Body(new ZodValidationPipe(...)) with @Body() and DTO types
  - Replace all @Param(new ZodValidationPipe(...)) with @Param() and DTO types
  - Replace all @Query(new ZodValidationPipe(...)) with @Query() and DTO types
  - Ensure all validation behavior remains consistent with previous implementation
  - _Requirements: 3.3, 3.4_

- [x] 9. Update API application main module
  - Update apps/api/src/app.module.ts to import BrokerModule
  - Ensure proper dependency injection configuration for broker endpoints
  - Verify all required services are available in the application context
  - _Requirements: 4.2, 4.4_

- [x] 10. Remove controller and validation pipe from broker library
  - Delete broker.controller.ts from libs/broker/src/
  - Remove custom ZodValidationPipe class from broker library
  - Update libs/broker/src/index.ts to remove controller exports
  - Update broker.module.ts to remove controller from providers and exports
  - _Requirements: 4.5_

- [x] 11. Update all import paths and fix circular dependencies
  - Verify all import statements use correct path aliases (@app/common, @app/broker)
  - Check for and resolve any circular dependency issues
  - Update any remaining references to old encryption service location
  - Ensure clean separation between library and application layers
  - _Requirements: 4.3, 4.4_

- [x] 12. Run comprehensive tests and verify functionality
  - Execute all existing unit tests to ensure they pass
  - Test all API endpoints to verify they work with same URLs and behavior
  - Verify authentication and authorization continue to work correctly
  - Test error handling and validation responses match existing format
  - _Requirements: 1.5, 2.5, 5.1, 5.2, 5.3, 5.4, 5.5_
