# Design Document

## Overview

The broker authentication and management system provides secure, scalable infrastructure for managing broker account connections, OAuth2 authentication flows, encrypted credential storage, and real-time connection monitoring. The system follows PatternTrade's existing architectural patterns using NestJS, TypeScript, Drizzle ORM, and implements comprehensive security measures for handling sensitive broker credentials.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "API Layer"
        BC[BrokerController]
        AC[AuthController]
    end

    subgraph "Service Layer"
        BS[BrokerService]
        BAS[BrokerAuthService]
        BHS[BrokerHealthService]
        ES[EncryptionService]
    end

    subgraph "Repository Layer"
        BR[BrokerRepository]
        BCSR[BrokerConnectionStatusRepository]
    end

    subgraph "External Services"
        KC[KiteConnect API]
        OB[Other Brokers]
    end

    subgraph "Database"
        BAT[broker_accounts]
        BCST[broker_connection_status]
        BHST[broker_health_stats]
    end

    subgraph "Infrastructure"
        REDIS[(Redis Cache)]
        ENC[Encryption Keys]
    end

    BC --> BS
    BC --> BAS
    BC --> BHS
    BS --> BR
    BS --> ES
    BAS --> KC
    BAS --> OB
    BR --> BAT
    BCSR --> BCST
    BHS --> BHST
    BS --> REDIS
    ES --> ENC
```

### Security Architecture

```mermaid
graph LR
    subgraph "Security Layers"
        A[Authentication] --> B[Authorization]
        B --> C[Encryption]
        C --> D[Audit Logging]
    end

    subgraph "Encryption Flow"
        E[Plain Credentials] --> F[AES-256 Encryption]
        F --> G[Encrypted Storage]
        G --> H[Decryption on Access]
    end

    subgraph "OAuth2 Flow"
        I[Authorization Request] --> J[User Consent]
        J --> K[Authorization Code]
        K --> L[Token Exchange]
        L --> M[Secure Token Storage]
    end
```

## Components and Interfaces

### 1. BrokerController

**Responsibilities:**

- Handle HTTP requests for broker account management
- Implement role-based access control
- Validate request data using Zod schemas
- Handle OAuth2 callback processing

**Key Methods:**

```typescript
interface IBrokerController {
  createBrokerAccount(data: CreateBrokerAccountRequest): Promise<BrokerAccountResponse>;
  getBrokerAccounts(filters?: BrokerAccountFilters): Promise<BrokerAccountResponse[]>;
  getBrokerAccount(id: string): Promise<BrokerAccountResponse>;
  updateBrokerAccount(id: string, data: UpdateBrokerAccountRequest): Promise<BrokerAccountResponse>;
  deleteBrokerAccount(id: string): Promise<void>;
  handleOAuthCallback(code: string, state: string): Promise<OAuthCallbackResponse>;
  getBrokerHealth(): Promise<BrokerHealthResponse>;
  reconnectBroker(id: string): Promise<ReconnectionResponse>;
}
```

### 2. BrokerService

**Responsibilities:**

- Core business logic for broker account management
- Coordinate with encryption service for secure data handling
- Manage broker account lifecycle
- Implement connection management logic

**Key Methods:**

```typescript
interface IBrokerService {
  createAccount(data: CreateBrokerAccountData): Promise<BrokerAccount>;
  findAccountById(id: string): Promise<BrokerAccount | null>;
  findAccountsByUserId(userId: string): Promise<BrokerAccount[]>;
  updateAccount(id: string, updates: UpdateBrokerAccountData): Promise<BrokerAccount>;
  deleteAccount(id: string): Promise<void>;
  validateBrokerCredentials(brokerType: BrokerType, credentials: BrokerCredentials): Promise<boolean>;
  refreshTokens(accountId: string): Promise<void>;
}
```

### 3. BrokerAuthService

**Responsibilities:**

- Handle OAuth2 authentication flows using KiteConnect JS library
- Manage token lifecycle (access, refresh) through KiteConnect methods
- Implement Zerodha-specific authentication logic using official SDK
- Handle authentication errors and retries

**Key Methods:**

```typescript
interface IBrokerAuthService {
  initiateOAuth(brokerType: BrokerType, userId: string): Promise<OAuthInitiationResponse>;
  handleOAuthCallback(code: string, state: string): Promise<OAuthTokens>;
  refreshAccessToken(accountId: string): Promise<OAuthTokens>;
  validateTokens(accountId: string): Promise<boolean>;
  revokeTokens(accountId: string): Promise<void>;
  getKiteConnectInstance(apiKey: string, accessToken?: string): KiteConnect;
}
```

### 4. BrokerHealthService

**Responsibilities:**

- Monitor broker connection status
- Implement health checks and heartbeat mechanisms
- Track connection statistics and metrics
- Handle automatic reconnection logic

**Key Methods:**

```typescript
interface IBrokerHealthService {
  checkConnectionHealth(accountId: string): Promise<ConnectionHealth>;
  getAllConnectionsHealth(): Promise<ConnectionHealth[]>;
  updateConnectionStatus(accountId: string, status: ConnectionStatus): Promise<void>;
  getConnectionStatistics(accountId: string): Promise<ConnectionStatistics>;
  initiateReconnection(accountId: string): Promise<ReconnectionResult>;
  scheduleHealthChecks(): void;
}
```

### 5. EncryptionService

**Responsibilities:**

- Encrypt/decrypt sensitive broker data
- Manage encryption keys securely
- Implement key rotation strategies
- Ensure compliance with security standards

**Key Methods:**

```typescript
interface IEncryptionService {
  encrypt(data: string): Promise<string>;
  decrypt(encryptedData: string): Promise<string>;
  encryptObject<T>(obj: T): Promise<EncryptedObject>;
  decryptObject<T>(encryptedObj: EncryptedObject): Promise<T>;
  rotateKeys(): Promise<void>;
  validateEncryption(data: string, encrypted: string): Promise<boolean>;
}
```

### 6. BrokerRepository

**Responsibilities:**

- Data access layer for broker accounts
- Implement CRUD operations with encryption
- Handle database transactions
- Provide query methods with filtering

**Key Methods:**

```typescript
interface IBrokerRepository {
  create(data: CreateBrokerAccountData): Promise<BrokerAccount>;
  findById(id: string): Promise<BrokerAccount | null>;
  findByUserId(userId: string): Promise<BrokerAccount[]>;
  findByBrokerType(brokerType: BrokerType): Promise<BrokerAccount[]>;
  update(id: string, updates: UpdateBrokerAccountData): Promise<BrokerAccount>;
  delete(id: string): Promise<void>;
  findActiveAccounts(): Promise<BrokerAccount[]>;
}
```

## Data Models

### Database Schema

```sql
-- Broker Accounts Table
CREATE TABLE broker_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  broker_type VARCHAR(50) NOT NULL,
  account_name VARCHAR(255) NOT NULL,
  is_admin_account BOOLEAN DEFAULT FALSE,

  -- Encrypted fields
  encrypted_api_key TEXT NOT NULL,
  encrypted_api_secret TEXT NOT NULL,
  encrypted_access_token TEXT,
  encrypted_refresh_token TEXT,

  -- Connection status
  connection_status VARCHAR(50) DEFAULT 'DISCONNECTED',
  last_connected_at TIMESTAMP,
  last_error TEXT,

  -- Audit fields
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by TEXT,
  updated_by TEXT,

  -- Constraints
  UNIQUE(user_id, broker_type),
  INDEX idx_broker_accounts_user_id (user_id),
  INDEX idx_broker_accounts_broker_type (broker_type),
  INDEX idx_broker_accounts_status (connection_status)
);

-- Broker Connection Status Table
CREATE TABLE broker_connection_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  broker_account_id UUID NOT NULL REFERENCES broker_accounts(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL,
  status_changed_at TIMESTAMP DEFAULT NOW(),
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  next_retry_at TIMESTAMP,

  INDEX idx_connection_status_account (broker_account_id),
  INDEX idx_connection_status_timestamp (status_changed_at)
);

-- Broker Health Statistics Table
CREATE TABLE broker_health_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  broker_account_id UUID NOT NULL REFERENCES broker_accounts(id) ON DELETE CASCADE,

  -- Connection metrics
  total_connections INTEGER DEFAULT 0,
  successful_connections INTEGER DEFAULT 0,
  failed_connections INTEGER DEFAULT 0,
  current_uptime_seconds INTEGER DEFAULT 0,
  total_downtime_seconds INTEGER DEFAULT 0,

  -- Performance metrics
  average_response_time_ms DECIMAL(10,2),
  last_heartbeat_at TIMESTAMP,
  last_error_at TIMESTAMP,
  last_error_message TEXT,

  -- Timestamps
  stats_date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  UNIQUE(broker_account_id, stats_date),
  INDEX idx_health_stats_account (broker_account_id),
  INDEX idx_health_stats_date (stats_date)
);
```

### TypeScript Models

```typescript
// Core broker account model
export interface BrokerAccount {
  id: string;
  userId: string;
  brokerType: BrokerType;
  accountName: string;
  isAdminAccount: boolean;
  connectionStatus: BrokerConnectionStatus;
  lastConnectedAt?: Date;
  lastError?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
}

// Encrypted credentials (stored separately)
export interface BrokerCredentials {
  apiKey: string;
  apiSecret: string;
  accessToken?: string;
  refreshToken?: string;
}

// Connection health model
export interface ConnectionHealth {
  accountId: string;
  status: BrokerConnectionStatus;
  lastCheckedAt: Date;
  responseTime?: number;
  errorMessage?: string;
  uptime: number;
  failureCount: number;
}

// OAuth tokens model
export interface OAuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt: Date;
  tokenType: string;
  scope?: string;
}
```

## Error Handling

### Error Types and Hierarchy

```typescript
export enum BrokerErrorType {
  // Authentication errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  OAUTH_FAILED = 'OAUTH_FAILED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED',

  // Connection errors
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  CONNECTION_TIMEOUT = 'CONNECTION_TIMEOUT',
  BROKER_UNAVAILABLE = 'BROKER_UNAVAILABLE',

  // Authorization errors
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_NOT_FOUND = 'ACCOUNT_NOT_FOUND',
  DUPLICATE_ACCOUNT = 'DUPLICATE_ACCOUNT',

  // Encryption errors
  ENCRYPTION_FAILED = 'ENCRYPTION_FAILED',
  DECRYPTION_FAILED = 'DECRYPTION_FAILED',
  KEY_ROTATION_FAILED = 'KEY_ROTATION_FAILED',

  // System errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}
```

### Error Handling Strategy

1. **Graceful Degradation**: System continues operating with reduced functionality when broker connections fail
2. **Retry Logic**: Exponential backoff for transient failures
3. **Circuit Breaker**: Prevent cascading failures by temporarily disabling problematic brokers
4. **Comprehensive Logging**: Detailed error context for debugging and monitoring
5. **User-Friendly Messages**: Clear error messages without exposing sensitive information

## Build and Quality Assurance Strategy

### Code Quality Standards

- **TypeScript Compilation**: All code must compile without TypeScript errors
- **ESLint Compliance**: Code must pass all linting rules defined in project configuration
- **Import Resolution**: All imports must be properly resolved and follow project conventions
- **Type Safety**: Strict TypeScript configuration with proper type definitions

### Build Process

- **Compilation**: Successful compilation of all TypeScript files
- **Dependency Resolution**: All npm dependencies properly installed and resolved
- **Module Bundling**: Proper module exports and imports following NestJS patterns
- **Environment Configuration**: Proper environment variable handling and validation

### Code Standards

```typescript
// Example of proper TypeScript patterns
export interface BrokerAccount {
  id: string;
  userId: string;
  brokerType: BrokerType;
  // ... other properties with proper typing
}

// Proper service implementation
@Injectable()
export class BrokerService {
  constructor(
    private readonly brokerRepository: BrokerRepository,
    private readonly encryptionService: EncryptionService,
  ) {}

  // Methods with proper return types and error handling
}
```

## Security Considerations

### Encryption Implementation

- **Algorithm**: AES-256-GCM for authenticated encryption
- **Key Management**: Separate encryption keys stored in secure key management system
- **Key Rotation**: Automated key rotation with backward compatibility
- **Salt/IV**: Unique initialization vectors for each encryption operation

### Access Control

- **Role-Based Access**: Admin-only access to broker management endpoints
- **API Authentication**: JWT tokens for API access
- **Session Management**: Secure session handling with Redis
- **Audit Logging**: Comprehensive audit trail for all operations

### OAuth2 Security

- **State Parameter**: CSRF protection for OAuth flows
- **PKCE**: Proof Key for Code Exchange for enhanced security
- **Token Storage**: Encrypted token storage with expiration handling
- **Scope Limitation**: Minimal required permissions for broker access

### Network Security

- **TLS/HTTPS**: All communications encrypted in transit
- **Certificate Validation**: Strict certificate validation for broker APIs
- **Rate Limiting**: Protection against abuse and DoS attacks
- **IP Whitelisting**: Optional IP restrictions for sensitive operations

## Performance Considerations

### Caching Strategy

- **Connection Status**: Cache broker connection status in Redis
- **Credentials**: Cache decrypted credentials with TTL
- **Health Metrics**: Cache health statistics for dashboard performance
- **Token Validation**: Cache token validation results

### Database Optimization

- **Indexing**: Optimized indexes for common query patterns
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Optimized queries for large datasets
- **Partitioning**: Table partitioning for historical data

### Scalability

- **Horizontal Scaling**: Stateless services for easy scaling
- **Load Balancing**: Distribute load across multiple instances
- **Async Processing**: Background jobs for non-critical operations
- **Circuit Breakers**: Prevent system overload during failures

## Monitoring and Observability

### Metrics Collection

- **Connection Metrics**: Success rates, response times, error rates
- **Authentication Metrics**: OAuth success rates, token refresh rates
- **System Metrics**: CPU, memory, database performance
- **Business Metrics**: Active accounts, trading volume, user activity

### Logging Strategy

- **Structured Logging**: JSON-formatted logs with consistent fields
- **Log Levels**: Appropriate log levels for different scenarios
- **Sensitive Data**: Careful handling of sensitive information in logs
- **Correlation IDs**: Request tracing across service boundaries

### Alerting

- **Connection Failures**: Alert on broker connection issues
- **Authentication Failures**: Alert on repeated auth failures
- **Performance Degradation**: Alert on response time increases
- **Security Events**: Alert on suspicious activities

## Deployment and Configuration

### Environment Configuration

```typescript
// Environment variables for broker management
export interface BrokerEnvConfig {
  // Encryption
  BROKER_ENCRYPTION_KEY: string;
  BROKER_KEY_ROTATION_INTERVAL: string;

  // Zerodha configuration
  ZERODHA_API_BASE_URL: string;
  ZERODHA_OAUTH_REDIRECT_URI: string;

  // Health monitoring
  BROKER_HEALTH_CHECK_INTERVAL: string;
  BROKER_CONNECTION_TIMEOUT: string;
  BROKER_MAX_RETRY_ATTEMPTS: string;

  // Security
  BROKER_SESSION_TIMEOUT: string;
  BROKER_RATE_LIMIT_REQUESTS: string;
  BROKER_RATE_LIMIT_WINDOW: string;
}
```

### Docker Configuration

```dockerfile
# Additional environment variables for broker service
ENV BROKER_ENCRYPTION_KEY=""
ENV ZERODHA_API_BASE_URL="https://api.kite.trade"
ENV BROKER_HEALTH_CHECK_INTERVAL="30000"
ENV BROKER_CONNECTION_TIMEOUT="10000"
```

### Database Migrations

- **Schema Versioning**: Proper migration versioning for database changes
- **Data Migration**: Safe migration of existing broker data
- **Rollback Strategy**: Ability to rollback schema changes if needed
- **Testing**: Thorough testing of migrations in staging environment
