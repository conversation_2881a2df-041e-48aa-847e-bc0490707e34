# Implementation Plan

- [x] 1. Set up User library module foundation
  - Create directory structure for `libs/user/src/` with proper module organization
  - Implement User module with NestJS dependency injection setup
  - Create barrel exports in `libs/user/src/index.ts` for clean public API
  - _Requirements: 8.1, 8.5_

- [x] 2. Implement User database schema and validation
  - [x] 2.1 Create User database schema with DrizzleORM
    - Define UserTable schema with all required fields (id, email, firstName, lastName, role, isActive, passwordHash, lastLoginAt)
    - Add proper indexes for email, role, and isActive fields
    - Include audit fields using existing base schema pattern
    - _Requirements: 8.1, 8.3_

  - [x] 2.2 Create User Zod validation schemas
    - Implement UserSchema with proper field validation using Zod v4
    - Create CreateUserSchema with password validation (8-128 chars)
    - Implement UpdateUserSchema with optional fields and strict validation
    - Export TypeScript types using z.output (not z.infer)
    - _Requirements: 7.1, 8.3_

  - [x] 2.3 Create User error classes
    - Implement UserError class extending BaseError with proper error codes
    - Define UserErrorEnum with all user-related error types
    - Create error messages mapping for consistent error responses
    - _Requirements: 7.1, 7.2, 7.3_

- [x] 3. Implement User repository layer
  - [x] 3.1 Create User repository with DrizzleORM integration
    - Implement UserRepository extending BaseRepository with proper typing
    - Add findByEmail method for authentication lookups
    - Implement password hashing utilities using bcrypt with 12 salt rounds
    - Create proper error handling for database operations
    - _Requirements: 8.1, 8.2_

- [x] 4. Implement User service layer
  - [x] 4.1 Create User service with business logic
    - Implement IUserService interface with all required methods
    - Add createUser method with password hashing and validation
    - Implement findUserById, findUserByEmail, updateUser, deleteUser methods
    - Add password validation and update methods for authentication
    - Create updateLastLogin method for session management
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 8.4_

- [x] 5. Set up Auth library module foundation
  - Create directory structure for `libs/auth/src/` with proper organization
  - Implement Auth module with NestJS dependency injection setup
  - Create barrel exports in `libs/auth/src/index.ts`
  - _Requirements: 3.1, 3.2, 5.1, 8.1_

- [x] 6. Implement Auth schemas and validation
  - [x] 6.1 Create Auth request/response schemas
    - Implement LoginRequestSchema with email and password validation
    - Create PasswordResetRequestSchema and PasswordResetSchema
    - Add ChangePasswordSchema with current/new password validation
    - Export TypeScript types using z.output pattern
    - _Requirements: 3.1, 4.1, 7.1, 8.3_

  - [x] 6.2 Create Auth error classes
    - Implement AuthError class extending BaseError
    - Define AuthErrorEnum with authentication-specific error codes
    - Create error messages for invalid credentials, session errors, password reset errors
    - _Requirements: 7.1, 7.2, 7.3_

- [x] 7. Implement password reset token management
  - [x] 7.1 Create PasswordResetToken database schema
    - Define PasswordResetTokenTable with user_id, token, expires_at, is_used fields
    - Add proper indexes and foreign key constraints
    - Include audit fields for tracking
    - _Requirements: 4.1, 4.2, 4.3, 8.2_

  - [x] 7.2 Implement PasswordResetToken repository
    - Create repository for token CRUD operations
    - Add methods for token generation, validation, and cleanup
    - Implement automatic token expiration handling
    - _Requirements: 4.1, 4.2, 4.3, 8.2_

- [x] 8. Implement Auth service layer
  - [x] 8.1 Create Auth service with authentication logic
    - Implement IAuthService interface with login, logout, session validation
    - Add login method with credential validation and session creation
    - Implement logout method with proper session cleanup
    - Create session validation method integrating with existing SessionService
    - _Requirements: 3.1, 3.2, 3.3, 6.1, 6.2, 6.3_

  - [x] 8.2 Add password reset functionality to Auth service
    - Implement requestPasswordReset with token generation
    - Add resetPassword method with token validation and password update
    - Create changePassword method for authenticated users
    - Add proper error handling for all password operations
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 9. Implement authentication guards and decorators
  - [x] 9.1 Create AuthGuard with session validation
    - Implement CanActivate interface with session-based authentication
    - Add support for @Public() decorator to bypass authentication
    - Integrate with existing SessionService for session validation
    - Add proper error handling for authentication failures
    - _Requirements: 5.1, 5.2, 5.3, 5.4_

  - [x] 9.2 Create RolesGuard for authorization
    - Implement role-based access control with admin/user roles
    - Add @Roles() decorator for endpoint-level authorization
    - Create proper error responses for authorization failures
    - _Requirements: 5.1, 5.2, 5.4_

  - [x] 9.3 Create auth decorators
    - Implement @Public() decorator for public endpoints
    - Create @Roles() decorator for role-based access control
    - Add proper TypeScript typing for decorators
    - _Requirements: 5.1, 5.2_

- [x] 10. Create User API module
  - [x] 10.1 Implement User controller
    - Create UserController with all CRUD endpoints
    - Add proper validation using Zod schemas and NestJS pipes
    - Implement admin-only access control using @Roles('admin')
    - Add pagination support for user listing
    - Create proper error handling and response formatting
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 7.4_

  - [x] 10.2 Create User API module
    - Implement User API module importing User library module
    - Configure proper dependency injection and module exports
    - Add controller registration and guard configuration
    - _Requirements: 8.1, 8.5_

- [x] 11. Create Auth API module
  - [x] 11.1 Implement Auth controller
    - Create AuthController with login, logout, password reset endpoints
    - Add @Public() decorator to login and password reset endpoints
    - Implement proper session context extraction (IP, User-Agent)
    - Add request/response validation using Zod schemas
    - Create consistent error responses for authentication failures
    - _Requirements: 3.1, 3.2, 3.3, 4.1, 4.2, 4.3, 7.4_

  - [x] 11.2 Create Auth API module
    - Implement Auth API module importing Auth library module
    - Configure session middleware integration
    - Add controller registration and proper module dependencies
    - _Requirements: 5.1, 8.1, 8.5_

- [x] 12. Create database migrations
  - [x] 12.1 Create User table migration
    - Write DrizzleORM migration for users table with all required fields
    - Add proper indexes for email, role, and isActive columns
    - Include audit fields and constraints
    - _Requirements: 8.2_

  - [x] 12.2 Create PasswordResetToken table migration
    - Write migration for password_reset_tokens table
    - Add foreign key constraint to users table
    - Create indexes for token lookup and expiration cleanup
    - _Requirements: 4.1, 8.2_

  - [x] 12.3 Create seed data migration
    - Seed data scripts should be idempotent.
    - Write seed migration for initial admin user creation
    - Use secure default password that requires change on first login
    - Add proper error handling for duplicate admin users
    - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 13. Integrate modules with main application
  - [ ] 13.1 Update main API module
    - Import User and Auth API modules in main app module
    - Configure global guards (AuthGuard, RolesGuard) with proper order
    - Add session middleware configuration
    - Update module dependencies and exports
    - _Requirements: 6.1, 6.2, 8.1_

  - [x] 13.2 Update application bootstrap
    - Configure session middleware in main.ts with Redis store
    - Add global validation pipe with Zod integration
    - Configure global exception filters for auth errors
    - _Requirements: 6.1, 6.2, 6.3, 7.4_
