# Design Document

## Overview

This design implements comprehensive User and Authentication modules for the PatternTrade API following a modular library-first architecture. The system provides secure user management with admin-controlled access, session-based authentication using Redis storage, and role-based access control (RBAC). The design follows established PatternTrade patterns including Zod v4 validation, DrizzleORM integration, and proper separation of concerns between library and API layers.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "API Layer"
        UC[User Controller]
        AC[Auth Controller]
    end

    subgraph "Library Layer"
        UM[User Module]
        AM[Auth Module]
        US[User Service]
        AS[Auth Service]
        UR[User Repository]
        AR[Auth Repository]
    end

    subgraph "Infrastructure"
        DB[(PostgreSQL)]
        REDIS[(Redis)]
        SESSION[Session Store]
    end

    UC --> UM
    AC --> AM
    UM --> US
    AM --> AS
    US --> UR
    AS --> AR
    UR --> DB
    AR --> DB
    AS --> SESSION
    SESSION --> REDIS
```

### Module Structure

The implementation follows a two-tier architecture:

1. **Library Modules** (`libs/user/`, `libs/auth/`): Core business logic, data access, and validation
2. **API Modules** (`apps/api/src/user/`, `apps/api/src/auth/`): HTTP endpoints and request handling

## Components and Interfaces

### User Library Module (`libs/user/`)

#### User Entity Schema

```typescript
// Database schema using DrizzleORM
export const UserTable = pgTable('users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  role: varchar('role', { length: 50 }).notNull().default('user'),
  isActive: boolean('is_active').notNull().default(true),
  passwordHash: text('password_hash').notNull(),
  lastLoginAt: timestamp('last_login_at'),
  ...baseSchema, // Includes audit fields
});
```

#### User Validation Schemas

```typescript
// Zod v4 schemas with exported types
export const UserSchema = z.object({
  id: z.number().int().positive(),
  email: emailSchema,
  firstName: nameSchema,
  lastName: nameSchema,
  role: z.enum(['admin', 'user']),
  isActive: z.boolean(),
  passwordHash: z.string(),
  lastLoginAt: optionalUtcDateTimeSchema,
  ...baseUpdatableEntitySchema.shape,
});

export const CreateUserSchema = z.object({
  email: emailSchema,
  firstName: nameSchema,
  lastName: nameSchema,
  role: z.enum(['admin', 'user']).default('user'),
  password: z.string().min(8).max(128),
});

export const UpdateUserSchema = z
  .object({
    firstName: nameSchema.optional(),
    lastName: nameSchema.optional(),
    role: z.enum(['admin', 'user']).optional(),
    isActive: z.boolean().optional(),
  })
  .strict();

// Exported types following PatternTrade standards
export type User = z.output<typeof UserSchema>;
export type CreateUserRequest = z.output<typeof CreateUserSchema>;
export type UpdateUserRequest = z.output<typeof UpdateUserSchema>;
```

#### User Service Interface

```typescript
export interface IUserService {
  createUser(data: CreateUserRequest): Promise<User>;
  findUserById(id: number): Promise<User | null>;
  findUserByEmail(email: string): Promise<User | null>;
  updateUser(id: number, updates: UpdateUserRequest): Promise<User>;
  deleteUser(id: number): Promise<void>;
  findAllUsers(filters?: UserFilters): Promise<User[]>;
  findUsersPaginated(options: PaginationOptions): Promise<PaginatedResult<User>>;
  validatePassword(user: User, password: string): Promise<boolean>;
  updatePassword(userId: number, newPassword: string): Promise<void>;
  updateLastLogin(userId: number): Promise<void>;
}
```

### Auth Library Module (`libs/auth/`)

#### Authentication Schemas

```typescript
export const LoginRequestSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().default(false),
});

export const PasswordResetRequestSchema = z.object({
  email: emailSchema,
});

export const PasswordResetSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  newPassword: z.string().min(8).max(128),
});

export const ChangePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8).max(128),
});

// Exported types
export type LoginRequest = z.output<typeof LoginRequestSchema>;
export type PasswordResetRequest = z.output<typeof PasswordResetRequestSchema>;
export type PasswordReset = z.output<typeof PasswordResetSchema>;
export type ChangePasswordRequest = z.output<typeof ChangePasswordSchema>;
```

#### Auth Service Interface

```typescript
export interface IAuthService {
  login(credentials: LoginRequest, sessionContext: SessionContext): Promise<AuthResult>;
  logout(sessionId: string): Promise<void>;
  validateSession(sessionId: string): Promise<SessionValidationResponse>;
  requestPasswordReset(email: string): Promise<void>;
  resetPassword(resetData: PasswordReset): Promise<void>;
  changePassword(userId: number, changeData: ChangePasswordRequest): Promise<void>;
  refreshSession(sessionId: string): Promise<SessionData>;
}
```

#### Session Management

The auth module integrates with the existing session service:

```typescript
export interface SessionContext {
  ipAddress?: string;
  userAgent?: string;
  rememberMe?: boolean;
}

export interface AuthResult {
  success: boolean;
  user: User;
  sessionId: string;
  expiresAt: string;
}
```

### Guards and Decorators

#### Authentication Guard

```typescript
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private authService: AuthService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const sessionId = request.session?.id;

    if (!sessionId) {
      throw new UnauthorizedException('No active session');
    }

    const validation = await this.authService.validateSession(sessionId);
    if (!validation.isValid || !validation.session) {
      throw new UnauthorizedException('Invalid session');
    }

    request.user = validation.session.user;
    return true;
  }
}
```

#### Role-Based Access Control Guard

```typescript
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false;
    }

    return requiredRoles.includes(user.role);
  }
}
```

#### Public Decorator

```typescript
export const IS_PUBLIC_KEY = 'isPublic';
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);
```

#### Roles Decorator

```typescript
export const ROLES_KEY = 'roles';
export const Roles = (...roles: Role[]) => SetMetadata(ROLES_KEY, roles);
```

### API Controllers

#### User Controller

```typescript
@Controller('users')
@UseGuards(AuthGuard, RolesGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @Roles('admin')
  async createUser(@Body() createUserDto: CreateUserRequest): Promise<User> {
    return this.userService.createUser(createUserDto);
  }

  @Get()
  @Roles('admin')
  async findAllUsers(@Query() query: UserSearchRequest): Promise<PaginatedResult<User>> {
    return this.userService.findUsersPaginated(query);
  }

  @Get(':id')
  @Roles('admin')
  async findUserById(@Param('id', ParseIntPipe) id: number): Promise<User> {
    const user = await this.userService.findUserById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  @Patch(':id')
  @Roles('admin')
  async updateUser(@Param('id', ParseIntPipe) id: number, @Body() updateUserDto: UpdateUserRequest): Promise<User> {
    return this.userService.updateUser(id, updateUserDto);
  }

  @Delete(':id')
  @Roles('admin')
  async deleteUser(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.userService.deleteUser(id);
  }
}
```

#### Auth Controller

```typescript
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @Public()
  async login(@Body() loginDto: LoginRequest, @Req() request: Request): Promise<AuthResult> {
    const sessionContext: SessionContext = {
      ipAddress: request.ip,
      userAgent: request.get('User-Agent'),
      rememberMe: loginDto.rememberMe,
    };

    return this.authService.login(loginDto, sessionContext);
  }

  @Post('logout')
  async logout(@Req() request: Request): Promise<void> {
    const sessionId = request.session?.id;
    if (sessionId) {
      await this.authService.logout(sessionId);
    }
  }

  @Post('password-reset/request')
  @Public()
  async requestPasswordReset(@Body() resetRequest: PasswordResetRequest): Promise<void> {
    return this.authService.requestPasswordReset(resetRequest.email);
  }

  @Post('password-reset/confirm')
  @Public()
  async resetPassword(@Body() resetData: PasswordReset): Promise<void> {
    return this.authService.resetPassword(resetData);
  }

  @Post('password/change')
  async changePassword(@Body() changeData: ChangePasswordRequest, @Req() request: Request): Promise<void> {
    const userId = request.user?.id;
    if (!userId) {
      throw new UnauthorizedException('User not authenticated');
    }
    return this.authService.changePassword(userId, changeData);
  }
}
```

## Data Models

### User Entity

- **id**: Primary key (auto-increment)
- **email**: Unique email address (varchar 255)
- **firstName**: User's first name (varchar 100)
- **lastName**: User's last name (varchar 100)
- **role**: User role enum ('admin', 'user')
- **isActive**: Account status (boolean)
- **passwordHash**: Bcrypt hashed password (text)
- **lastLoginAt**: Last login timestamp (nullable)
- **Audit fields**: createdAt, createdBy, updatedAt, updatedBy, deletedAt, deletedBy

### Password Reset Token Entity

- **id**: Primary key (auto-increment)
- **userId**: Foreign key to users table
- **token**: Unique reset token (varchar 255)
- **expiresAt**: Token expiration timestamp
- **isUsed**: Whether token has been used (boolean)
- **Audit fields**: createdAt, createdBy

### Session Data (Redis)

Leverages existing session service with user-specific data:

- **sessionId**: Unique session identifier
- **user**: User object with id, email, role
- **isAuthenticated**: Authentication status
- **expiresAt**: Session expiration
- **lastAccessedAt**: Last access timestamp
- **ipAddress**: Client IP address
- **userAgent**: Client user agent

## Error Handling

### Custom Error Classes

#### User Errors

```typescript
export const UserErrorEnum = z.enum([
  'USER_NOT_FOUND',
  'USER_ALREADY_EXISTS',
  'USER_INACTIVE',
  'INVALID_USER_DATA',
  'USER_CREATION_FAILED',
  'USER_UPDATE_FAILED',
  'USER_DELETION_FAILED',
]);

export class UserError extends BaseError<UserErrorEnumType> {
  constructor(name: UserErrorEnumType, domain: ErrorDomainType, details?: ErrorDetails) {
    super({
      name,
      domain,
      message: details?.message || UserErrorMessages[name],
      cause: details?.cause,
    });
  }
}
```

#### Auth Errors

```typescript
export const AuthErrorEnum = z.enum([
  'INVALID_CREDENTIALS',
  'USER_NOT_AUTHENTICATED',
  'SESSION_EXPIRED',
  'SESSION_INVALID',
  'PASSWORD_RESET_TOKEN_INVALID',
  'PASSWORD_RESET_TOKEN_EXPIRED',
  'PASSWORD_TOO_WEAK',
  'CURRENT_PASSWORD_INCORRECT',
]);

export class AuthError extends BaseError<AuthErrorEnumType> {
  constructor(name: AuthErrorEnumType, domain: ErrorDomainType, details?: ErrorDetails) {
    super({
      name,
      domain,
      message: details?.message || AuthErrorMessages[name],
      cause: details?.cause,
    });
  }
}
```

### Error Response Format

All errors follow the established PatternTrade error response format:

```typescript
{
  success: false,
  error: "Error message",
  timestamp: "2024-01-01T00:00:00.000Z",
  statusCode: 400
}
```

## Testing Strategy

### Unit Testing Approach

#### Service Layer Tests

- **User Service**: Test all CRUD operations, password validation, business logic
- **Auth Service**: Test authentication flows, session management, password reset
- **Repository Layer**: Test database operations with mocked DrizzleORM

#### Test Structure Example

```typescript
describe('UserService', () => {
  let service: UserService;
  let repository: jest.Mocked<UserRepository>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: UserRepository,
          useValue: createMockRepository(),
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get(UserRepository);
  });

  describe('createUser', () => {
    it('should create user with hashed password', async () => {
      // Test implementation
    });

    it('should throw error for duplicate email', async () => {
      // Test implementation
    });
  });
});
```

### Integration Testing Approach

#### Controller Tests

- **User Controller**: Test all endpoints with proper authentication/authorization
- **Auth Controller**: Test login/logout flows, password reset functionality

#### Test Database Setup

- Use test database with proper cleanup between tests
- Seed test data for consistent test scenarios
- Mock Redis session store for session-related tests

#### E2E Testing Scenarios

1. **User Management Flow**: Admin creates user → User logs in → User changes password
2. **Authentication Flow**: Login → Access protected endpoint → Logout
3. **Password Reset Flow**: Request reset → Receive token → Reset password → Login with new password
4. **Authorization Flow**: Test role-based access control for different user types

### Test Data Management

#### User Test Fixtures

```typescript
export const testUsers = {
  admin: {
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as const,
    password: 'AdminPass123!',
  },
  regularUser: {
    email: '<EMAIL>',
    firstName: 'Regular',
    lastName: 'User',
    role: 'user' as const,
    password: 'UserPass123!',
  },
};
```

#### Mock Factories

```typescript
export const createMockUser = (overrides?: Partial<User>): User => ({
  id: 1,
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'user',
  isActive: true,
  passwordHash: '$2b$10$hashedpassword',
  lastLoginAt: null,
  createdAt: '2024-01-01T00:00:00.000Z',
  createdBy: 'system',
  updatedAt: '2024-01-01T00:00:00.000Z',
  updatedBy: 'system',
  deletedAt: null,
  deletedBy: null,
  ...overrides,
});
```

## Security Considerations

### Password Security

- **Hashing**: Use bcrypt with salt rounds of 12
- **Validation**: Minimum 8 characters, maximum 128 characters
- **Reset Tokens**: Cryptographically secure random tokens with 1-hour expiration

### Session Security

- **Storage**: Redis with secure session configuration
- **Cookies**: HttpOnly, Secure (in production), SameSite=Lax
- **Expiration**: Configurable session timeout with automatic cleanup

### Input Validation

- **Zod Schemas**: Comprehensive validation for all inputs
- **Sanitization**: Email normalization, string trimming
- **Rate Limiting**: Implement rate limiting for authentication endpoints

### Authorization

- **RBAC**: Role-based access control with admin/user roles
- **Guards**: NestJS guards for authentication and authorization
- **Public Endpoints**: Explicit marking of public endpoints

## Database Migrations

### Initial User Table Migration

```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  role VARCHAR(50) NOT NULL DEFAULT 'user',
  is_active BOOLEAN NOT NULL DEFAULT true,
  password_hash TEXT NOT NULL,
  last_login_at TIMESTAMP,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  created_by TEXT NOT NULL,
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_by TEXT NOT NULL,
  deleted_at TIMESTAMP,
  deleted_by TEXT
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);
```

### Password Reset Tokens Table Migration

```sql
CREATE TABLE password_reset_tokens (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  token VARCHAR(255) NOT NULL UNIQUE,
  expires_at TIMESTAMP NOT NULL,
  is_used BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  created_by TEXT NOT NULL
);

CREATE INDEX idx_password_reset_tokens_user_id ON password_reset_tokens(user_id);
CREATE INDEX idx_password_reset_tokens_token ON password_reset_tokens(token);
CREATE INDEX idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at);
```

### Seed Data for Admin Users

```sql
INSERT INTO users (email, first_name, last_name, role, password_hash, created_by, updated_by)
VALUES
  ('<EMAIL>', 'System', 'Administrator', 'admin', '$2b$12$hashedpassword', 'system', 'system')
ON CONFLICT (email) DO NOTHING;
```

## Performance Considerations

### Database Optimization

- **Indexes**: Proper indexing on frequently queried fields (email, role, isActive)
- **Connection Pooling**: Leverage existing DrizzleORM connection pooling
- **Query Optimization**: Use efficient queries with proper WHERE clauses

### Session Management

- **Redis Optimization**: Use Redis with proper TTL settings
- **Session Cleanup**: Automated cleanup of expired sessions
- **Memory Usage**: Efficient session data structure

### Caching Strategy

- **User Data**: Cache frequently accessed user data
- **Session Validation**: Optimize session validation with Redis
- **Password Reset**: Rate limiting and token cleanup

This design provides a comprehensive, secure, and scalable foundation for user management and authentication in the PatternTrade API while following established architectural patterns and best practices.
