{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/admin": ["libs/admin/src"], "@app/admin/*": ["libs/admin/src/*"], "@app/auth": ["libs/auth/src"], "@app/auth/*": ["libs/auth/src/*"], "@app/broker": ["libs/broker"], "@app/broker/*": ["libs/broker/*"], "@app/common": ["libs/common/src"], "@app/common/*": ["libs/common/src/*"], "@app/core": ["libs/core/src"], "@app/core/*": ["libs/core/src/*"], "@app/event": ["libs/event/src"], "@app/event/*": ["libs/event/src/*"], "@app/holding": ["libs/holding/src"], "@app/holding/*": ["libs/holding/src/*"], "@app/journal": ["libs/journal/src"], "@app/journal/*": ["libs/journal/src/*"], "@app/margin": ["libs/margin/src"], "@app/margin/*": ["libs/margin/src/*"], "@app/notification": ["libs/notification/src"], "@app/notification/*": ["libs/notification/src/*"], "@app/order": ["libs/order/src"], "@app/order/*": ["libs/order/src/*"], "@app/portfolio": ["libs/portfolio/src"], "@app/portfolio/*": ["libs/portfolio/src/*"], "@app/position": ["libs/position/src"], "@app/position/*": ["libs/position/src/*"], "@app/scanner": ["libs/scanner/src"], "@app/scanner/*": ["libs/scanner/src/*"], "@app/schedular": ["libs/schedular/src"], "@app/schedular/*": ["libs/schedular/src/*"], "@app/signal": ["libs/signal/src"], "@app/signal/*": ["libs/signal/src/*"], "@app/stats": ["libs/stats/src"], "@app/stats/*": ["libs/stats/src/*"], "@app/strategy": ["libs/strategy/src"], "@app/strategy/*": ["libs/strategy/src/*"], "@app/symbol": ["libs/symbol/src"], "@app/symbol/*": ["libs/symbol/src/*"], "@app/ticker-data": ["libs/ticker-data/src"], "@app/ticker-data/*": ["libs/ticker-data/src/*"], "@app/trade": ["libs/trade/src"], "@app/trade/*": ["libs/trade/src/*"], "@app/user": ["libs/user/src"], "@app/user/*": ["libs/user/src/*"], "@app/utils": ["libs/utils/src"], "@app/utils/*": ["libs/utils/src/*"], "@app/watchlist": ["libs/watchlist/src"], "@app/watchlist/*": ["libs/watchlist/src/*"]}}, "exclude": ["env/**/*", "**/*.spec.ts", "**/*.test.ts", "**/test/**/*", "**/__tests__/**/*", ".kiro/specs/**/*", "test-*.js", "test-*.ts"]}