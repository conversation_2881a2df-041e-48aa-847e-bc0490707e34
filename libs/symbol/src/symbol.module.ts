import { Module } from '@nestjs/common';
import { SymbolService } from './symbol.service';
import { SymbolDownloadQueueService } from './symbol-download.queue';
import { SymbolDownloadWorkerService } from './symbol-download.worker';

@Module({
  providers: [
    SymbolService,
    SymbolDownloadQueueService,
    SymbolDownloadWorkerService,
  ],
  exports: [
    SymbolService,
    SymbolDownloadQueueService,
    SymbolDownloadWorkerService,
  ],
})
export class SymbolModule {}
