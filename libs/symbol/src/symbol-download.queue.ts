import { Injectable } from '@nestjs/common';
import { z } from 'zod/v4';
import { 
  BaseQueueService, 
  QueueService, 
  QueueNameEnum,
  QueuePriorityType,
  type JobResult 
} from '@app/core/queue';
import { DateTimeUtilsService } from '@app/utils';
import { EnvService } from '@app/core/env';
import { createQueueConnection, DEFAULT_QUEUE_OPTIONS } from '@app/core/queue/queue.config';

/**
 * Symbol download job data schema
 */
export const SymbolDownloadJobDataSchema = z.object({
  exchange: z.string().min(1),
  segment: z.string().min(1),
  forceRefresh: z.boolean().default(false),
  batchSize: z.number().int().min(1).max(1000).default(100),
  requestId: z.string().uuid().optional(),
});

export type SymbolDownloadJobDataType = z.output<typeof SymbolDownloadJobDataSchema>;

/**
 * Symbol download queue service
 * Manages symbol master data download jobs
 */
@Injectable()
export class SymbolDownloadQueueService extends BaseQueueService<SymbolDownloadJobDataType> {
  constructor(
    private readonly queueService: QueueService,
    private readonly envService: EnvService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    const connectionConfig = createQueueConnection(envService);
    super(
      QueueNameEnum.enum.SYMBOL_DOWNLOAD,
      connectionConfig,
      DEFAULT_QUEUE_OPTIONS,
      dateTimeUtils,
    );
  }

  /**
   * Add symbol download job for a specific exchange
   */
  async addSymbolDownloadJob(
    exchange: string,
    segment: string,
    options?: {
      forceRefresh?: boolean;
      batchSize?: number;
      priority?: QueuePriorityType;
      delay?: number;
      requestId?: string;
    }
  ): Promise<string> {
    const jobData: SymbolDownloadJobDataType = {
      exchange,
      segment,
      forceRefresh: options?.forceRefresh || false,
      batchSize: options?.batchSize || 100,
      requestId: options?.requestId,
    };

    // Validate job data
    const validatedData = SymbolDownloadJobDataSchema.parse(jobData);

    const jobName = `download-symbols-${exchange}-${segment}`;
    
    let job;
    if (options?.priority) {
      job = await this.addJobWithPriority(jobName, validatedData, options.priority, {
        delay: options.delay,
      });
    } else {
      job = await this.addJob(jobName, validatedData, {
        delay: options?.delay,
      });
    }

    return job.id || 'unknown';
  }

  /**
   * Add bulk symbol download jobs for multiple exchanges
   */
  async addBulkSymbolDownloadJobs(
    exchanges: Array<{ exchange: string; segment: string }>,
    options?: {
      forceRefresh?: boolean;
      batchSize?: number;
      priority?: QueuePriorityType;
      staggerDelayMs?: number;
    }
  ): Promise<string[]> {
    const jobIds: string[] = [];
    const staggerDelay = options?.staggerDelayMs || 1000;

    for (let i = 0; i < exchanges.length; i++) {
      const { exchange, segment } = exchanges[i];
      const delay = i * staggerDelay; // Stagger jobs to avoid overwhelming the system

      const jobId = await this.addSymbolDownloadJob(exchange, segment, {
        ...options,
        delay,
      });

      jobIds.push(jobId);
    }

    return jobIds;
  }

  /**
   * Schedule daily symbol download for all exchanges
   */
  async scheduleDaily(): Promise<string> {
    const job = await this.addRepeatingJob(
      'daily-symbol-download',
      {
        exchange: 'ALL',
        segment: 'ALL',
        forceRefresh: true,
        batchSize: 500,
      },
      {
        pattern: '0 6 * * *', // Every day at 6 AM
        tz: 'Asia/Kolkata',
      },
      {
        priority: 2, // High priority
      }
    );

    return job.id || 'unknown';
  }

  /**
   * Get symbol download job status
   */
  async getJobStatus(jobId: string): Promise<{
    id: string;
    name?: string;
    data?: SymbolDownloadJobDataType;
    progress: number;
    state: string;
    attempts: number;
    timestamp: string;
  } | null> {
    const job = await this.getJob(jobId);
    if (!job) {
      return null;
    }

    return {
      id: job.id || 'unknown',
      name: job.name,
      data: job.data,
      progress: job.progress,
      state: await job.getState(),
      attempts: job.attemptsMade,
      timestamp: this.dateTimeUtils.getCurrentUtcDateTime(),
    };
  }

  /**
   * Cancel symbol download job
   */
  async cancelJob(jobId: string): Promise<void> {
    await this.removeJob(jobId);
  }

  /**
   * Get pending symbol download jobs count
   */
  async getPendingJobsCount(): Promise<number> {
    const healthStatus = await this.getHealthStatus();
    return healthStatus.waiting + healthStatus.delayed;
  }

  /**
   * Get active symbol download jobs count
   */
  async getActiveJobsCount(): Promise<number> {
    const healthStatus = await this.getHealthStatus();
    return healthStatus.active;
  }

  /**
   * Clean old completed and failed jobs
   */
  async cleanOldJobs(olderThanHours: number = 24): Promise<void> {
    const olderThanMs = olderThanHours * 60 * 60 * 1000;
    await this.cleanQueue(olderThanMs);
  }
}
