import { CanActivate, ExecutionContext, Injectable, Logger, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { ROLES_KEY, Role } from '../decorators/roles.decorator';

// Define extended request interface
interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    email: string;
    role: 'admin' | 'user';
  };
}

/**
 * Role-based authorization guard that checks user roles for protected endpoints
 * Requirements: 5.1, 5.2, 5.4 - Role-based access control with admin/user roles
 */
@Injectable()
export class RolesGuard implements CanActivate {
  private readonly logger = new Logger(RolesGuard.name);

  constructor(private reflector: Reflector) {}

  /**
   * Determines if the current user has the required role(s) to access the endpoint
   *
   * @param context - Execution context containing request information
   * @returns boolean - True if user has required role, false otherwise
   * @throws ForbiddenException - When user lacks required role
   */
  canActivate(context: ExecutionContext): boolean {
    try {
      // Get required roles from decorator metadata
      const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);

      // If no roles are specified, allow access
      if (!requiredRoles || requiredRoles.length === 0) {
        this.logger.debug('No role requirements specified, allowing access');
        return true;
      }

      const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
      const user = request.user;

      // User should be attached by AuthGuard
      if (!user) {
        this.logger.warn('No user found in request - AuthGuard should run before RolesGuard');
        throw new ForbiddenException('User authentication required');
      }

      // Check if user has any of the required roles
      const hasRequiredRole = requiredRoles.includes(user.role);

      if (!hasRequiredRole) {
        this.logger.warn(
          `User ${user.email} with role '${user.role}' attempted to access endpoint requiring roles: [${requiredRoles.join(', ')}]`,
        );
        throw new ForbiddenException(
          `Access denied. Required role(s): ${requiredRoles.join(', ')}. Your role: ${user.role}`,
        );
      }

      this.logger.debug(
        `Authorization successful for user ${user.email} with role '${user.role}' accessing endpoint requiring: [${requiredRoles.join(', ')}]`,
      );

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error('Authorization error:', error);
      throw new ForbiddenException('Authorization failed');
    }
  }
}
