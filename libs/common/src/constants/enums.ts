import { z } from 'zod/v4';

// ==================== SYSTEM ENUMS ====================

/**
 * Environment enum for application deployment contexts
 */
export const EnvironmentEnum = z
  .enum(['local', 'docker', 'development', 'staging', 'test', 'preproduction', 'development_senthil', 'production'])
  .describe('Application deployment environments');

/**
 * Log level enum for application logging
 */
export const LogLevelEnum = z
  .enum(['trace', 'debug', 'info', 'warn', 'error', 'fatal'])
  .describe('Logging severity levels');

/**
 * API response status enum
 */
export const ApiStatusEnum = z.enum(['success', 'error', 'pending']).describe('API response status indicators');

/**
 * Sort order enum for pagination and listing
 */
export const SortOrderEnum = z.enum(['asc', 'desc']).describe('Sort direction for queries');

/**
 * Day of week enum for scheduling and business hours
 */
export const DayOfWeekEnum = z
  .enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
  .describe('Days of the week for scheduling');

// ==================== FINANCIAL MARKET ENUMS ====================

/**
 * Market segment enum for Indian financial markets
 */
export const MarketSegmentEnum = z
  .enum(['EQUITY', 'COMMODITY', 'CURRENCY'])
  .describe('Supported market segments in Indian financial markets');

/**
 * Exchange enum for supported Indian exchanges
 */
export const ExchangeEnum = z
  .enum([
    'NSE', // National Stock Exchange (Equity)
    'BSE', // Bombay Stock Exchange (Equity)
    'NFO', // NSE Futures & Options
    'BFO', // BSE Futures & Options
    'MCX', // Multi Commodity Exchange
    'NCDEX', // National Commodity & Derivatives Exchange
    'NSE_CUR', // NSE Currency
    'BSE_CUR', // BSE Currency
  ])
  .describe('Supported Indian stock exchanges');

/**
 * Instrument type enum for financial instruments
 */
export const InstrumentTypeEnum = z
  .enum([
    'EQ', // Equity
    'FUT', // Futures
    'OPT', // Options
    'CE', // Call Option
    'PE', // Put Option
  ])
  .describe('Financial instrument types');

// ==================== TRADING ENUMS ====================

/**
 * Order type enum for trade execution
 */
export const OrderTypeEnum = z
  .enum([
    'MARKET', // Market Order
    'LIMIT', // Limit Order
    'SL', // Stop Loss
    'SL_M', // Stop Loss Market
  ])
  .describe('Order execution types');

/**
 * Order side enum (Buy/Sell)
 */
export const OrderSideEnum = z.enum(['BUY', 'SELL']).describe('Order side - buy or sell');

/**
 * Order status enum for tracking order lifecycle
 */
export const OrderStatusEnum = z
  .enum(['PENDING', 'OPEN', 'COMPLETE', 'CANCELLED', 'REJECTED', 'TRIGGERED'])
  .describe('Order execution status');

/**
 * Product type enum for order placement
 */
export const ProductTypeEnum = z
  .enum([
    'CNC', // Cash and Carry
    'MIS', // Margin Intraday Square-off
    'NRML', // Normal (Carry Forward)
  ])
  .describe('Product types for order placement');

/**
 * Order validity enum
 */
export const OrderValidityEnum = z
  .enum([
    'DAY', // Valid for the day
    'IOC', // Immediate or Cancel
    'GTT', // Good Till Triggered
  ])
  .describe('Order validity types');

// ==================== BROKER ENUMS ====================

/**
 * Supported broker types
 */
export const BrokerTypeEnum = z
  .enum(['ZERODHA', 'ALICE_BLUE', 'FINVASIA', 'UPSTOX', 'DHAN', 'GROW', 'ANGEL_ONE'])
  .describe('Supported broker platforms');

/**
 * Broker connection status
 */
export const BrokerConnectionStatusEnum = z
  .enum(['CONNECTED', 'DISCONNECTED', 'CONNECTING', 'RECONNECTING', 'ERROR', 'EXPIRED'])
  .describe('Broker API connection status');

/**
 * Broker credentials status
 */
export const BrokerCredentialStatusEnum = z
  .enum(['VALID', 'INVALID', 'EXPIRED', 'PENDING_VERIFICATION'])
  .describe('Broker credential validation status');

// ==================== USER & ACCOUNT ENUMS ====================

/**
 * User role enum for access control
 */
export const UserRoleEnum = z.enum(['ADMIN', 'TRADER', 'ANALYST', 'VIEWER']).describe('User roles and permissions');

/**
 * User status enum
 */
export const UserStatusEnum = z
  .enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION'])
  .describe('User account status');

/**
 * Account type enum
 */
export const AccountTypeEnum = z
  .enum([
    'INDIVIDUAL',
    'CORPORATE',
    'PARTNERSHIP',
    'HUF', // Hindu Undivided Family
  ])
  .describe('Trading account types');

// ==================== DATA ENUMS ====================

/**
 * Time interval enum for historical data
 */
export const TimeIntervalEnum = z
  .enum(['1min', '5min', '15min', '30min', '1hour', '1day', '1week', '1month'])
  .describe('Time intervals for historical data');

/**
 * Data source enum
 */
export const DataSourceEnum = z.enum(['LIVE', 'DELAYED', 'HISTORICAL', 'SIMULATED']).describe('Data source types');

/**
 * Market data subscription mode enum
 */
export const SubscriptionModeEnum = z
  .enum(['LTP', 'QUOTE', 'FULL'])
  .describe(
    'Market data subscription modes - LTP (Last Traded Price), QUOTE (LTP + Market depth), FULL (Complete market data)',
  );

// ==================== BROKER-SPECIFIC TRANSFORMATION ENUMS ====================

/**
 * Kite instrument type transformation enum
 * Transforms Kite API instrument types to PatternTrade format
 */
export const KiteInstrumentTypeTransformEnum = z
  .enum(['EQ', 'FUT', 'CE', 'PE', 'INDEX'])
  .transform((kiteType) => {
    const mapping = {
      EQ: 'EQUITY',
      FUT: 'FUTURES',
      CE: 'CALL_OPTION',
      PE: 'PUT_OPTION',
      INDEX: 'INDEX',
    } as const;
    return mapping[kiteType] || kiteType;
  })
  .describe('Transforms Kite API instrument types to PatternTrade format');

/**
 * Subscription mode to Kite transformation enum
 * Transforms PatternTrade subscription modes to Kite API format
 */
export const SubscriptionModeToKiteTransformEnum = z
  .enum(['LTP', 'QUOTE', 'FULL'])
  .transform((ptMode) => {
    const mapping = {
      LTP: 'ltp',
      QUOTE: 'quote',
      FULL: 'full',
    } as const;
    return mapping[ptMode] || 'quote';
  })
  .describe('Transforms PatternTrade subscription modes to Kite API format');

/**
 * Price type enum for technical analysis
 */
export const PriceTypeEnum = z
  .enum(['OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOLUME', 'OHLC'])
  .describe('Price data types for analysis');

// ==================== STRATEGY & ANALYSIS ENUMS ====================

/**
 * Strategy status enum
 */
export const StrategyStatusEnum = z
  .enum(['DRAFT', 'ACTIVE', 'INACTIVE', 'PAUSED', 'STOPPED', 'BACKTESTING', 'ERROR'])
  .describe('Trading strategy execution status');

/**
 * Signal type enum for trading signals
 */
export const SignalTypeEnum = z
  .enum(['BUY', 'SELL', 'HOLD', 'STRONG_BUY', 'STRONG_SELL'])
  .describe('Trading signal recommendations');

/**
 * Risk level enum
 */
export const RiskLevelEnum = z.enum(['LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH']).describe('Risk assessment levels');

// ==================== NOTIFICATION ENUMS ====================

/**
 * Notification type enum
 */
export const NotificationTypeEnum = z
  .enum(['ORDER_EXECUTED', 'ORDER_CANCELLED', 'PRICE_ALERT', 'SYSTEM_ALERT', 'STRATEGY_SIGNAL', 'MARKET_NEWS'])
  .describe('Notification categories');

/**
 * Notification priority enum
 */
export const NotificationPriorityEnum = z
  .enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT'])
  .describe('Notification priority levels');

/**
 * Notification status enum
 */
export const NotificationStatusEnum = z
  .enum(['PENDING', 'SENT', 'DELIVERED', 'READ', 'FAILED'])
  .describe('Notification delivery status');

// ==================== SYSTEM STATUS ENUMS ====================

/**
 * General entity status enum
 */
export const EntityStatusEnum = z
  .enum(['ACTIVE', 'INACTIVE', 'ARCHIVED', 'DELETED'])
  .describe('Generic entity status for soft deletion');

/**
 * Process status enum
 */
export const ProcessStatusEnum = z
  .enum(['PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED'])
  .describe('Background process status');

/**
 * Health status enum for system monitoring
 */
export const HealthStatusEnum = z
  .enum(['HEALTHY', 'DEGRADED', 'UNHEALTHY', 'UNKNOWN'])
  .describe('System component health status');

// ==================== CONFIGURATION ENUMS ====================

/**
 * Application type enum for configuration scoping
 */
export const ApplicationEnum = z
  .enum(['global', 'api', 'oms', 'ticker', 'analyser', 'simulator'])
  .describe('Application types for configuration scoping');

/**
 * Configuration action enum for audit tracking
 */
export const ConfigurationActionEnum = z
  .enum(['CREATE', 'UPDATE', 'DELETE', 'ACTIVATE', 'DEACTIVATE'])
  .describe('Configuration management actions for audit trail');

/**
 * Configuration category enum for dynamic configuration
 */
export const ConfigurationCategoryEnum = z
  .enum(['features', 'orderLimits', 'timeouts', 'rateLimits', 'marketData', 'services', 'application'])
  .describe('Configuration categories for dynamic application settings');

// ==================== TYPE EXPORTS ====================
// Following PatternTrade API standards: Export TypeScript types from Zod schemas using z.output

export type Environment = z.output<typeof EnvironmentEnum>;
export type LogLevel = z.output<typeof LogLevelEnum>;
export type ApiStatus = z.output<typeof ApiStatusEnum>;
export type SortOrder = z.output<typeof SortOrderEnum>;
export type DayOfWeek = z.output<typeof DayOfWeekEnum>;

export type MarketSegment = z.output<typeof MarketSegmentEnum>;
export type Exchange = z.output<typeof ExchangeEnum>;
export type InstrumentType = z.output<typeof InstrumentTypeEnum>;

export type OrderType = z.output<typeof OrderTypeEnum>;
export type OrderSide = z.output<typeof OrderSideEnum>;
export type OrderStatus = z.output<typeof OrderStatusEnum>;
export type ProductType = z.output<typeof ProductTypeEnum>;
export type OrderValidity = z.output<typeof OrderValidityEnum>;

export type BrokerType = z.output<typeof BrokerTypeEnum>;
export type BrokerConnectionStatus = z.output<typeof BrokerConnectionStatusEnum>;
export type BrokerCredentialStatus = z.output<typeof BrokerCredentialStatusEnum>;

export type UserRole = z.output<typeof UserRoleEnum>;
export type UserStatus = z.output<typeof UserStatusEnum>;
export type AccountType = z.output<typeof AccountTypeEnum>;

export type TimeInterval = z.output<typeof TimeIntervalEnum>;
export type DataSource = z.output<typeof DataSourceEnum>;
export type SubscriptionMode = z.output<typeof SubscriptionModeEnum>;
export type PriceType = z.output<typeof PriceTypeEnum>;

export type StrategyStatus = z.output<typeof StrategyStatusEnum>;
export type SignalType = z.output<typeof SignalTypeEnum>;
export type RiskLevel = z.output<typeof RiskLevelEnum>;

export type NotificationType = z.output<typeof NotificationTypeEnum>;
export type NotificationPriority = z.output<typeof NotificationPriorityEnum>;
export type NotificationStatus = z.output<typeof NotificationStatusEnum>;

export type EntityStatus = z.output<typeof EntityStatusEnum>;
export type ProcessStatus = z.output<typeof ProcessStatusEnum>;
export type HealthStatus = z.output<typeof HealthStatusEnum>;

export type Application = z.output<typeof ApplicationEnum>;
export type ConfigurationAction = z.output<typeof ConfigurationActionEnum>;
export type ConfigurationCategory = z.output<typeof ConfigurationCategoryEnum>;

// ==================== UTILITY FUNCTIONS ====================

/**
 * Get all values from a Zod enum as an array
 * Uses Zod 4's improved type inference
 */
export const getEnumValues = <T extends z.ZodEnum<Record<string, string>>>(enumSchema: T): Array<z.output<T>> => {
  return enumSchema.options as Array<z.output<T>>;
};

/**
 * Check if a value is valid for a given enum
 * Uses Zod 4's improved type inference
 */
export const isValidEnumValue = <T extends z.ZodEnum<Record<string, string>>>(
  enumSchema: T,
  value: unknown,
): value is z.output<T> => {
  return enumSchema.safeParse(value).success;
};

/**
 * Get enum description
 * Compatible with Zod 4's improved metadata handling
 */
export const getEnumDescription = <T extends z.ZodEnum<Record<string, string>>>(enumSchema: T): string | undefined => {
  return (enumSchema as { description?: string }).description;
};

/**
 * Safely parse enum value with fallback
 * Uses Zod 4's improved type inference
 */
export const parseEnumValue = <T extends z.ZodEnum<Record<string, string>>>(
  enumSchema: T,
  value: unknown,
  fallback: z.output<T>,
): z.output<T> => {
  const result = enumSchema.safeParse(value);
  return result.success ? result.data : fallback;
};

/**
 * Get enum key-value pairs as an object
 * Uses Zod 4's improved type inference
 */
export const getEnumKeyValuePairs = <T extends z.ZodEnum<Record<string, string>>>(
  enumSchema: T,
): Record<z.output<T>, z.output<T>> => {
  const values = enumSchema.options as Array<z.output<T>>;
  return values.reduce(
    (acc, value) => {
      acc[value] = value;
      return acc;
    },
    {} as Record<z.output<T>, z.output<T>>,
  );
};

/**
 * Create a validation function for a specific enum
 * Uses Zod 4's improved type inference
 */
export const createEnumValidator = <T extends z.ZodEnum<Record<string, string>>>(enumSchema: T) => {
  return (value: unknown): value is z.output<T> => isValidEnumValue(enumSchema, value);
};

/**
 * Transform enum values using a mapping function
 * Uses Zod 4's improved type inference
 */
export const transformEnumValues = <T extends z.ZodEnum<Record<string, string>>, R>(
  enumSchema: T,
  transformer: (value: z.output<T>) => R,
): R[] => {
  return enumSchema.options.map((value: string) => transformer(value as z.output<T>));
};

/**
 * Get random enum value (useful for testing)
 * Uses Zod 4's improved type inference
 */
export const getRandomEnumValue = <T extends z.ZodEnum<Record<string, string>>>(enumSchema: T): z.output<T> => {
  const values = enumSchema.options;
  return values[Math.floor(Math.random() * values.length)] as z.output<T>;
};

// ==================== ENUM COLLECTIONS ====================

/**
 * Market-related enums collection
 */
export const MarketEnums = {
  MarketSegment: MarketSegmentEnum,
  Exchange: ExchangeEnum,
  InstrumentType: InstrumentTypeEnum,
  OrderType: OrderTypeEnum,
  OrderSide: OrderSideEnum,
  OrderStatus: OrderStatusEnum,
  ProductType: ProductTypeEnum,
  OrderValidity: OrderValidityEnum,
} as const;

/**
 * Broker-related enums collection
 */
export const BrokerEnums = {
  BrokerType: BrokerTypeEnum,
  ConnectionStatus: BrokerConnectionStatusEnum,
  CredentialStatus: BrokerCredentialStatusEnum,
  SubscriptionMode: SubscriptionModeEnum,
  KiteInstrumentTypeTransform: KiteInstrumentTypeTransformEnum,
  SubscriptionModeToKiteTransform: SubscriptionModeToKiteTransformEnum,
} as const;

/**
 * User-related enums collection
 */
export const UserEnums = {
  Role: UserRoleEnum,
  Status: UserStatusEnum,
  AccountType: AccountTypeEnum,
} as const;

/**
 * System-related enums collection
 */
export const SystemEnums = {
  Environment: EnvironmentEnum,
  LogLevel: LogLevelEnum,
  ApiStatus: ApiStatusEnum,
  EntityStatus: EntityStatusEnum,
  ProcessStatus: ProcessStatusEnum,
  HealthStatus: HealthStatusEnum,
} as const;
