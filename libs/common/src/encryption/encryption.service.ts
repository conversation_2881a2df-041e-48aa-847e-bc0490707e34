import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { createCipheriv, createDecipheriv, randomBytes, createHash, timingSafeEqual } from 'crypto';
import { EnvService } from '@app/core/env';
import { EncryptionError, EncryptionErrorEnum } from './encryption.error';
import { IEncryptionService, EncryptedObject } from './encryption.interface';

/**
 * Encryption service for secure credential storage
 * Implements AES-256-GCM encryption with proper key management
 *
 * Requirements:
 * - 4.1: AES-256 encryption with proper key management
 * - 4.2: Industry-standard key rotation and storage practices
 * - 4.3: Proper access controls and audit logging
 */
@Injectable()
export class EncryptionService implements IEncryptionService, OnModuleInit {
  private readonly logger = new Logger(EncryptionService.name);

  // Encryption configuration
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly IV_LENGTH = 16; // 128 bits
  private static readonly AUTH_TAG_LENGTH = 16; // 128 bits
  private static readonly KEY_LENGTH = 32; // 256 bits
  private static readonly SALT_LENGTH = 32; // 256 bits

  private encryptionKey!: Buffer;
  private keyVersion: string = '1';

  constructor(private readonly envService: EnvService) {}

  onModuleInit() {
    this.logger.log('Initializing encryption service...');

    try {
      this.initializeEncryptionKey();
      this.logger.log('Encryption service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize encryption service', error);
      throw new EncryptionError('ENCRYPTION_SERVICE_INITIALIZATION_FAILED', 'ENCRYPTION', {
        message: 'Encryption service initialization failed',
        cause: error,
        operation: 'INITIALIZE',
      });
    }
  }

  /**
   * Encrypt a string using AES-256-GCM
   * @param data - Plain text data to encrypt
   * @returns Base64 encoded encrypted data with metadata
   */
  encrypt(data: string): string {
    try {
      if (!data) {
        throw new Error('Data to encrypt cannot be empty');
      }

      // Generate random IV for each encryption
      const iv = randomBytes(EncryptionService.IV_LENGTH);

      // Create cipher
      const cipher = createCipheriv(EncryptionService.ALGORITHM, this.encryptionKey, iv);
      cipher.setAAD(Buffer.from(this.keyVersion)); // Additional authenticated data

      // Encrypt data
      let encrypted = cipher.update(data, 'utf8', 'base64');
      encrypted += cipher.final('base64');

      // Get authentication tag
      const authTag = cipher.getAuthTag();

      // Create encrypted object with metadata
      const encryptedObj: EncryptedObject = {
        data: encrypted,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        algorithm: EncryptionService.ALGORITHM,
        keyVersion: this.keyVersion,
      };

      // Return as base64 encoded JSON
      return Buffer.from(JSON.stringify(encryptedObj)).toString('base64');
    } catch (error) {
      this.logger.error('Encryption failed:', error);
      throw new EncryptionError('ENCRYPTION_FAILED', 'ENCRYPTION', {
        message: 'Failed to encrypt data',
        cause: error,
        operation: 'ENCRYPT',
      });
    }
  }

  /**
   * Decrypt a string using AES-256-GCM
   * @param encryptedData - Base64 encoded encrypted data with metadata
   * @returns Decrypted plain text data
   */
  decrypt(encryptedData: string): string {
    try {
      if (!encryptedData) {
        throw new Error('Encrypted data cannot be empty');
      }

      // Parse encrypted object from base64 JSON
      const encryptedObj: EncryptedObject = JSON.parse(Buffer.from(encryptedData, 'base64').toString('utf8'));

      // Validate encrypted object structure
      this.validateEncryptedObject(encryptedObj);

      // Convert base64 strings back to buffers
      const iv = Buffer.from(encryptedObj.iv, 'base64');
      const authTag = Buffer.from(encryptedObj.authTag, 'base64');

      // Create decipher
      const decipher = createDecipheriv(encryptedObj.algorithm, this.encryptionKey, iv) as any; // eslint-disable-line @typescript-eslint/no-explicit-any
      decipher.setAuthTag(authTag);
      decipher.setAAD(Buffer.from(encryptedObj.keyVersion));

      // Decrypt data
      let decrypted = decipher.update(encryptedObj.data, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Decryption failed:', error);
      throw new EncryptionError('DECRYPTION_FAILED', 'ENCRYPTION', {
        message: 'Failed to decrypt data',
        cause: error,
        operation: 'DECRYPT',
      });
    }
  }

  /**
   * Encrypt an object by converting it to JSON first
   * @param obj - Object to encrypt
   * @returns Encrypted object with metadata
   */
  encryptObject<T>(obj: T): EncryptedObject {
    try {
      const jsonString = JSON.stringify(obj);
      const encryptedString = this.encrypt(jsonString);

      // Parse the encrypted string back to object for type safety
      return JSON.parse(Buffer.from(encryptedString, 'base64').toString('utf8'));
    } catch (error) {
      this.logger.error('Object encryption failed:', error);
      throw new EncryptionError('ENCRYPTION_FAILED', 'ENCRYPTION', {
        message: 'Failed to encrypt object',
        cause: error,
        operation: 'ENCRYPT_OBJECT',
      });
    }
  }

  /**
   * Decrypt an object by decrypting and parsing JSON
   * @param encryptedObj - Encrypted object with metadata
   * @returns Decrypted object
   */
  decryptObject<T>(encryptedObj: EncryptedObject): T {
    try {
      // Convert encrypted object back to base64 string
      const encryptedString = Buffer.from(JSON.stringify(encryptedObj)).toString('base64');
      const decryptedString = this.decrypt(encryptedString);

      return JSON.parse(decryptedString) as T;
    } catch (error) {
      this.logger.error('Object decryption failed:', error);
      throw new EncryptionError('DECRYPTION_FAILED', 'ENCRYPTION', {
        message: 'Failed to decrypt object',
        cause: error,
        operation: 'DECRYPT_OBJECT',
      });
    }
  }

  /**
   * Validate encryption by encrypting and decrypting test data
   * @param data - Test data to validate
   * @param encrypted - Encrypted version to validate against
   * @returns True if validation passes
   */
  validateEncryption(data: string, encrypted: string): boolean {
    try {
      const decrypted = this.decrypt(encrypted);
      return timingSafeEqual(Buffer.from(data), Buffer.from(decrypted));
    } catch (error) {
      this.logger.warn('Encryption validation failed:', error);
      return false;
    }
  }

  /**
   * Rotate encryption keys (placeholder for future implementation)
   * @returns void
   */
  rotateKeys(): void {
    this.logger.warn('Key rotation not yet implemented');
    throw new EncryptionError('KEY_ROTATION_FAILED', 'ENCRYPTION', {
      message: 'Key rotation feature not yet implemented',
      operation: 'ROTATE_KEYS',
    });
  }

  // ==================== PRIVATE METHODS ====================

  /**
   * Initialize encryption key from environment variables
   * @private
   */
  private initializeEncryptionKey(): void {
    try {
      // Get encryption key from environment
      const keyString = this.envService.get('BROKER_ENCRYPTION_KEY');

      if (!keyString) {
        throw new Error('BROKER_ENCRYPTION_KEY environment variable is required');
      }

      // Derive key using PBKDF2 for additional security
      const salt = this.envService.get('BROKER_ENCRYPTION_SALT') || 'patterntrade-broker-salt';
      this.encryptionKey = createHash('sha256')
        .update(keyString + salt)
        .digest();

      // Validate key length
      if (this.encryptionKey.length !== EncryptionService.KEY_LENGTH) {
        throw new Error(
          `Invalid key length: expected ${EncryptionService.KEY_LENGTH}, got ${this.encryptionKey.length}`,
        );
      }

      this.logger.log('Encryption key initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize encryption key:', error);
      throw error;
    }
  }

  /**
   * Validate encrypted object structure
   * @param obj - Object to validate
   * @private
   */
  private validateEncryptedObject(obj: EncryptedObject): void {
    const requiredFields = ['data', 'iv', 'authTag', 'algorithm', 'keyVersion'];

    for (const field of requiredFields) {
      if (!(field in obj) || typeof obj[field as keyof EncryptedObject] !== 'string') {
        throw new Error(`Invalid encrypted object: missing or invalid field '${field}'`);
      }
    }

    if (obj.algorithm !== EncryptionService.ALGORITHM) {
      throw new Error(`Unsupported encryption algorithm: ${obj.algorithm}`);
    }
  }
}
