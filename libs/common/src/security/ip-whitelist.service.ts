import { Injectable, Logger } from '@nestjs/common';
import { EnvService } from '@app/core/env';
import { CacheService } from '@app/core/cache';
import { isIP, isIPv4, isIPv6 } from 'net';

export interface IPWhitelistConfig {
  enabled: boolean;
  allowedIPs: string[];
  allowedCIDRs: string[];
  allowPrivateIPs: boolean;
  allowLoopback: boolean;
  cacheEnabled: boolean;
  cacheTTL: number; // seconds
}

export interface IPValidationResult {
  allowed: boolean;
  reason?: string;
  matchedRule?: string;
  ipType: 'IPv4' | 'IPv6' | 'Invalid';
}

/**
 * Application-wide IP whitelisting service for sensitive operations
 *
 * Requirements:
 * - 4.6: IP whitelisting for sensitive operations
 * - 6.6: Audit trails for security and compliance
 */
@Injectable()
export class IPWhitelistService {
  private readonly logger = new Logger(IPWhitelistService.name);
  private readonly defaultConfig: IPWhitelistConfig;
  private readonly sensitiveOperations = new Set([
    // Authentication operations
    'LOGIN',
    'REGISTER',
    'FORGOT_PASSWORD',
    'RESET_PASSWORD',

    // Broker operations
    'CREATE_BROKER_ACCOUNT',
    'DELETE_BROKER_ACCOUNT',
    'UPDATE_BROKER_CREDENTIALS',
    'OAUTH_INITIATE',
    'OAUTH_CALLBACK',
    'REFRESH_TOKEN',
    'REVOKE_TOKEN',

    // Admin operations
    'CREATE_USER',
    'DELETE_USER',
    'UPDATE_USER_ROLE',
    'SYSTEM_CONFIG_UPDATE',
  ]);

  constructor(
    private readonly envService: EnvService,
    private readonly cacheService: CacheService,
  ) {
    this.defaultConfig = this.loadIPWhitelistConfig();
  }

  /**
   * Validate IP address against whitelist for sensitive operations
   */
  async validateIPAccess(
    ipAddress: string,
    operation: string,
    userId?: string,
    correlationId?: string,
  ): Promise<IPValidationResult> {
    // Skip validation if IP whitelisting is disabled
    if (!this.defaultConfig.enabled) {
      return {
        allowed: true,
        reason: 'IP whitelisting disabled',
        ipType: this.getIPType(ipAddress),
      };
    }

    // Skip validation for non-sensitive operations
    if (!this.isSensitiveOperation(operation)) {
      return {
        allowed: true,
        reason: 'Non-sensitive operation',
        ipType: this.getIPType(ipAddress),
      };
    }

    const normalizedIP = this.normalizeIP(ipAddress);
    const ipType = this.getIPType(normalizedIP);

    // Check cache first if enabled
    if (this.defaultConfig.cacheEnabled) {
      const cachedResult = await this.getCachedValidation(normalizedIP, operation);
      if (cachedResult) {
        return { ...cachedResult, ipType };
      }
    }

    // Validate IP format
    if (ipType === 'Invalid') {
      const result: IPValidationResult = {
        allowed: false,
        reason: 'Invalid IP address format',
        ipType,
      };

      await this.logIPViolation(normalizedIP, operation, result.reason!, userId, correlationId);
      return result;
    }

    // Check against whitelist rules
    const validationResult = await this.checkIPWhitelist(normalizedIP, operation);

    // Cache the result if enabled
    if (this.defaultConfig.cacheEnabled) {
      await this.cacheValidationResult(normalizedIP, operation, validationResult);
    }

    // Log security events
    if (!validationResult.allowed) {
      await this.logIPViolation(normalizedIP, operation, validationResult.reason!, userId, correlationId);
    } else {
      await this.logIPAccess(normalizedIP, operation, validationResult.matchedRule!, userId, correlationId);
    }

    return { ...validationResult, ipType };
  }

  /**
   * Enforce IP whitelist and throw error if access denied
   */
  async enforceIPWhitelist(
    ipAddress: string,
    operation: string,
    userId?: string,
    correlationId?: string,
  ): Promise<void> {
    const result = await this.validateIPAccess(ipAddress, operation, userId, correlationId);

    if (!result.allowed) {
      throw new Error(`Access denied from IP address ${ipAddress}: ${result.reason}`);
    }
  }

  /**
   * Add IP address to whitelist (admin function)
   */
  async addToWhitelist(ipAddress: string, adminUserId: string, reason?: string): Promise<void> {
    const normalizedIP = this.normalizeIP(ipAddress);
    const ipType = this.getIPType(normalizedIP);

    if (ipType === 'Invalid') {
      throw new Error('Invalid IP address format');
    }

    try {
      // Add to runtime whitelist (this would typically be persisted to database)
      this.defaultConfig.allowedIPs.push(normalizedIP);

      // Clear cache to force re-validation
      if (this.defaultConfig.cacheEnabled) {
        await this.clearIPCache(normalizedIP);
      }

      // Log admin action
      this.logger.log(
        `IP ${normalizedIP} added to whitelist by admin ${adminUserId}. Reason: ${reason || 'Not specified'}`,
      );
    } catch (error) {
      this.logger.error(`Failed to add IP ${normalizedIP} to whitelist:`, error);
      throw new Error('Failed to add IP to whitelist');
    }
  }

  /**
   * Remove IP address from whitelist (admin function)
   */
  async removeFromWhitelist(ipAddress: string, adminUserId: string, reason?: string): Promise<void> {
    const normalizedIP = this.normalizeIP(ipAddress);

    try {
      // Remove from runtime whitelist
      const index = this.defaultConfig.allowedIPs.indexOf(normalizedIP);
      if (index > -1) {
        this.defaultConfig.allowedIPs.splice(index, 1);
      }

      // Clear cache
      if (this.defaultConfig.cacheEnabled) {
        await this.clearIPCache(normalizedIP);
      }

      // Log admin action
      this.logger.log(
        `IP ${normalizedIP} removed from whitelist by admin ${adminUserId}. Reason: ${reason || 'Not specified'}`,
      );
    } catch (error) {
      this.logger.error(`Failed to remove IP ${normalizedIP} from whitelist:`, error);
      throw new Error('Failed to remove IP from whitelist');
    }
  }

  /**
   * Get current whitelist configuration
   */
  getWhitelistConfig(): IPWhitelistConfig {
    return { ...this.defaultConfig };
  }

  /**
   * Check if operation requires IP whitelisting
   */
  isSensitiveOperation(operation: string): boolean {
    return this.sensitiveOperations.has(operation);
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Load IP whitelist configuration from environment
   */
  private loadIPWhitelistConfig(): IPWhitelistConfig {
    const enabled = this.envService.getArbitrary('IP_WHITELIST_ENABLED', 'false') === 'true';
    const allowedIPs = this.parseIPList(this.envService.getArbitrary('ALLOWED_IPS', '') || '');
    const allowedCIDRs = this.parseIPList(this.envService.getArbitrary('ALLOWED_CIDRS', '') || '');
    const allowPrivateIPs = this.envService.getArbitrary('ALLOW_PRIVATE_IPS', 'true') === 'true';
    const allowLoopback = this.envService.getArbitrary('ALLOW_LOOPBACK', 'true') === 'true';
    const cacheEnabled = this.envService.getArbitrary('IP_CACHE_ENABLED', 'true') === 'true';
    const cacheTTL = parseInt(this.envService.getArbitrary('IP_CACHE_TTL', '300') || '300', 10); // 5 minutes

    return {
      enabled,
      allowedIPs,
      allowedCIDRs,
      allowPrivateIPs,
      allowLoopback,
      cacheEnabled,
      cacheTTL,
    };
  }

  /**
   * Parse comma-separated IP list from environment variable
   */
  private parseIPList(ipListString: string): string[] {
    if (!ipListString.trim()) {
      return [];
    }

    return ipListString
      .split(',')
      .map((ip) => ip.trim())
      .filter((ip) => ip.length > 0);
  }

  /**
   * Normalize IP address (remove IPv6 brackets, etc.)
   */
  private normalizeIP(ipAddress: string): string {
    let normalized = ipAddress.trim();

    // Remove IPv6 brackets
    if (normalized.startsWith('[') && normalized.endsWith(']')) {
      normalized = normalized.slice(1, -1);
    }

    // Handle IPv4-mapped IPv6 addresses
    if (normalized.startsWith('::ffff:')) {
      const ipv4Part = normalized.substring(7);
      if (isIPv4(ipv4Part)) {
        normalized = ipv4Part;
      }
    }

    return normalized;
  }

  /**
   * Get IP address type
   */
  private getIPType(ipAddress: string): 'IPv4' | 'IPv6' | 'Invalid' {
    if (!isIP(ipAddress)) {
      return 'Invalid';
    }
    return isIPv4(ipAddress) ? 'IPv4' : 'IPv6';
  }

  /**
   * Check IP against whitelist rules
   */
  private async checkIPWhitelist(ipAddress: string, operation: string): Promise<Omit<IPValidationResult, 'ipType'>> {
    await Promise.resolve();
    // Check exact IP matches
    if (this.defaultConfig.allowedIPs.includes(ipAddress)) {
      return {
        allowed: true,
        matchedRule: `Exact IP match: ${ipAddress}`,
      };
    }

    // Check CIDR ranges
    for (const cidr of this.defaultConfig.allowedCIDRs) {
      if (this.isIPInCIDR(ipAddress, cidr)) {
        return {
          allowed: true,
          matchedRule: `CIDR match: ${cidr}`,
        };
      }
    }

    // Check private IP ranges if allowed
    if (this.defaultConfig.allowPrivateIPs && this.isPrivateIP(ipAddress)) {
      return {
        allowed: true,
        matchedRule: 'Private IP range allowed',
      };
    }

    // Check loopback if allowed
    if (this.defaultConfig.allowLoopback && this.isLoopbackIP(ipAddress)) {
      return {
        allowed: true,
        matchedRule: 'Loopback IP allowed',
      };
    }

    return {
      allowed: false,
      reason: `IP address ${ipAddress} not in whitelist for operation ${operation}`,
    };
  }

  /**
   * Check if IP is in CIDR range
   */
  private isIPInCIDR(ipAddress: string, cidr: string): boolean {
    try {
      // This is a simplified implementation
      // In production, you might want to use a library like 'ip-range-check'
      const [network, prefixLength] = cidr.split('/');

      if (!network || !prefixLength) {
        return false;
      }

      // For now, just check if it's the same network address
      // This should be replaced with proper CIDR calculation
      return ipAddress.startsWith(
        network
          .split('.')
          .slice(0, Math.floor(parseInt(prefixLength) / 8))
          .join('.'),
      );
    } catch (error) {
      this.logger.warn(`Invalid CIDR format: ${cidr}`, error);
      return false;
    }
  }

  /**
   * Check if IP is in private range
   */
  private isPrivateIP(ipAddress: string): boolean {
    if (isIPv4(ipAddress)) {
      const parts = ipAddress.split('.').map(Number);

      // 10.0.0.0/8
      if (parts[0] === 10) return true;

      // **********/12
      if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return true;

      // ***********/16
      if (parts[0] === 192 && parts[1] === 168) return true;
    }

    // IPv6 private ranges (simplified)
    if (isIPv6(ipAddress)) {
      return ipAddress.startsWith('fc') || ipAddress.startsWith('fd');
    }

    return false;
  }

  /**
   * Check if IP is loopback
   */
  private isLoopbackIP(ipAddress: string): boolean {
    return ipAddress === '127.0.0.1' || ipAddress === '::1' || ipAddress.startsWith('127.');
  }

  /**
   * Get cached validation result
   */
  private async getCachedValidation(
    ipAddress: string,
    operation: string,
  ): Promise<Omit<IPValidationResult, 'ipType'> | null> {
    try {
      const cacheKey = `ip_whitelist:${ipAddress}:${operation}`;
      const cached = await this.cacheService.get<string>(cacheKey);

      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      this.logger.warn(`Failed to get cached IP validation for ${ipAddress}:`, error);
    }

    return null;
  }

  /**
   * Cache validation result
   */
  private async cacheValidationResult(
    ipAddress: string,
    operation: string,
    result: Omit<IPValidationResult, 'ipType'>,
  ): Promise<void> {
    try {
      const cacheKey = `ip_whitelist:${ipAddress}:${operation}`;
      await this.cacheService.set(cacheKey, JSON.stringify(result), this.defaultConfig.cacheTTL);
    } catch (error) {
      this.logger.warn(`Failed to cache IP validation for ${ipAddress}:`, error);
    }
  }

  /**
   * Clear IP cache
   */
  private async clearIPCache(ipAddress: string): Promise<void> {
    try {
      const pattern = `ip_whitelist:${ipAddress}:*`;
      // Note: This is a simplified implementation
      // In production, you might need to use Redis SCAN or similar
      await this.cacheService.del(pattern);
    } catch (error) {
      this.logger.warn(`Failed to clear IP cache for ${ipAddress}:`, error);
    }
  }

  /**
   * Log IP access violation
   */
  private async logIPViolation(
    ipAddress: string,
    operation: string,
    reason: string,
    userId?: string,
    correlationId?: string,
  ): Promise<void> {
    await Promise.resolve();
    this.logger.warn(`IP access denied: ${ipAddress} for operation ${operation} - ${reason}`, {
      ipAddress,
      operation,
      reason,
      userId,
      correlationId,
    });
  }

  /**
   * Log successful IP access
   */
  private async logIPAccess(
    ipAddress: string,
    operation: string,
    matchedRule: string,
    userId?: string,
    correlationId?: string,
  ): Promise<void> {
    await Promise.resolve();
    this.logger.debug(`IP access granted: ${ipAddress} for operation ${operation} - ${matchedRule}`, {
      ipAddress,
      operation,
      matchedRule,
      userId,
      correlationId,
    });
  }
}
