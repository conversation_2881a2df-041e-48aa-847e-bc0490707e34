import { Injectable, Logger } from '@nestjs/common';
import { EnvService, ConfigService } from '@app/core';
import type { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import { DateTimeUtilsService } from '@app/utils/datetime-utils.service';

export interface SecurityConfig {
  cors: CorsOptions;
  helmet: {
    contentSecurityPolicy: {
      directives: Record<string, string[]>;
    };
    crossOriginEmbedderPolicy: boolean;
    crossOriginOpenerPolicy: { policy: string };
    crossOriginResourcePolicy: { policy: string };
    dnsPrefetchControl: boolean;
    frameguard: { action: string };
    hidePoweredBy: boolean;
    hsts: {
      maxAge: number;
      includeSubDomains: boolean;
      preload: boolean;
    };
    ieNoOpen: boolean;
    noSniff: boolean;
    originAgentCluster: boolean;
    permittedCrossDomainPolicies: boolean;
    referrerPolicy: { policy: string[] };
    xssFilter: boolean;
  };
  rateLimit: {
    ttl: number;
    limit: number;
    skipIf?: (context: Record<string, unknown>) => boolean;
  };
}

/**
 * Application-wide security configuration service
 *
 * Requirements:
 * - 4.4: Security headers and CORS configuration
 * - 4.6: IP whitelisting for sensitive operations
 * - 6.6: Audit trails for security and compliance
 */
@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);
  private securityConfig: SecurityConfig;

  constructor(
    private readonly envService: EnvService,
    private readonly configService: ConfigService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    // Initialize with default config, will be loaded asynchronously
    this.securityConfig = this.getDefaultSecurityConfig();
    void this.initializeSecurityConfig();
  }

  /**
   * Initialize security configuration asynchronously
   */
  private async initializeSecurityConfig(): Promise<void> {
    try {
      this.securityConfig = await this.loadSecurityConfig();
    } catch (error) {
      this.logger.error('Failed to load security configuration, using defaults', error);
    }
  }

  /**
   * Get CORS configuration
   */
  getCorsConfig(): CorsOptions {
    return this.securityConfig.cors;
  }

  /**
   * Get Helmet security headers configuration
   */
  getHelmetConfig() {
    return this.securityConfig.helmet;
  }

  /**
   * Get rate limiting configuration
   */
  getRateLimitConfig() {
    return this.securityConfig.rateLimit;
  }

  /**
   * Get security configuration for different environments
   */
  getSecurityConfigForEnvironment(environment: string): Partial<SecurityConfig> {
    const configs: Record<string, Partial<SecurityConfig>> = {
      development: {
        cors: {
          origin: true,
          credentials: true,
          methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
          allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID'],
        },
        rateLimit: {
          ttl: 60000, // 1 minute
          limit: 1000, // Very high for development
        },
      },

      staging: {
        cors: {
          origin: ['https://staging.patterntrade.com', 'https://staging-admin.patterntrade.com'],
          credentials: true,
          methods: ['GET', 'POST', 'PUT', 'DELETE'],
          allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID'],
        },
        rateLimit: {
          ttl: 60000, // 1 minute
          limit: 200,
        },
      },

      production: {
        cors: {
          origin: ['https://patterntrade.com', 'https://admin.patterntrade.com', 'https://app.patterntrade.com'],
          credentials: true,
          methods: ['GET', 'POST', 'PUT', 'DELETE'],
          allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID'],
          optionsSuccessStatus: 200,
        },
        rateLimit: {
          ttl: 60000, // 1 minute
          limit: 100,
        },
      },
    };

    return configs[environment] || configs.production;
  }

  /**
   * Validate security headers in request
   */
  validateSecurityHeaders(headers: Record<string, string>): {
    valid: boolean;
    missingHeaders: string[];
    recommendations: string[];
  } {
    const requiredHeaders = ['user-agent', 'accept'];
    const recommendedHeaders = ['x-correlation-id', 'x-forwarded-for'];

    const missingHeaders: string[] = [];
    const recommendations: string[] = [];

    // Check required headers
    for (const header of requiredHeaders) {
      if (!headers[header.toLowerCase()]) {
        missingHeaders.push(header);
      }
    }

    // Check recommended headers
    for (const header of recommendedHeaders) {
      if (!headers[header.toLowerCase()]) {
        recommendations.push(`Consider adding ${header} header for better tracing`);
      }
    }

    // Check for suspicious patterns
    const userAgent = headers['user-agent'];
    if (userAgent && this.isSuspiciousUserAgent(userAgent)) {
      recommendations.push('User-Agent appears to be automated - ensure this is expected');
    }

    return {
      valid: missingHeaders.length === 0,
      missingHeaders,
      recommendations,
    };
  }

  /**
   * Generate security report for audit
   */
  generateSecurityReport(): {
    timestamp: string;
    environment: string;
    corsEnabled: boolean;
    helmetEnabled: boolean;
    rateLimitEnabled: boolean;
    securityHeaders: string[];
    recommendations: string[];
  } {
    const environment = this.envService.get('NODE_ENV');

    return {
      timestamp: this.dateTimeUtils.getUtcNow(),
      environment,
      corsEnabled: true,
      helmetEnabled: true,
      rateLimitEnabled: true,
      securityHeaders: [
        'Content-Security-Policy',
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-XSS-Protection',
        'Strict-Transport-Security',
        'Referrer-Policy',
      ],
      recommendations: this.getSecurityRecommendations(environment),
    };
  }

  /**
   * Get rate limit configuration for different operations
   */
  getOperationRateLimitConfig(operation: string): { ttl: number; limit: number } {
    const configs: Record<string, { ttl: number; limit: number }> = {
      // Authentication operations - stricter limits
      LOGIN: { ttl: 300000, limit: 5 }, // 5 attempts per 5 minutes
      REGISTER: { ttl: 300000, limit: 3 }, // 3 attempts per 5 minutes
      FORGOT_PASSWORD: { ttl: 300000, limit: 3 }, // 3 attempts per 5 minutes
      RESET_PASSWORD: { ttl: 300000, limit: 5 }, // 5 attempts per 5 minutes

      // Broker operations - moderate limits
      CREATE_BROKER_ACCOUNT: { ttl: 60000, limit: 5 }, // 5 per minute
      DELETE_BROKER_ACCOUNT: { ttl: 60000, limit: 3 }, // 3 per minute
      UPDATE_BROKER_CREDENTIALS: { ttl: 300000, limit: 10 }, // 10 per 5 minutes
      OAUTH_INITIATE: { ttl: 300000, limit: 20 }, // 20 per 5 minutes
      OAUTH_CALLBACK: { ttl: 300000, limit: 30 }, // 30 per 5 minutes

      // Regular API operations - normal limits
      GET_ACCOUNTS: { ttl: 60000, limit: 100 }, // 100 per minute
      GET_HEALTH: { ttl: 60000, limit: 200 }, // 200 per minute
      RECONNECT: { ttl: 300000, limit: 20 }, // 20 per 5 minutes

      // Token operations - moderate limits
      REFRESH_TOKEN: { ttl: 300000, limit: 50 }, // 50 per 5 minutes
      VALIDATE_TOKEN: { ttl: 60000, limit: 100 }, // 100 per minute
      REVOKE_TOKEN: { ttl: 300000, limit: 20 }, // 20 per 5 minutes
    };

    return configs[operation] || { ttl: 60000, limit: 100 }; // Default: 100 per minute
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Get default security configuration (synchronous fallback)
   */
  private getDefaultSecurityConfig(): SecurityConfig {
    const environment = this.envService.get('NODE_ENV') || 'development';

    // Default configuration with fallback values
    const baseConfig: SecurityConfig = {
      cors: {
        origin: false,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID', 'X-Forwarded-For', 'X-Real-IP'],
        exposedHeaders: ['X-Correlation-ID', 'X-Rate-Limit-Remaining'],
      },
      helmet: {
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", 'data:', 'https:'],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
          },
        },
        crossOriginEmbedderPolicy: false,
        crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
        crossOriginResourcePolicy: { policy: 'cross-origin' },
        dnsPrefetchControl: true,
        frameguard: { action: 'deny' },
        hidePoweredBy: true,
        hsts: {
          maxAge: 31536000,
          includeSubDomains: true,
          preload: true,
        },
        ieNoOpen: true,
        noSniff: true,
        originAgentCluster: true,
        permittedCrossDomainPolicies: false,
        referrerPolicy: { policy: ['no-referrer'] },
        xssFilter: true,
      },
      rateLimit: {
        ttl: 60000, // Default 1 minute
        limit: 100, // Default 100 requests
      },
    };

    // Environment-specific adjustments
    if (environment === 'development' || environment === 'local') {
      baseConfig.cors.origin = true;
      baseConfig.helmet.contentSecurityPolicy.directives.connectSrc = ["'self'", 'ws:', 'wss:'];
    }

    return baseConfig;
  }

  /**
   * Load security configuration from environment and config service
   */
  private async loadSecurityConfig(): Promise<SecurityConfig> {
    const environment = this.envService.get('NODE_ENV') || 'development';

    // Get non-sensitive values from ConfigService
    const baseUrl = await this.configService.get<string>('API_BASE_URL').catch(() => 'http://localhost:3000');
    const rateLimitRequests = await this.configService.get<number>('BROKER_RATE_LIMIT_REQUESTS').catch(() => 100);
    const rateLimitWindow = await this.configService.get<number>('BROKER_RATE_LIMIT_WINDOW').catch(() => 60000);

    // Get base configuration and update with loaded values
    const baseConfig = this.getDefaultSecurityConfig();

    // Update rate limiting with loaded configuration
    baseConfig.rateLimit = {
      ttl: rateLimitWindow,
      limit: rateLimitRequests,
      skipIf: (context: Record<string, unknown>) => {
        // Skip rate limiting for health checks
        try {
          const request = context.getRequest as () => { url?: string };
          const url = request?.call(context)?.url;
          return url?.includes('/health') || false;
        } catch {
          return false;
        }
      },
    };

    // Update CORS with base URL
    baseConfig.helmet.contentSecurityPolicy.directives.connectSrc = ["'self'", baseUrl];

    // Apply environment-specific overrides
    const envConfig = this.getSecurityConfigForEnvironment(environment);

    return {
      ...baseConfig,
      ...envConfig,
      cors: { ...baseConfig.cors, ...envConfig.cors },
      helmet: { ...baseConfig.helmet, ...envConfig.helmet },
      rateLimit: { ...baseConfig.rateLimit, ...envConfig.rateLimit },
    };
  }

  /**
   * Check if User-Agent appears suspicious
   */
  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
      /go-http-client/i,
      /okhttp/i,
    ];

    return suspiciousPatterns.some((pattern) => pattern.test(userAgent));
  }

  /**
   * Get security recommendations for environment
   */
  private getSecurityRecommendations(environment: string): string[] {
    const recommendations: string[] = [];

    if (environment === 'development') {
      recommendations.push(
        'Development environment detected - ensure security measures are enabled in production',
        'Consider using HTTPS even in development for testing',
        'Review CORS origins before deploying to production',
      );
    }

    if (environment === 'production') {
      recommendations.push(
        'Regularly review and update security headers',
        'Monitor for suspicious IP addresses and user agents',
        'Implement additional monitoring for rate limit violations',
        'Consider implementing additional authentication factors for sensitive operations',
      );
    }

    return recommendations;
  }
}
