import { Injectable, Logger } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { DateTimeUtilsService } from '@app/utils/datetime-utils.service';
import {
  AuditEventCategory,
  AuditEventCategoryEnum,
  AuditEventSeverity,
  AuditEventSeverityEnum,
  AuditEventType,
  AuditEventTypeEnum,
} from './security.constants';

export interface AuditLogContext {
  correlationId: string;
  userId?: string;
  accountId?: string;
  operation: string;
  startTime: Date;
  metadata?: Record<string, unknown>;
}

export interface AuditEvent {
  eventType: AuditEventType;
  userId: string;
  accountId?: string;
  details: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  correlationId: string;
  severity: AuditEventSeverity;
  category: AuditEventCategory;
  module: string; // e.g., 'BROKER', 'AUTH', 'USER', 'SYSTEM'
}

/**
 * Application-wide comprehensive audit logging service
 *
 * Requirements:
 * - 6.1: Detailed error logging with context
 * - 6.6: Audit trails for security and compliance
 * - 4.4: Comprehensive audit logging for all operations
 */
@Injectable()
export class AuditLoggingService {
  private readonly logger = new Logger(AuditLoggingService.name);
  private readonly auditLogger = new Logger('ApplicationAudit');
  private readonly securityLogger = new Logger('ApplicationSecurity');
  private readonly complianceLogger = new Logger('ApplicationCompliance');

  constructor(private readonly dateTimeUtils: DateTimeUtilsService) {}

  /**
   * Generate a new correlation ID for tracking operations
   */
  generateCorrelationId(): string {
    return randomUUID();
  }

  /**
   * Create a new log context for operations
   */
  createLogContext(
    operation: string,
    options?: {
      userId?: string;
      accountId?: string;
      metadata?: Record<string, unknown>;
      correlationId?: string;
    },
  ): AuditLogContext {
    return {
      correlationId: options?.correlationId || this.generateCorrelationId(),
      userId: options?.userId,
      accountId: options?.accountId,
      operation,
      startTime: this.dateTimeUtils.getNewDate(),
      metadata: options?.metadata,
    };
  }

  /**
   * Log the start of an operation
   */
  logOperationStart(context: AuditLogContext, module: string = 'SYSTEM'): void {
    this.logger.log({
      message: `Starting operation: ${context.operation}`,
      correlationId: context.correlationId,
      userId: context.userId,
      accountId: context.accountId,
      module,
      timestamp: context.startTime.toISOString(),
      metadata: this.sanitizeLogData(context.metadata),
    });
  }

  /**
   * Log successful completion of an operation
   */
  logOperationSuccess(context: AuditLogContext, result?: Record<string, unknown>, module: string = 'SYSTEM'): void {
    const duration = this.dateTimeUtils.getTime() - context.startTime.getTime();

    this.logger.log({
      message: `Operation completed successfully: ${context.operation}`,
      correlationId: context.correlationId,
      userId: context.userId,
      accountId: context.accountId,
      module,
      duration: `${duration}ms`,
      result: this.sanitizeLogData(result),
      timestamp: this.dateTimeUtils.getUtcNow(),
    });
  }

  /**
   * Log operation failure with detailed error context
   */
  logOperationError(context: AuditLogContext, error: Error, module: string = 'SYSTEM'): void {
    const duration = this.dateTimeUtils.getTime() - context.startTime.getTime();

    this.logger.error({
      message: `Operation failed: ${context.operation}`,
      correlationId: context.correlationId,
      userId: context.userId,
      accountId: context.accountId,
      module,
      duration: `${duration}ms`,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      timestamp: this.dateTimeUtils.getUtcNow(),
    });
  }

  /**
   * Log audit events for compliance and security monitoring
   */
  logAuditEvent(event: AuditEvent): void {
    const logData = {
      eventType: event.eventType,
      userId: event.userId,
      accountId: event.accountId,
      module: event.module,
      correlationId: event.correlationId,
      timestamp: event.timestamp.toISOString(),
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      severity: event.severity,
      category: event.category,
      details: this.sanitizeAuditData(event.details),
    };

    // Log to appropriate logger based on category
    switch (event.category) {
      case AuditEventCategoryEnum.enum.SECURITY:
      case AuditEventCategoryEnum.enum.AUTHENTICATION:
      case AuditEventCategoryEnum.enum.AUTHORIZATION:
        this.securityLogger.log(logData);
        break;
      case AuditEventCategoryEnum.enum.COMPLIANCE:
        this.complianceLogger.log(logData);
        break;
      default:
        this.auditLogger.log(logData);
    }

    // Also log high severity events to main logger
    if (
      event.severity === AuditEventSeverityEnum.enum.HIGH ||
      event.severity === AuditEventSeverityEnum.enum.CRITICAL
    ) {
      this.logger.warn(`${event.severity} severity audit event: ${event.eventType}`, logData);
    }
  }

  /**
   * Log security-related events
   */
  logSecurityEvent(
    eventType: AuditEventType,
    userId: string,
    operation: string,
    reason: string,
    correlationId: string,
    module: string,
    ipAddress?: string,
    userAgent?: string,
    additionalDetails?: Record<string, unknown>,
  ): void {
    const event: AuditEvent = {
      eventType,
      userId,
      details: {
        operation,
        reason,
        timestamp: this.dateTimeUtils.getUtcNow(),
        ...additionalDetails,
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: this.getSeverityForEvent(eventType),
      category: AuditEventCategoryEnum.enum.SECURITY,
      module,
    };

    this.logAuditEvent(event);
  }

  /**
   * Log authentication events
   */
  logAuthenticationEvent(
    eventType:
      | typeof AuditEventTypeEnum.enum.AUTHENTICATION_FAILED
      | typeof AuditEventTypeEnum.enum.AUTHENTICATION_SUCCESS
      | typeof AuditEventTypeEnum.enum.USER_LOGIN
      | typeof AuditEventTypeEnum.enum.USER_LOGOUT,
    userId: string,
    correlationId: string,
    ipAddress?: string,
    userAgent?: string,
    details?: Record<string, unknown>,
  ): void {
    const event: AuditEvent = {
      eventType,
      userId,
      details: {
        timestamp: this.dateTimeUtils.getUtcNow(),
        ...details,
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: eventType.includes('FAILED') ? AuditEventSeverityEnum.enum.HIGH : AuditEventSeverityEnum.enum.LOW,
      category: AuditEventCategoryEnum.enum.AUTHENTICATION,
      module: 'AUTH',
    };

    this.logAuditEvent(event);
  }

  /**
   * Log admin actions
   */
  logAdminAction(
    action: string,
    adminUserId: string,
    targetUserId?: string,
    targetAccountId?: string,
    correlationId?: string,
    ipAddress?: string,
    userAgent?: string,
    details?: Record<string, unknown>,
  ): void {
    const event: AuditEvent = {
      eventType: AuditEventTypeEnum.enum.ADMIN_ACTION,
      userId: adminUserId,
      accountId: targetAccountId,
      details: {
        action,
        targetUserId,
        timestamp: this.dateTimeUtils.getUtcNow(),
        ...details,
      },
      correlationId: correlationId || this.generateCorrelationId(),
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: AuditEventSeverityEnum.enum.MEDIUM,
      category: AuditEventCategoryEnum.enum.COMPLIANCE,
      module: 'ADMIN',
    };

    this.logAuditEvent(event);
  }

  /**
   * Log system configuration changes
   */
  logSystemConfigChange(
    configKey: string,
    oldValue: unknown,
    newValue: unknown,
    adminUserId: string,
    correlationId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    const event: AuditEvent = {
      eventType: AuditEventTypeEnum.enum.SYSTEM_CONFIG_CHANGED,
      userId: adminUserId,
      details: {
        configKey,
        oldValue: this.sanitizeConfigValue(oldValue),
        newValue: this.sanitizeConfigValue(newValue),
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId: correlationId || this.generateCorrelationId(),
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: AuditEventSeverityEnum.enum.HIGH,
      category: AuditEventCategoryEnum.enum.SYSTEM,
      module: 'CONFIG',
    };

    this.logAuditEvent(event);
  }

  /**
   * Log data export/import events
   */
  logDataEvent(
    eventType: typeof AuditEventTypeEnum.enum.DATA_EXPORT | typeof AuditEventTypeEnum.enum.DATA_IMPORT,
    userId: string,
    dataType: string,
    recordCount: number,
    correlationId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    const event: AuditEvent = {
      eventType,
      userId,
      details: {
        dataType,
        recordCount,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId: correlationId || this.generateCorrelationId(),
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: AuditEventSeverityEnum.enum.MEDIUM,
      category: AuditEventCategoryEnum.enum.COMPLIANCE,
      module: 'DATA',
    };

    this.logAuditEvent(event);
  }

  /**
   * Log performance metrics for monitoring
   */
  logPerformanceMetrics(
    context: AuditLogContext,
    metrics: {
      responseTime: number;
      memoryUsage?: number;
      cpuUsage?: number;
      connectionCount?: number;
    },
    module: string = 'SYSTEM',
  ): void {
    this.logger.debug({
      message: `Performance metrics for operation: ${context.operation}`,
      correlationId: context.correlationId,
      module,
      metrics,
      timestamp: this.dateTimeUtils.getUtcNow(),
    });
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Get severity level for event type
   */
  private getSeverityForEvent(eventType: AuditEventType): AuditEventSeverity {
    const severityMap: Record<string, AuditEventSeverity> = {
      // Critical events
      [AuditEventTypeEnum.enum.ACCOUNT_DELETED]: AuditEventSeverityEnum.enum.CRITICAL,
      [AuditEventTypeEnum.enum.SECURITY_VIOLATION]: AuditEventSeverityEnum.enum.CRITICAL,
      [AuditEventTypeEnum.enum.SYSTEM_CONFIG_CHANGED]: AuditEventSeverityEnum.enum.CRITICAL,

      // High severity events
      [AuditEventTypeEnum.enum.AUTHENTICATION_FAILED]: AuditEventSeverityEnum.enum.HIGH,
      [AuditEventTypeEnum.enum.PERMISSION_DENIED]: AuditEventSeverityEnum.enum.HIGH,
      [AuditEventTypeEnum.enum.IP_ACCESS_DENIED]: AuditEventSeverityEnum.enum.HIGH,
      [AuditEventTypeEnum.enum.CREDENTIALS_UPDATED]: AuditEventSeverityEnum.enum.HIGH,
      [AuditEventTypeEnum.enum.ROLE_CHANGED]: AuditEventSeverityEnum.enum.HIGH,
      [AuditEventTypeEnum.enum.PASSWORD_CHANGED]: AuditEventSeverityEnum.enum.HIGH,

      // Medium severity events
      [AuditEventTypeEnum.enum.ACCOUNT_CREATED]: AuditEventSeverityEnum.enum.MEDIUM,
      [AuditEventTypeEnum.enum.ACCOUNT_UPDATED]: AuditEventSeverityEnum.enum.MEDIUM,
      [AuditEventTypeEnum.enum.RATE_LIMIT_EXCEEDED]: AuditEventSeverityEnum.enum.MEDIUM,
      [AuditEventTypeEnum.enum.SUSPICIOUS_ACTIVITY]: AuditEventSeverityEnum.enum.MEDIUM,
      [AuditEventTypeEnum.enum.ADMIN_ACTION]: AuditEventSeverityEnum.enum.MEDIUM,
      [AuditEventTypeEnum.enum.TOKEN_REVOKED]: AuditEventSeverityEnum.enum.MEDIUM,
      [AuditEventTypeEnum.enum.DATA_EXPORT]: AuditEventSeverityEnum.enum.MEDIUM,
      [AuditEventTypeEnum.enum.DATA_IMPORT]: AuditEventSeverityEnum.enum.MEDIUM,

      // Low severity events
      [AuditEventTypeEnum.enum.AUTHENTICATION_SUCCESS]: AuditEventSeverityEnum.enum.LOW,
      [AuditEventTypeEnum.enum.USER_LOGIN]: AuditEventSeverityEnum.enum.LOW,
      [AuditEventTypeEnum.enum.USER_LOGOUT]: AuditEventSeverityEnum.enum.LOW,
      [AuditEventTypeEnum.enum.TOKEN_REFRESHED]: AuditEventSeverityEnum.enum.LOW,
      [AuditEventTypeEnum.enum.CONNECTION_ESTABLISHED]: AuditEventSeverityEnum.enum.LOW,
      [AuditEventTypeEnum.enum.IP_ACCESS_GRANTED]: AuditEventSeverityEnum.enum.LOW,
    };

    return severityMap[eventType] || AuditEventSeverityEnum.enum.MEDIUM;
  }

  /**
   * Sanitize log data to remove sensitive information
   */
  private sanitizeLogData(data?: Record<string, unknown>): Record<string, unknown> | undefined {
    if (!data) return undefined;

    const sensitiveKeys = [
      'password',
      'apiKey',
      'apiSecret',
      'accessToken',
      'refreshToken',
      'credentials',
      'authorization',
      'secret',
      'key',
      'token',
    ];

    const sanitized = { ...data };

    for (const key of Object.keys(sanitized)) {
      const lowerKey = key.toLowerCase();
      if (sensitiveKeys.some((sensitive) => lowerKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * Sanitize audit data to remove sensitive information while preserving audit trail
   */
  private sanitizeAuditData(data: Record<string, unknown>): Record<string, unknown> {
    const sanitized = { ...data };

    // For audit logs, we keep more information but still redact sensitive values
    const sensitiveKeys = ['apikey', 'apisecret', 'accesstoken', 'refreshtoken', 'password', 'secret'];

    for (const key of Object.keys(sanitized)) {
      const lowerKey = key.toLowerCase();
      if (sensitiveKeys.some((sensitive) => lowerKey.includes(sensitive))) {
        // For audit logs, we might want to keep partial information
        if (typeof sanitized[key] === 'string') {
          const value = sanitized[key];
          sanitized[key] =
            value.length > 8 ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}` : '[REDACTED]';
        } else {
          sanitized[key] = '[REDACTED]';
        }
      }
    }

    return sanitized;
  }

  /**
   * Sanitize configuration values for logging
   */
  private sanitizeConfigValue(value: unknown): unknown {
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      if (lowerValue.includes('password') || lowerValue.includes('secret') || lowerValue.includes('key')) {
        return '[REDACTED]';
      }
    }
    return value;
  }
}
