import { Test, TestingModule } from '@nestjs/testing';
import { SecurityService } from './security.service';
import { EnvService, ConfigService } from '@app/core';
import { DateTimeUtilsService } from '@app/utils';

describe('SecurityService', () => {
  let service: SecurityService;
  let envService: jest.Mocked<EnvService>;
  let configService: jest.Mocked<ConfigService>;
  let dateTimeUtils: jest.Mocked<DateTimeUtilsService>;

  beforeEach(async () => {
    const mockEnvService = {
      get: jest.fn(),
      getArbitrary: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const mockDateTimeUtils = {
      // Add any required methods
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SecurityService,
        {
          provide: EnvService,
          useValue: mockEnvService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: DateTimeUtilsService,
          useValue: mockDateTimeUtils,
        },
      ],
    }).compile();

    service = module.get<SecurityService>(SecurityService);
    envService = module.get(EnvService);
    configService = module.get(ConfigService);
    dateTimeUtils = module.get(DateTimeUtilsService);
  });

  beforeEach(() => {
    // Setup default environment values
    envService.get.mockImplementation((key: string) => {
      const defaults = {
        NODE_ENV: 'test',
      };
      return defaults[key as keyof typeof defaults];
    });

    // Setup default config service values
    configService.get.mockImplementation(async (key: string) => {
      const defaults = {
        API_BASE_URL: 'http://localhost:3000',
        BROKER_RATE_LIMIT_REQUESTS: 100,
        BROKER_RATE_LIMIT_WINDOW: 60000,
      };
      return defaults[key as keyof typeof defaults];
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return CORS configuration', () => {
    const corsConfig = service.getCorsConfig();
    expect(corsConfig).toBeDefined();
    expect(corsConfig.credentials).toBe(true);
    expect(corsConfig.methods).toContain('GET');
    expect(corsConfig.methods).toContain('POST');
  });

  it('should return Helmet configuration', () => {
    const helmetConfig = service.getHelmetConfig();
    expect(helmetConfig).toBeDefined();
    expect(helmetConfig.hidePoweredBy).toBe(true);
    expect(helmetConfig.noSniff).toBe(true);
  });

  it('should return rate limit configuration', () => {
    const rateLimitConfig = service.getRateLimitConfig();
    expect(rateLimitConfig).toBeDefined();
    expect(rateLimitConfig.ttl).toBe(60000);
    expect(rateLimitConfig.limit).toBe(100);
  });

  it('should validate security headers', () => {
    const headers = {
      'user-agent': 'Mozilla/5.0',
      accept: 'application/json',
    };

    const result = service.validateSecurityHeaders(headers);
    expect(result.valid).toBe(true);
    expect(result.missingHeaders).toHaveLength(0);
  });

  it('should detect missing required headers', () => {
    const headers = {
      accept: 'application/json',
      // Missing user-agent
    };

    const result = service.validateSecurityHeaders(headers);
    expect(result.valid).toBe(false);
    expect(result.missingHeaders).toContain('user-agent');
  });

  it('should generate security report', () => {
    const report = service.generateSecurityReport();
    expect(report).toBeDefined();
    expect(report.environment).toBe('test');
    expect(report.corsEnabled).toBe(true);
    expect(report.helmetEnabled).toBe(true);
    expect(report.rateLimitEnabled).toBe(true);
  });

  it('should return operation-specific rate limit config', () => {
    const loginConfig = service.getOperationRateLimitConfig('LOGIN');
    expect(loginConfig.limit).toBe(5);
    expect(loginConfig.ttl).toBe(300000);

    const defaultConfig = service.getOperationRateLimitConfig('UNKNOWN_OPERATION');
    expect(defaultConfig.limit).toBe(100);
    expect(defaultConfig.ttl).toBe(60000);
  });
});
