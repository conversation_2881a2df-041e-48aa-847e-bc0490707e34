import { SetMetadata } from '@nestjs/common';
import type { IPWhitelistOperations } from './security.constants';

export const IP_WHITELIST_KEY = 'ipWhitelist';

/**
 * Decorator to enable IP whitelisting for specific endpoints
 * @param operation - The operation name for audit logging
 */
export const RequireIPWhitelist = (operation: IPWhitelistOperations) => SetMetadata(IP_WHITELIST_KEY, operation);
