import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { IPWhitelistService } from './ip-whitelist.service';
import { AuditLoggingService } from './audit-logging.service';
import { IP_WHITELIST_KEY } from './ip-whitelist.decorator';
import { DateTimeUtilsService } from '@app/utils/datetime-utils.service';

interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    email: string;
    role: 'admin' | 'user';
  };
}

/**
 * Guard to enforce IP whitelisting for sensitive operations
 *
 * Requirements:
 * - 4.6: IP whitelisting for sensitive operations
 * - 6.6: Audit trails for security and compliance
 */
@Injectable()
export class IPWhitelistGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly ipWhitelistService: IPWhitelistService,
    private readonly auditLoggingService: AuditLoggingService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const operation = this.reflector.getAllAndOverride<string>(IP_WHITELIST_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If no IP whitelist operation is specified, allow access
    if (!operation) {
      return true;
    }

    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
    const ipAddress = this.getClientIP(request);
    const userId = request.user?.id?.toString();
    const correlationId = this.auditLoggingService.generateCorrelationId();
    const userAgent = request.get('User-Agent');

    try {
      // Validate IP access
      const result = await this.ipWhitelistService.validateIPAccess(ipAddress, operation, userId, correlationId);

      if (!result.allowed) {
        // Log security violation
        this.auditLoggingService.logSecurityEvent(
          'IP_ACCESS_DENIED',
          userId || 'UNKNOWN',
          operation,
          result.reason || 'IP not in whitelist',
          correlationId,
          'SECURITY',
          ipAddress,
          userAgent,
          {
            ipType: result.ipType,
            endpoint: request.url,
            method: request.method,
          },
        );

        throw new ForbiddenException({
          error: 'IP_ACCESS_DENIED',
          message: `Access denied from IP address ${ipAddress}`,
          correlationId,
          timestamp: this.dateTimeUtils.getUtcNow(),
        });
      }

      // Log successful access for sensitive operations
      if (this.ipWhitelistService.isSensitiveOperation(operation)) {
        this.auditLoggingService.logSecurityEvent(
          'IP_ACCESS_GRANTED',
          userId || 'UNKNOWN',
          operation,
          result.matchedRule || 'IP whitelist validation passed',
          correlationId,
          'SECURITY',
          ipAddress,
          userAgent,
          {
            ipType: result.ipType,
            endpoint: request.url,
            method: request.method,
          },
        );
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      // Log system error
      this.auditLoggingService.logSecurityEvent(
        'SECURITY_VIOLATION',
        userId || 'UNKNOWN',
        operation,
        `IP whitelist validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        correlationId,
        'SECURITY',
        ipAddress,
        userAgent,
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          endpoint: request.url,
          method: request.method,
        },
      );

      throw new ForbiddenException({
        error: 'IP_VALIDATION_FAILED',
        message: 'IP validation failed',
        correlationId,
        timestamp: this.dateTimeUtils.getUtcNow(),
      });
    }
  }

  /**
   * Extract client IP address from request
   */
  private getClientIP(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}
