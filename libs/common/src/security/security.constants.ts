import { z } from 'zod';

export const IPWhitelistOperationsEnum = z.enum([
  'CREATE_BROKER_ACCOUNT',
  'UPDATE_BROKER_CREDENTIALS',
  'DELETE_BROKER_ACCOUNT',
  'OAUTH_INITIATE',
  'OAUTH_CALLBACK',
  'REFRESH_TOKEN',
  'REVOKE_TOKEN',
  'MONITORING_CONTROL',
]);

export type IPWhitelistOperations = z.infer<typeof IPWhitelistOperationsEnum>;

export const AuditEventTypeEnum = z.enum([
  'ACCOUNT_CREATED',
  'ACCOUNT_UPDATED',
  'ACCOUNT_DELETED',
  'CREDENTIALS_UPDATED',
  'OAUTH_INITIATED',
  'OAUTH_COMPLETED',
  'TOKEN_REFRESHED',
  'TOKEN_REVOKED',
  'CONNECTION_ESTABLISHED',
  'CONNECTION_LOST',
  'PERMISSION_DENIED',
  'SECURITY_VIOLATION',
  'RATE_LIMIT_EXCEEDED',
  'IP_ACCESS_DENIED',
  'IP_ACCESS_GRANTED',
  'SUSPICIOUS_ACTIVITY',
  'ADMIN_ACTION',
  'AUTHENTICATION_FAILED',
  'AUTHENTICATION_SUCCESS',
  'USER_LOGIN',
  'USER_LOGOUT',
  'PASSWORD_CHANGED',
  'EMAIL_CHANGED',
  'ROLE_CHANGED',
  'SYSTEM_CONFIG_CHANGED',
  'DATA_EXPORT',
  'DATA_IMPORT',
]);

export type AuditEventType = z.infer<typeof AuditEventTypeEnum>;

export const AuditEventCategoryEnum = z.enum([
  'SECURITY',
  'OPERATION',
  'SYSTEM',
  'COMPLIANCE',
  'AUTHENTICATION',
  'AUTHORIZATION',
]);

export type AuditEventCategory = z.infer<typeof AuditEventCategoryEnum>;

export const AuditEventSeverityEnum = z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']);

export type AuditEventSeverity = z.infer<typeof AuditEventSeverityEnum>;
