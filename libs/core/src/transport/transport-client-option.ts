import type { RedisOptions } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import type { EnvService } from '../env/env.service';

export function getRedisTransportOption(envService: EnvService) {
  const host = envService.get('REDIS_HOST');
  const port = envService.get('REDIS_PORT');
  const username = envService.get('REDIS_USERNAME');
  const password = envService.get('REDIS_PASSWORD');

  return {
    transport: Transport.REDIS,
    options: {
      host,
      port,
      username,
      password,
    },
  } as RedisOptions;
}
