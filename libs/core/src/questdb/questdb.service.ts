import { Injectable, Lo<PERSON>, OnM<PERSON>ule<PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { Pool, PoolClient, QueryResult } from 'pg';
import { z } from 'zod/v4';
import { EnvService } from '../env/env.service';
import { HealthCheckService } from '../health-check/health-check.service';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import {
  QuestDBError,
  createQuestDBConnectionError,
  createQuestDBQueryError,
  createQuestDBConfigError,
} from './questdb.error';
import { QuestDBConnectionConfig, QuestDBConnectionConfigSchema, createQuestDBConfig } from './questdb-config.schema';

/**
 * Query result interface for QuestDB operations
 */
export interface QuestDBQueryResult<T = Record<string, unknown>> {
  data: T[];
  rowCount: number;
  executedAt: string;
  executionTimeMs: number;
}

/**
 * Connection statistics interface
 */
export interface QuestDBConnectionStats {
  isConnected: boolean;
  totalConnections: number;
  idleConnections: number;
  waitingCount: number;
  connectionAttempts: number;
  lastHealthCheck: string | null;
  healthCheckCount: number;
  failedHealthChecks: number;
}

/**
 * Time-series insert options interface
 */
export interface TimeSeriesInsertOptions {
  table: string;
  timestamp: string | Date;
  data: Record<string, unknown>;
  designatedTimestamp?: string;
}

/**
 * Time-series query options interface
 */
export interface TimeSeriesQueryOptions {
  table: string;
  startTime: string | Date;
  endTime: string | Date;
  columns?: string[];
  orderBy?: 'ASC' | 'DESC';
  limit?: number;
}

/**
 * OHLCV data interface for market data
 */
export interface OHLCVData {
  symbol: string;
  timestamp: string | Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

/**
 * QuestDB Service
 *
 * Provides high-performance time-series database operations using QuestDB
 * via PostgreSQL wire protocol. Follows the same architectural patterns
 * as DrizzleService for consistency across the codebase.
 *
 * Features:
 * - Connection pooling and management
 * - Automatic reconnection with exponential backoff
 * - Health monitoring and status reporting
 * - Time-series specific operations
 * - Comprehensive error handling
 * - Integration with existing core services
 */
@Injectable()
export class QuestDBService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(QuestDBService.name);

  // Connection management
  private connection: Pool | null = null;
  private isConnected = false;

  // Resilience properties
  private connectionAttempts = 0;
  private readonly maxRetryAttempts = 5;
  private readonly retryDelay = 5000; // 5 seconds
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private reconnectionTimeout: NodeJS.Timeout | null = null;

  // Health check properties
  private lastHealthCheck: Date | null = null;
  private healthCheckCount = 0;
  private failedHealthChecks = 0;

  constructor(
    private readonly envService: EnvService,
    private readonly healthCheckService: HealthCheckService,
    private readonly datetimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  /**
   * Initialize the QuestDB service
   * Called automatically by NestJS when the module starts
   */
  async onModuleInit(): Promise<void> {
    this.logger.log('Initializing QuestDB service...');
    await this.connectWithRetry();
    this.startHealthCheck();
  }

  /**
   * Cleanup when the module is destroyed
   * Called automatically by NestJS when the module shuts down
   */
  async onModuleDestroy(): Promise<void> {
    this.logger.log('Shutting down QuestDB service...');
    await this.disconnect();
  }

  /**
   * Get the QuestDB connection pool
   * @throws {QuestDBError} If connection is not established
   */
  getConnection(): Pool {
    if (!this.connection || !this.isConnected) {
      throw new QuestDBError('CONNECTION_FAILED', {
        message: 'QuestDB not connected. Please ensure the service is initialized.',
      });
    }
    return this.connection;
  }

  /**
   * Check if the connection is healthy
   */
  isConnectionHealthy(): boolean {
    return this.isConnected && this.connection !== null;
  }

  /**
   * Get connection statistics
   */
  getConnectionStats(): QuestDBConnectionStats {
    return {
      isConnected: this.isConnected,
      totalConnections: this.connection?.totalCount || 0,
      idleConnections: this.connection?.idleCount || 0,
      waitingCount: this.connection?.waitingCount || 0,
      connectionAttempts: this.connectionAttempts,
      lastHealthCheck: this.lastHealthCheck ? this.lastHealthCheck.toISOString() : null,
      healthCheckCount: this.healthCheckCount,
      failedHealthChecks: this.failedHealthChecks,
    };
  }

  /**
   * Execute a SQL query against QuestDB
   * @param query SQL query string
   * @param params Query parameters for parameterized queries
   * @returns Query result with data, metadata, and execution statistics
   */
  async executeQuery<T = Record<string, unknown>>(query: string, params?: unknown[]): Promise<QuestDBQueryResult<T>> {
    if (!this.connection || !this.isConnected) {
      throw new QuestDBError('CONNECTION_FAILED', {
        message: 'QuestDB not connected. Please ensure the service is initialized.',
      });
    }

    const startTime = this.datetimeUtils.getTime();
    let client: PoolClient | null = null;

    try {
      // Get client from pool
      client = await this.connection.connect();

      // Execute query with parameters
      const result: QueryResult =
        params && params.length > 0 ? await client.query(query, params) : await client.query(query);

      const executionTime = this.datetimeUtils.getTime() - startTime;

      this.logger.debug('Query executed successfully', {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        executionTime,
        rowCount: result.rowCount || 0,
      });

      return {
        data: result.rows as T[],
        rowCount: result.rowCount || 0,
        executedAt: this.datetimeUtils.getUtcNow(),
        executionTimeMs: executionTime,
      };
    } catch (error) {
      const executionTime = this.datetimeUtils.getTime() - startTime;

      this.logger.error(`Query execution failed: ${this.errorUtils.getErrorMessage(error)}`, {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        executionTime,
        error: this.errorUtils.getErrorStack(error),
      });

      throw createQuestDBQueryError(query, error);
    } finally {
      // Always release the client back to the pool
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Execute a query with timeout
   * @param query SQL query string
   * @param params Query parameters
   * @param timeoutMs Timeout in milliseconds (default: 30000)
   */
  async executeQueryWithTimeout<T = Record<string, unknown>>(
    query: string,
    params?: unknown[],
    timeoutMs: number = 30000,
  ): Promise<QuestDBQueryResult<T>> {
    return Promise.race([
      this.executeQuery<T>(query, params),
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(
            new QuestDBError('CONNECTION_TIMEOUT', {
              message: `Query execution timed out after ${timeoutMs}ms`,
            }),
          );
        }, timeoutMs);
      }),
    ]);
  }

  /**
   * Execute multiple queries in a transaction
   * @param queries Array of query objects with query string and optional parameters
   */
  async executeTransaction(queries: Array<{ query: string; params?: unknown[] }>): Promise<QuestDBQueryResult[]> {
    if (!this.connection || !this.isConnected) {
      throw new QuestDBError('CONNECTION_FAILED', {
        message: 'QuestDB not connected. Please ensure the service is initialized.',
      });
    }

    const client = await this.connection.connect();
    const results: QuestDBQueryResult[] = [];

    try {
      await client.query('BEGIN');

      for (const { query, params } of queries) {
        const startTime = this.datetimeUtils.getTime();
        const result = params && params.length > 0 ? await client.query(query, params) : await client.query(query);

        results.push({
          data: result.rows,
          rowCount: result.rowCount || 0,
          executedAt: this.datetimeUtils.getUtcNow(),
          executionTimeMs: this.datetimeUtils.getTime() - startTime,
        });
      }

      await client.query('COMMIT');
      this.logger.debug(`Transaction completed successfully with ${queries.length} queries`);

      return results;
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error(`Transaction failed: ${this.errorUtils.getErrorMessage(error)}`, {
        error: this.errorUtils.getErrorStack(error),
      });

      throw new QuestDBError('TRANSACTION_FAILED', {
        message: `Transaction failed: ${this.errorUtils.getErrorMessage(error)}`,
        cause: error,
      });
    } finally {
      client.release();
    }
  }

  /**
   * Insert time-series data into QuestDB
   * @param options Time-series insert options
   */
  async insertTimeSeriesData(options: TimeSeriesInsertOptions): Promise<QuestDBQueryResult> {
    const { table, timestamp, data, designatedTimestamp } = options;

    try {
      // Validate timestamp
      const formattedTimestamp = this.formatTimestamp(timestamp);

      // Build column names and values
      const columns = Object.keys(data);
      const values = Object.values(data);

      // Add timestamp column
      const timestampColumn = designatedTimestamp || 'timestamp';
      columns.push(timestampColumn);
      values.push(formattedTimestamp);

      // Build parameterized query
      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
      const columnNames = columns.join(', ');

      const query = `INSERT INTO ${table} (${columnNames}) VALUES (${placeholders})`;

      return await this.executeQuery(query, values);
    } catch (error) {
      throw new QuestDBError('INVALID_TIME_SERIES_DATA', {
        message: `Failed to insert time-series data: ${this.errorUtils.getErrorMessage(error)}`,
        cause: error,
      });
    }
  }

  /**
   * Bulk insert time-series data for high-throughput scenarios
   * @param table Table name
   * @param records Array of records to insert
   * @param timestampColumn Name of the timestamp column (default: 'timestamp')
   */
  async bulkInsertTimeSeriesData<T extends Record<string, unknown>>(
    table: string,
    records: T[],
    timestampColumn: string = 'timestamp',
  ): Promise<QuestDBQueryResult> {
    if (!records.length) {
      throw new QuestDBError('INVALID_TIME_SERIES_DATA', {
        message: 'No records provided for bulk insert',
      });
    }

    try {
      // Get column names from first record
      const firstRecord = records[0];
      const columns = Object.keys(firstRecord);

      if (!columns.includes(timestampColumn)) {
        throw new QuestDBError('TIMESTAMP_FORMAT_INVALID', {
          message: `Timestamp column '${timestampColumn}' not found in records`,
        });
      }

      // Build VALUES clause for bulk insert
      const valuesClauses: string[] = [];
      const allValues: unknown[] = [];
      let paramIndex = 1;

      for (const record of records) {
        const recordValues = columns.map((col) => {
          const value = record[col];
          // Format timestamp if it's the timestamp column
          return col === timestampColumn ? this.formatTimestamp(value as string | Date) : value;
        });

        const placeholders = recordValues.map(() => `$${paramIndex++}`).join(', ');
        valuesClauses.push(`(${placeholders})`);
        allValues.push(...recordValues);
      }

      const query = `
        INSERT INTO ${table} (${columns.join(', ')})
        VALUES ${valuesClauses.join(', ')}
      `;

      const result = await this.executeQuery(query, allValues);

      this.logger.debug(`Bulk inserted ${records.length} records into ${table}`, {
        recordCount: records.length,
        executionTime: result.executionTimeMs,
      });

      return result;
    } catch (error) {
      throw new QuestDBError('BULK_INSERT_FAILED', {
        message: `Bulk insert failed for ${records.length} records: ${this.errorUtils.getErrorMessage(error)}`,
        cause: error,
      });
    }
  }

  /**
   * Query time-series data with range and filtering options
   * @param options Time-series query options
   */
  async queryTimeSeriesData<T = Record<string, unknown>>(
    options: TimeSeriesQueryOptions,
  ): Promise<QuestDBQueryResult<T>> {
    const { table, startTime, endTime, columns, orderBy, limit } = options;

    try {
      // Format timestamps
      const formattedStartTime = this.formatTimestamp(startTime);
      const formattedEndTime = this.formatTimestamp(endTime);

      // Build SELECT clause
      const selectClause = columns ? columns.join(', ') : '*';

      // Build query
      let query = `
        SELECT ${selectClause}
        FROM ${table}
        WHERE timestamp >= $1 AND timestamp <= $2
      `;

      const params = [formattedStartTime, formattedEndTime];

      // Add ordering
      if (orderBy) {
        query += ` ORDER BY timestamp ${orderBy}`;
      }

      // Add limit
      if (limit) {
        query += ` LIMIT $${params.length + 1}`;
        params.push(limit.toString());
      }

      return await this.executeQuery<T>(query, params);
    } catch (error) {
      throw createQuestDBQueryError(`Time-series query on ${table}`, error);
    }
  }

  /**
   * Insert OHLCV (Open, High, Low, Close, Volume) market data
   * @param data OHLCV data to insert
   * @param table Table name (default: 'market_data')
   */
  async insertOHLCVData(data: OHLCVData, table: string = 'market_data'): Promise<QuestDBQueryResult> {
    const { symbol, timestamp, open, high, low, close, volume } = data;

    try {
      const formattedTimestamp = this.formatTimestamp(timestamp);

      const query = `
        INSERT INTO ${table} (symbol, timestamp, open, high, low, close, volume)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `;

      const params = [symbol, formattedTimestamp, open, high, low, close, volume];

      return await this.executeQuery(query, params);
    } catch (error) {
      throw new QuestDBError('INVALID_TIME_SERIES_DATA', {
        message: `Failed to insert OHLCV data: ${this.errorUtils.getErrorMessage(error)}`,
        cause: error,
      });
    }
  }

  /**
   * Bulk insert OHLCV data for multiple records
   * @param records Array of OHLCV data records
   * @param table Table name (default: 'market_data')
   */
  async bulkInsertOHLCVData(records: OHLCVData[], table: string = 'market_data'): Promise<QuestDBQueryResult> {
    if (!records.length) {
      throw new QuestDBError('INVALID_TIME_SERIES_DATA', {
        message: 'No OHLCV records provided for bulk insert',
      });
    }

    try {
      const valuesClauses: string[] = [];
      const allValues: unknown[] = [];
      let paramIndex = 1;

      for (const record of records) {
        const { symbol, timestamp, open, high, low, close, volume } = record;
        const formattedTimestamp = this.formatTimestamp(timestamp);

        const placeholders = Array.from({ length: 7 }, () => `$${paramIndex++}`).join(', ');
        valuesClauses.push(`(${placeholders})`);
        allValues.push(symbol, formattedTimestamp, open, high, low, close, volume);
      }

      const query = `
        INSERT INTO ${table} (symbol, timestamp, open, high, low, close, volume)
        VALUES ${valuesClauses.join(', ')}
      `;

      const result = await this.executeQuery(query, allValues);

      this.logger.debug(`Bulk inserted ${records.length} OHLCV records into ${table}`, {
        recordCount: records.length,
        executionTime: result.executionTimeMs,
      });

      return result;
    } catch (error) {
      throw new QuestDBError('BULK_INSERT_FAILED', {
        message: `Bulk OHLCV insert failed for ${records.length} records: ${this.errorUtils.getErrorMessage(error)}`,
        cause: error,
      });
    }
  }

  /**
   * Get latest market data for symbols
   * @param symbols Array of symbol names
   * @param table Table name (default: 'market_data')
   */
  async getLatestMarketData<T = OHLCVData>(
    symbols: string[],
    table: string = 'market_data',
  ): Promise<QuestDBQueryResult<T>> {
    if (!symbols.length) {
      throw new QuestDBError('INVALID_TIME_SERIES_DATA', {
        message: 'No symbols provided for latest market data query',
      });
    }

    try {
      const placeholders = symbols.map((_, index) => `$${index + 1}`).join(', ');

      const query = `
        SELECT symbol, timestamp, open, high, low, close, volume
        FROM ${table}
        WHERE symbol IN (${placeholders})
        LATEST ON timestamp PARTITION BY symbol
      `;

      return await this.executeQuery<T>(query, symbols);
    } catch (error) {
      throw createQuestDBQueryError(`Latest market data query for symbols: ${symbols.join(', ')}`, error);
    }
  }

  /**
   * Format timestamp for QuestDB
   * Ensures timestamps are in the correct format for QuestDB time-series operations
   */
  private formatTimestamp(timestamp: string | Date): string {
    if (timestamp instanceof Date) {
      return timestamp.toISOString();
    }

    if (typeof timestamp === 'string') {
      // Try to parse and reformat to ensure valid ISO format
      const date = this.datetimeUtils.getNewDate(timestamp);
      if (isNaN(date.getTime())) {
        throw new QuestDBError('TIMESTAMP_FORMAT_INVALID', {
          message: `Invalid timestamp format: ${timestamp}`,
        });
      }
      return date.toISOString();
    }

    throw new QuestDBError('TIMESTAMP_FORMAT_INVALID', {
      message: `Timestamp must be a Date object or ISO string, got: ${typeof timestamp}`,
    });
  }

  /**
   * Connect to QuestDB with retry logic
   */
  private async connectWithRetry(): Promise<void> {
    while (this.connectionAttempts < this.maxRetryAttempts) {
      try {
        await this.connect();
        this.connectionAttempts = 0; // Reset on successful connection
        return;
      } catch (error) {
        this.connectionAttempts++;
        this.logger.error(
          `Connection attempt ${this.connectionAttempts}/${this.maxRetryAttempts} failed: ${this.errorUtils.getErrorMessage(error)}`,
          { error: this.errorUtils.getErrorStack(error) },
        );

        if (this.connectionAttempts >= this.maxRetryAttempts) {
          throw createQuestDBConnectionError(error, 'Max connection attempts exceeded');
        }

        // Exponential backoff
        const delay = this.retryDelay * Math.pow(2, this.connectionAttempts - 1);
        this.logger.log(`Retrying connection in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      return; // Already started
    }

    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck().catch((error) => {
        this.logger.error(`Health check error: ${this.errorUtils.getErrorMessage(error)}`);
      });
    }, 30000); // Every 30 seconds

    this.logger.debug('Health check monitoring started');
  }

  /**
   * Establish connection to QuestDB
   */
  private async connect(): Promise<void> {
    try {
      // Get connection configuration from environment
      const config = this.getConnectionConfig();

      // Validate configuration
      const validatedConfig = QuestDBConnectionConfigSchema.parse(config);

      this.logger.log('Establishing QuestDB connection...', {
        host: validatedConfig.host,
        port: validatedConfig.port,
        database: validatedConfig.database,
        username: validatedConfig.username,
      });

      // Create connection pool
      this.connection = new Pool({
        host: validatedConfig.host,
        port: validatedConfig.port,
        database: validatedConfig.database,
        user: validatedConfig.username,
        password: validatedConfig.password,
        ssl: validatedConfig.ssl,
        max: validatedConfig.maxConnections,
        connectionTimeoutMillis: validatedConfig.connectionTimeoutMillis,
        idleTimeoutMillis: validatedConfig.idleTimeoutMillis,
      });

      // Test the connection
      await this.testConnection();

      this.isConnected = true;
      this.healthCheckService.setQuestdbStatus(true, this.getConnectionStatsForHealthCheck());
      this.logger.log('QuestDB connection established successfully');
    } catch (error) {
      this.isConnected = false;
      this.healthCheckService.setQuestdbStatus(false);

      if (error instanceof z.ZodError) {
        throw createQuestDBConfigError('connection configuration', error);
      }

      throw createQuestDBConnectionError(error);
    }
  }

  /**
   * Get connection configuration from environment variables
   */
  private getConnectionConfig(): QuestDBConnectionConfig {
    return createQuestDBConfig({
      QUESTDB_HOST: this.envService.get('QUESTDB_HOST'),
      QUESTDB_PORT: this.envService.get('QUESTDB_PORT'),
      QUESTDB_DATABASE: this.envService.get('QUESTDB_DATABASE'),
      QUESTDB_USERNAME: this.envService.get('QUESTDB_USERNAME'),
      QUESTDB_PASSWORD: this.envService.get('QUESTDB_PASSWORD'),
      QUESTDB_SSL: this.envService.get('QUESTDB_SSL'),
      QUESTDB_MAX_CONNECTIONS: this.envService.get('QUESTDB_MAX_CONNECTIONS'),
    });
  }

  /**
   * Test the database connection
   */
  private async testConnection(): Promise<boolean> {
    if (!this.connection) {
      return false;
    }

    try {
      const client = await this.connection.connect();
      await client.query('SELECT 1 as connection_test');
      client.release();
      return true;
    } catch (error) {
      this.logger.error(`Connection test failed: ${this.errorUtils.getErrorMessage(error)}`);
      return false;
    }
  }

  /**
   * Disconnect from QuestDB
   */
  private async disconnect(): Promise<void> {
    try {
      // Clear intervals
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      if (this.reconnectionTimeout) {
        clearTimeout(this.reconnectionTimeout);
        this.reconnectionTimeout = null;
      }

      // Close connection pool
      if (this.connection) {
        await this.connection.end();
        this.connection = null;
      }

      this.isConnected = false;
      this.healthCheckService.setQuestdbStatus(false);
      this.logger.log('QuestDB connection closed successfully');
    } catch (error) {
      this.logger.error(`Error during disconnect: ${this.errorUtils.getErrorMessage(error)}`);
    }
  }

  /**
   * Perform health check
   */
  private async performHealthCheck(): Promise<void> {
    try {
      this.lastHealthCheck = this.datetimeUtils.getNewDate();
      this.healthCheckCount++;

      const isHealthy = await this.testConnection();

      if (!isHealthy && this.isConnected) {
        this.logger.warn('Health check failed, marking connection as unhealthy');
        this.handleConnectionError(new Error('Health check failed'));
      } else if (isHealthy && !this.isConnected) {
        this.logger.log('Health check passed, connection restored');
        this.isConnected = true;
        this.failedHealthChecks = 0;
      }

      if (isHealthy) {
        this.failedHealthChecks = 0;
        this.healthCheckService.setQuestdbStatus(true, this.getConnectionStatsForHealthCheck());
      } else {
        this.failedHealthChecks++;
        this.healthCheckService.setQuestdbStatus(false);
      }
    } catch (error) {
      this.logger.error(`Health check error: ${this.errorUtils.getErrorMessage(error)}`);
      this.handleConnectionError(this.errorUtils.toError(error));
    }
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(error: Error): void {
    this.logger.error(`Connection error detected: ${this.errorUtils.getErrorMessage(error)}`, {
      error: this.errorUtils.getErrorStack(error),
    });

    this.isConnected = false;
    this.failedHealthChecks++;
    this.healthCheckService.setQuestdbStatus(false);

    // Schedule reconnection if not already scheduled
    this.scheduleReconnection();
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnection(): void {
    if (this.reconnectionTimeout) {
      return; // Already scheduled
    }

    this.reconnectionTimeout = setTimeout(() => {
      this.performReconnection()
        .catch((error) => {
          this.logger.error(`Reconnection failed: ${this.errorUtils.getErrorMessage(error)}`);
        })
        .finally(() => {
          this.reconnectionTimeout = null;
        });
    }, this.retryDelay);
  }

  /**
   * Perform reconnection
   */
  private async performReconnection(): Promise<void> {
    this.logger.log('Attempting to reconnect to QuestDB...');

    try {
      await this.connectWithRetry();
      this.logger.log('Reconnection successful');
    } catch (error) {
      this.logger.error(`Reconnection failed: ${this.errorUtils.getErrorMessage(error)}`);
      // Schedule another reconnection attempt
      this.scheduleReconnection();
    }
  }

  /**
   * Get connection statistics formatted for health check service
   */
  private getConnectionStatsForHealthCheck() {
    return {
      totalConnections: this.connection?.totalCount || 0,
      idleConnections: this.connection?.idleCount || 0,
      waitingCount: this.connection?.waitingCount || 0,
      healthCheckCount: this.healthCheckCount,
      failedHealthChecks: this.failedHealthChecks,
    };
  }
}
