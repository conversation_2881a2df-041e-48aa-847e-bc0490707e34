import { Injectable, Logger, OnM<PERSON>uleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { Worker, Job, WorkerOptions } from 'bullmq';
import { Redis } from 'ioredis';
import { DateTimeUtilsService } from '@app/utils';
import { QueueError, type WorkerHealthStatus, type JobResult } from './queue.error';
import { 
  type QueueConnectionType, 
  type WorkerOptionsType,
  DEFAULT_WORKER_OPTIONS 
} from './queue.config';

/**
 * Abstract base class for queue workers
 * Provides standardized error handling, job progress tracking, and graceful shutdown
 */
@Injectable()
export abstract class BaseWorkerService<T extends Record<string, unknown> = Record<string, unknown>> 
  implements OnModuleInit, OnModuleDestroy {
  
  protected readonly logger = new Logger(this.constructor.name);
  protected worker!: Worker<T>;
  protected connection!: Redis;
  protected isRunning = false;
  protected lastProcessedAt?: string;
  protected lastError?: string;
  protected lastErrorAt?: string;
  
  constructor(
    protected readonly queueName: string,
    protected readonly connectionConfig: QueueConnectionType,
    protected readonly workerOptions: WorkerOptionsType = DEFAULT_WORKER_OPTIONS,
    protected readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  /**
   * Initialize worker on module init
   */
  async onModuleInit(): Promise<void> {
    await this.startWorker();
  }

  /**
   * Start the worker
   */
  async startWorker(): Promise<void> {
    try {
      if (this.isRunning) {
        this.logger.warn(`Worker for queue '${this.queueName}' is already running`);
        return;
      }

      // Create Redis connection for worker
      this.connection = new Redis({
        host: this.connectionConfig.host,
        port: this.connectionConfig.port,
        username: this.connectionConfig.username,
        password: this.connectionConfig.password,
        db: this.connectionConfig.db,
        maxRetriesPerRequest: this.connectionConfig.maxRetriesPerRequest,
        enableReadyCheck: this.connectionConfig.enableReadyCheck,
        lazyConnect: this.connectionConfig.lazyConnect,
        keepAlive: this.connectionConfig.keepAlive,
        family: this.connectionConfig.family,
        keyPrefix: this.connectionConfig.keyPrefix,
      });

      // Create BullMQ worker
      this.worker = new Worker<T>(
        this.queueName,
        this.processJobWrapper.bind(this),
        {
          connection: this.connection,
          concurrency: this.workerOptions.concurrency,
          limiter: this.workerOptions.limiter,
        }
      );

      // Set up event listeners
      this.setupEventListeners();

      this.isRunning = true;
      this.logger.log(`Worker for queue '${this.queueName}' started successfully`);
    } catch (error) {
      this.logger.error(`Failed to start worker for queue '${this.queueName}':`, error);
      throw new QueueError('WORKER_START_FAILED', {
        message: `Failed to start worker for queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName },
      });
    }
  }

  /**
   * Stop the worker
   */
  async stopWorker(): Promise<void> {
    try {
      if (!this.isRunning) {
        this.logger.warn(`Worker for queue '${this.queueName}' is not running`);
        return;
      }

      await this.worker.close();
      await this.connection.disconnect();
      
      this.isRunning = false;
      this.logger.log(`Worker for queue '${this.queueName}' stopped successfully`);
    } catch (error) {
      this.logger.error(`Failed to stop worker for queue '${this.queueName}':`, error);
      throw new QueueError('WORKER_STOP_FAILED', {
        message: `Failed to stop worker for queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName },
      });
    }
  }

  /**
   * Abstract method to be implemented by concrete worker classes
   * This is where the actual job processing logic goes
   */
  protected abstract processJob(job: Job<T>): Promise<JobResult<unknown>>;

  /**
   * Wrapper for job processing with error handling and logging
   */
  private async processJobWrapper(job: Job<T>): Promise<JobResult<unknown>> {
    const startTime = Date.now();
    const jobId = job.id || 'unknown';
    
    try {
      this.logger.log(`Processing job '${job.name}' with ID: ${jobId}`);
      
      // Update job progress to indicate processing started
      await job.updateProgress(0);
      
      // Process the job
      const result = await this.processJob(job);
      
      // Update job progress to indicate completion
      await job.updateProgress(100);
      
      const duration = Date.now() - startTime;
      this.lastProcessedAt = this.dateTimeUtils.getUtcNow();
      
      this.logger.log(`Job '${job.name}' (ID: ${jobId}) completed successfully in ${duration}ms`);
      
      return {
        ...result,
        success: true,
        jobId,
        queueName: this.queueName,
        processedAt: this.lastProcessedAt,
        duration,
        attempts: job.attemptsMade,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.lastError = error instanceof Error ? error.message : 'Unknown error';
      this.lastErrorAt = this.dateTimeUtils.getUtcNow();
      
      this.logger.error(`Job '${job.name}' (ID: ${jobId}) failed after ${duration}ms:`, error);
      
      const jobResult: JobResult<unknown> = {
        success: false,
        error: error instanceof QueueError ? error : new QueueError('WORKER_PROCESS_FAILED', {
          message: `Job processing failed: ${this.lastError}`,
          cause: error,
          context: { queueName: this.queueName, jobId, jobName: job.name },
        }),
        jobId,
        queueName: this.queueName,
        processedAt: this.lastErrorAt,
        duration,
        attempts: job.attemptsMade,
      };
      
      // Re-throw the error so BullMQ can handle retries
      throw jobResult.error;
    }
  }

  /**
   * Set up event listeners for the worker
   */
  private setupEventListeners(): void {
    this.worker.on('completed', (job: Job<T>, result: JobResult<unknown>) => {
      this.logger.log(`Job '${job.name}' (ID: ${job.id}) completed successfully`);
      this.onJobCompleted(job, result);
    });

    this.worker.on('failed', (job: Job<T> | undefined, error: Error) => {
      const jobId = job?.id || 'unknown';
      const jobName = job?.name || 'unknown';
      this.logger.error(`Job '${jobName}' (ID: ${jobId}) failed:`, error);
      this.onJobFailed(job, error);
    });

    this.worker.on('active', (job: Job<T>) => {
      this.logger.debug(`Job '${job.name}' (ID: ${job.id}) started processing`);
      this.onJobActive(job);
    });

    this.worker.on('stalled', (jobId: string) => {
      this.logger.warn(`Job with ID: ${jobId} stalled`);
      this.onJobStalled(jobId);
    });

    this.worker.on('progress', (job: Job<T>, progress: unknown) => {
      this.logger.debug(`Job '${job.name}' (ID: ${job.id}) progress: ${JSON.stringify(progress)}`);
      this.onJobProgress(job, progress as number | object | string);
    });

    this.worker.on('error', (error: Error) => {
      this.logger.error(`Worker error for queue '${this.queueName}':`, error);
      this.lastError = error.message;
      this.lastErrorAt = this.dateTimeUtils.getUtcNow();
      this.onWorkerError(error);
    });
  }

  /**
   * Hook methods for subclasses to override
   */
  protected onJobCompleted(job: Job<T>, result: JobResult<unknown>): void {
    // Override in subclasses if needed
  }

  protected onJobFailed(job: Job<T> | undefined, error: Error): void {
    // Override in subclasses if needed
  }

  protected onJobActive(job: Job<T>): void {
    // Override in subclasses if needed
  }

  protected onJobStalled(jobId: string): void {
    // Override in subclasses if needed
  }

  protected onJobProgress(job: Job<T>, progress: number | object | string): void {
    // Override in subclasses if needed
  }

  protected onWorkerError(error: Error): void {
    // Override in subclasses if needed
  }

  /**
   * Get worker health status
   */
  async getHealthStatus(): Promise<WorkerHealthStatus> {
    try {
      const processing = this.worker ? await this.worker.getNextJob('') : 0;
      
      return {
        name: this.constructor.name,
        queueName: this.queueName,
        isRunning: this.isRunning,
        concurrency: this.workerOptions.concurrency,
        processing: typeof processing === 'number' ? processing : 0,
        lastProcessedAt: this.lastProcessedAt,
        lastError: this.lastError,
        lastErrorAt: this.lastErrorAt,
      };
    } catch (error) {
      return {
        name: this.constructor.name,
        queueName: this.queueName,
        isRunning: false,
        concurrency: this.workerOptions.concurrency,
        processing: 0,
        lastError: error instanceof Error ? error.message : 'Unknown error',
        lastErrorAt: this.dateTimeUtils.getUtcNow(),
      };
    }
  }

  /**
   * Get the worker instance (for advanced operations)
   */
  getWorker(): Worker<T> {
    return this.worker;
  }

  /**
   * Cleanup resources on module destroy
   */
  async onModuleDestroy(): Promise<void> {
    await this.stopWorker();
  }
}
