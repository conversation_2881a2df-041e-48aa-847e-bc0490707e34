import { z } from 'zod/v4';
import { BaseError } from '@app/common/errors';

/**
 * Queue-specific error types
 */
export const QueueErrorEnum = z.enum([
  'QUEUE_CONNECTION_FAILED',
  'QUEUE_CREATION_FAILED',
  'JOB_ADD_FAILED',
  'JOB_REMOVE_FAILED',
  'JOB_GET_FAILED',
  'JOB_UPDATE_FAILED',
  'WORKER_START_FAILED',
  'WORKER_STOP_FAILED',
  'WORKER_PROCESS_FAILED',
  'QUEUE_PAUSE_FAILED',
  'QUEUE_RESUME_FAILED',
  'QUEUE_DRAIN_FAILED',
  'QUEUE_CLEAN_FAILED',
  'INVALID_JOB_DATA',
  'INVALID_QUEUE_OPTIONS',
  'INVALID_WORKER_OPTIONS',
  'QUEUE_NOT_FOUND',
  'WORKER_NOT_FOUND',
  'JOB_NOT_FOUND',
  'QUEUE_HEALTH_CHECK_FAILED',
  'DASHBOARD_SETUP_FAILED',
  'REDIS_CONNECTION_FAILED',
]);

/**
 * Error messages for queue operations
 */
export const QueueErrorMessages: Record<QueueErrorEnumType, string> = {
  QUEUE_CONNECTION_FAILED: 'Failed to connect to queue backend',
  QUEUE_CREATION_FAILED: 'Failed to create queue instance',
  JOB_ADD_FAILED: 'Failed to add job to queue',
  JOB_REMOVE_FAILED: 'Failed to remove job from queue',
  JOB_GET_FAILED: 'Failed to retrieve job from queue',
  JOB_UPDATE_FAILED: 'Failed to update job in queue',
  WORKER_START_FAILED: 'Failed to start queue worker',
  WORKER_STOP_FAILED: 'Failed to stop queue worker',
  WORKER_PROCESS_FAILED: 'Failed to process job in worker',
  QUEUE_PAUSE_FAILED: 'Failed to pause queue',
  QUEUE_RESUME_FAILED: 'Failed to resume queue',
  QUEUE_DRAIN_FAILED: 'Failed to drain queue',
  QUEUE_CLEAN_FAILED: 'Failed to clean queue',
  INVALID_JOB_DATA: 'Invalid job data provided',
  INVALID_QUEUE_OPTIONS: 'Invalid queue options provided',
  INVALID_WORKER_OPTIONS: 'Invalid worker options provided',
  QUEUE_NOT_FOUND: 'Queue not found',
  WORKER_NOT_FOUND: 'Worker not found',
  JOB_NOT_FOUND: 'Job not found',
  QUEUE_HEALTH_CHECK_FAILED: 'Queue health check failed',
  DASHBOARD_SETUP_FAILED: 'Failed to setup queue dashboard',
  REDIS_CONNECTION_FAILED: 'Failed to connect to Redis for queue operations',
};

/**
 * Type definition for queue error enum
 */
export type QueueErrorEnumType = z.output<typeof QueueErrorEnum>;

/**
 * Queue-specific error class
 * Extends BaseError with queue domain-specific error handling
 */
export class QueueError extends BaseError<QueueErrorEnumType> {
  constructor(name: QueueErrorEnumType, details?: { message?: string; cause?: unknown; context?: Record<string, unknown> }) {
    super({
      name,
      domain: 'QUEUE' as const,
      message: details?.message || QueueErrorMessages[name],
      cause: details?.cause,
    });
    
    // Add queue-specific context if provided
    if (details?.context) {
      Object.assign(this, { context: details.context });
    }
    
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}

/**
 * Job processing result interface
 */
export interface JobResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: QueueError;
  jobId: string;
  queueName: string;
  processedAt: string;
  duration?: number;
  attempts?: number;
}

/**
 * Queue health status interface
 */
export interface QueueHealthStatus {
  name: string;
  isHealthy: boolean;
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
  lastError?: string;
  lastErrorAt?: string;
}

/**
 * Worker health status interface
 */
export interface WorkerHealthStatus {
  name: string;
  queueName: string;
  isRunning: boolean;
  concurrency: number;
  processing: number;
  lastProcessedAt?: string;
  lastError?: string;
  lastErrorAt?: string;
}
