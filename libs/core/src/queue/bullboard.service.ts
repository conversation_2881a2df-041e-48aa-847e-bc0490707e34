import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import type { Application, Request, Response, NextFunction } from 'express';
import { QueueService } from './queue.service';
import { EnvService } from '../env/env.service';
import { QueueError } from './queue.error';

/**
 * BullBoard dashboard service
 * Provides web UI for queue monitoring and management
 */
@Injectable()
export class BullBoardService implements OnModuleInit {
  private readonly logger = new Logger(BullBoardService.name);
  private serverAdapter: ExpressAdapter;
  private isInitialized = false;

  constructor(
    private readonly queueService: QueueService,
    private readonly envService: EnvService,
  ) {
    this.serverAdapter = new ExpressAdapter();
  }

  /**
   * Initialize BullBoard on module init
   */
  async onModuleInit(): Promise<void> {
    await this.initializeBullBoard();
  }

  /**
   * Initialize BullBoard with all registered queues
   */
  private async initializeBullBoard(): Promise<void> {
    try {
      // Set base path for the dashboard
      this.serverAdapter.setBasePath('/admin/queues');

      // Get all registered queues and create adapters
      const queues = this.queueService.getAllQueues();
      const queueAdapters = queues.map(queue => new BullMQAdapter(queue));

      // Create BullBoard instance
      createBullBoard({
        queues: queueAdapters,
        serverAdapter: this.serverAdapter,
        options: {
          uiConfig: {
            boardTitle: 'PatternTrade API - Queue Dashboard',
            boardLogo: {
              path: '/logo.png',
              width: '100px',
              height: 'auto',
            },
            miscLinks: [
              { text: 'API Documentation', url: '/docs' },
              { text: 'Health Check', url: '/health' },
            ],
            favIcon: {
              default: '/favicon.ico',
              alternative: '/favicon-32x32.png',
            },
          },
        },
      });

      this.isInitialized = true;
      this.logger.log('BullBoard dashboard initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize BullBoard dashboard:', error);
      throw new QueueError('DASHBOARD_SETUP_FAILED', {
        message: 'Failed to initialize BullBoard dashboard',
        cause: error,
      });
    }
  }

  /**
   * Add a new queue to the dashboard
   */
  addQueue(queueName: string): void {
    try {
      if (!this.isInitialized) {
        this.logger.warn('BullBoard not initialized, cannot add queue');
        return;
      }

      const queue = this.queueService.getQueue(queueName);
      const adapter = new BullMQAdapter(queue);
      
      // Add the queue adapter to BullBoard
      createBullBoard({
        queues: [adapter],
        serverAdapter: this.serverAdapter,
      });

      this.logger.log(`Queue '${queueName}' added to BullBoard dashboard`);
    } catch (error) {
      this.logger.error(`Failed to add queue '${queueName}' to dashboard:`, error);
      throw new QueueError('DASHBOARD_SETUP_FAILED', {
        message: `Failed to add queue '${queueName}' to dashboard`,
        cause: error,
        context: { queueName },
      });
    }
  }

  /**
   * Setup BullBoard middleware with authentication
   */
  setupMiddleware(app: Application): void {
    try {
      // Authentication middleware
      const authMiddleware = this.createAuthMiddleware();
      
      // Apply authentication middleware to the dashboard routes
      app.use('/admin/queues', authMiddleware);
      
      // Mount BullBoard router
      app.use('/admin/queues', this.serverAdapter.getRouter());

      this.logger.log('BullBoard middleware setup completed');
    } catch (error) {
      this.logger.error('Failed to setup BullBoard middleware:', error);
      throw new QueueError('DASHBOARD_SETUP_FAILED', {
        message: 'Failed to setup BullBoard middleware',
        cause: error,
      });
    }
  }

  /**
   * Create authentication middleware for the dashboard
   */
  private createAuthMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      // Check if user is authenticated
      // This is a basic implementation - in production, integrate with your auth system
      const isAuthenticated = this.checkAuthentication(req);
      
      if (!isAuthenticated) {
        // Return 401 Unauthorized
        res.status(401).json({
          success: false,
          error: 'Authentication required to access queue dashboard',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Check if user has admin privileges
      const hasAdminAccess = this.checkAdminAccess(req);
      
      if (!hasAdminAccess) {
        // Return 403 Forbidden
        res.status(403).json({
          success: false,
          error: 'Admin access required to access queue dashboard',
          statusCode: 403,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      next();
    };
  }

  /**
   * Check if the request is authenticated
   * TODO: Integrate with your authentication system
   */
  private checkAuthentication(req: Request): boolean {
    // Basic implementation - check for session or JWT token
    const session = req.session as any;
    const authHeader = req.headers.authorization;
    
    // Check session-based authentication
    if (session?.user?.id) {
      return true;
    }
    
    // Check JWT token authentication
    if (authHeader?.startsWith('Bearer ')) {
      // TODO: Validate JWT token
      return true;
    }
    
    // For development, allow access if NODE_ENV is local
    if (this.envService.get('NODE_ENV') === 'local') {
      return true;
    }
    
    return false;
  }

  /**
   * Check if the authenticated user has admin access
   * TODO: Integrate with your authorization system
   */
  private checkAdminAccess(req: Request): boolean {
    // Basic implementation - check user role
    const session = req.session as any;
    
    // Check if user has admin role
    if (session?.user?.role === 'admin' || session?.user?.isAdmin) {
      return true;
    }
    
    // For development, allow access if NODE_ENV is local
    if (this.envService.get('NODE_ENV') === 'local') {
      return true;
    }
    
    return false;
  }

  /**
   * Get the server adapter instance
   */
  getServerAdapter(): ExpressAdapter {
    return this.serverAdapter;
  }

  /**
   * Check if BullBoard is initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Get dashboard URL
   */
  getDashboardUrl(): string {
    const port = this.envService.get('PORT');
    const host = this.envService.get('NODE_ENV') === 'local' ? 'localhost' : 'your-domain.com';
    return `http://${host}:${port}/admin/queues`;
  }

  /**
   * Refresh dashboard with current queues
   */
  async refreshDashboard(): Promise<void> {
    try {
      // Re-initialize with current queues
      await this.initializeBullBoard();
      this.logger.log('BullBoard dashboard refreshed successfully');
    } catch (error) {
      this.logger.error('Failed to refresh BullBoard dashboard:', error);
      throw new QueueError('DASHBOARD_SETUP_FAILED', {
        message: 'Failed to refresh BullBoard dashboard',
        cause: error,
      });
    }
  }
}
