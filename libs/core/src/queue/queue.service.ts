import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { Queue } from 'bullmq';
import { EnvService } from '../env/env.service';
import { DateTimeUtilsService } from '@app/utils';
import { QueueError, type QueueHealthStatus } from './queue.error';
import { 
  createQueueConnection,
  DEFAULT_QUEUE_OPTIONS,
  type QueueConnectionType,
  type QueueOptionsType,
  type QueueNameType,
  QueueNameEnum
} from './queue.config';

/**
 * Central queue management service
 * Manages queue instances and provides queue registry functionality
 */
@Injectable()
export class QueueService implements OnModuleDestroy {
  private readonly logger = new Logger(QueueService.name);
  private readonly queues = new Map<string, Queue>();
  private readonly connectionConfig: QueueConnectionType;

  constructor(
    private readonly envService: EnvService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    this.connectionConfig = createQueueConnection(this.envService);
  }

  /**
   * Create or get a queue instance
   */
  getQueue<T extends Record<string, unknown> = Record<string, unknown>>(
    queueName: QueueNameType | string,
    options: QueueOptionsType = DEFAULT_QUEUE_OPTIONS
  ): Queue<T> {
    try {
      // Validate queue name if it's a known queue type
      if (Object.values(QueueNameEnum.enum).includes(queueName as QueueNameType)) {
        QueueNameEnum.parse(queueName);
      }

      // Return existing queue if already created
      if (this.queues.has(queueName)) {
        return this.queues.get(queueName) as Queue<T>;
      }

      // Create new queue instance
      const queue = new Queue<T>(queueName, {
        connection: {
          host: this.connectionConfig.host,
          port: this.connectionConfig.port,
          username: this.connectionConfig.username,
          password: this.connectionConfig.password,
          db: this.connectionConfig.db,
          maxRetriesPerRequest: this.connectionConfig.maxRetriesPerRequest,
          retryDelayOnFailover: this.connectionConfig.retryDelayOnFailover,
          enableReadyCheck: this.connectionConfig.enableReadyCheck,
          lazyConnect: this.connectionConfig.lazyConnect,
          keepAlive: this.connectionConfig.keepAlive,
          family: this.connectionConfig.family,
          keyPrefix: this.connectionConfig.keyPrefix,
        },
        defaultJobOptions: options.defaultJobOptions,
      });

      // Store queue instance
      this.queues.set(queueName, queue);
      
      this.logger.log(`Queue '${queueName}' created and registered`);
      return queue;
    } catch (error) {
      this.logger.error(`Failed to create queue '${queueName}':`, error);
      throw new QueueError('QUEUE_CREATION_FAILED', {
        message: `Failed to create queue '${queueName}'`,
        cause: error,
        context: { queueName },
      });
    }
  }

  /**
   * Get all registered queue names
   */
  getRegisteredQueueNames(): string[] {
    return Array.from(this.queues.keys());
  }

  /**
   * Get all registered queue instances
   */
  getAllQueues(): Queue[] {
    return Array.from(this.queues.values());
  }

  /**
   * Check if a queue exists
   */
  hasQueue(queueName: string): boolean {
    return this.queues.has(queueName);
  }

  /**
   * Remove a queue from registry
   */
  async removeQueue(queueName: string): Promise<void> {
    try {
      const queue = this.queues.get(queueName);
      if (queue) {
        await queue.close();
        this.queues.delete(queueName);
        this.logger.log(`Queue '${queueName}' removed from registry`);
      }
    } catch (error) {
      this.logger.error(`Failed to remove queue '${queueName}':`, error);
      throw new QueueError('QUEUE_CREATION_FAILED', {
        message: `Failed to remove queue '${queueName}'`,
        cause: error,
        context: { queueName },
      });
    }
  }

  /**
   * Get health status for all registered queues
   */
  async getAllQueuesHealthStatus(): Promise<QueueHealthStatus[]> {
    const healthStatuses: QueueHealthStatus[] = [];

    for (const [queueName, queue] of this.queues.entries()) {
      try {
        const [waiting, active, completed, failed, delayed] = await Promise.all([
          queue.getWaiting(),
          queue.getActive(),
          queue.getCompleted(),
          queue.getFailed(),
          queue.getDelayed(),
        ]);

        const isPaused = await queue.isPaused();

        healthStatuses.push({
          name: queueName,
          isHealthy: true,
          waiting: waiting.length,
          active: active.length,
          completed: completed.length,
          failed: failed.length,
          delayed: delayed.length,
          paused: isPaused,
        });
      } catch (error) {
        healthStatuses.push({
          name: queueName,
          isHealthy: false,
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0,
          paused: false,
          lastError: error instanceof Error ? error.message : 'Unknown error',
          lastErrorAt: this.dateTimeUtils.getUtcNow(),
        });
      }
    }

    return healthStatuses;
  }

  /**
   * Pause all queues
   */
  async pauseAllQueues(): Promise<void> {
    const pausePromises = Array.from(this.queues.values()).map(queue => 
      queue.pause().catch(error => {
        this.logger.error(`Failed to pause queue:`, error);
        return error;
      })
    );

    await Promise.allSettled(pausePromises);
    this.logger.log('All queues pause operation completed');
  }

  /**
   * Resume all queues
   */
  async resumeAllQueues(): Promise<void> {
    const resumePromises = Array.from(this.queues.values()).map(queue => 
      queue.resume().catch(error => {
        this.logger.error(`Failed to resume queue:`, error);
        return error;
      })
    );

    await Promise.allSettled(resumePromises);
    this.logger.log('All queues resume operation completed');
  }

  /**
   * Clean all queues
   */
  async cleanAllQueues(olderThanMs: number = 24 * 60 * 60 * 1000): Promise<void> {
    const cleanPromises = Array.from(this.queues.values()).map(queue => 
      Promise.all([
        queue.clean(olderThanMs, 100, 'completed'),
        queue.clean(olderThanMs, 50, 'failed'),
      ]).catch(error => {
        this.logger.error(`Failed to clean queue:`, error);
        return error;
      })
    );

    await Promise.allSettled(cleanPromises);
    this.logger.log('All queues clean operation completed');
  }

  /**
   * Get connection configuration
   */
  getConnectionConfig(): QueueConnectionType {
    return this.connectionConfig;
  }

  /**
   * Cleanup all queues on module destroy
   */
  async onModuleDestroy(): Promise<void> {
    this.logger.log('Closing all queue connections...');
    
    const closePromises = Array.from(this.queues.values()).map(queue => 
      queue.close().catch(error => {
        this.logger.error('Error closing queue:', error);
        return error;
      })
    );

    await Promise.allSettled(closePromises);
    this.queues.clear();
    
    this.logger.log('All queue connections closed');
  }
}
