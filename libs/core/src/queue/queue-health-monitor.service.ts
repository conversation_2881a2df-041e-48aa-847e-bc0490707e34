import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { QueueHealthService } from './queue-health.service';
import { HealthCheckService } from '../health-check/health-check.service';
import { DateTimeUtilsService } from '@app/utils';

/**
 * Queue health monitoring service
 * Integrates queue health checks with the main health check system
 */
@Injectable()
export class QueueHealthMonitorService implements OnModuleInit {
  private readonly logger = new Logger(QueueHealthMonitorService.name);
  private isMonitoring = false;

  constructor(
    private readonly queueHealthService: QueueHealthService,
    private readonly healthCheckService: HealthCheckService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  /**
   * Initialize monitoring on module init
   */
  async onModuleInit(): Promise<void> {
    await this.startMonitoring();
  }

  /**
   * Start queue health monitoring
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      this.logger.warn('Queue health monitoring is already running');
      return;
    }

    try {
      // Perform initial health check
      await this.performHealthCheck();
      
      this.isMonitoring = true;
      this.logger.log('Queue health monitoring started successfully');
    } catch (error) {
      this.logger.error('Failed to start queue health monitoring:', error);
      throw error;
    }
  }

  /**
   * Stop queue health monitoring
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    this.logger.log('Queue health monitoring stopped');
  }

  /**
   * Perform comprehensive queue health check
   * Runs every 30 seconds
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async performHealthCheck(): Promise<void> {
    if (!this.isMonitoring) {
      return;
    }

    try {
      this.logger.debug('Performing queue health check');

      // Get comprehensive system health status
      const systemHealth = await this.queueHealthService.getSystemHealthStatus();

      // Update main health check service with queue status
      this.healthCheckService.setQueueStatus(systemHealth.isHealthy, {
        summary: systemHealth.summary,
        queues: systemHealth.queues,
        workers: systemHealth.workers,
      });

      // Log health status if there are issues
      if (!systemHealth.isHealthy) {
        this.logger.warn('Queue system health check failed', {
          summary: systemHealth.summary,
          unhealthyQueues: systemHealth.queues.filter(q => !q.isHealthy),
          stoppedWorkers: systemHealth.workers.filter(w => !w.isRunning),
        });
      } else {
        this.logger.debug('Queue system health check passed', {
          summary: systemHealth.summary,
        });
      }
    } catch (error) {
      this.logger.error('Queue health check failed:', error);
      
      // Mark queue system as unhealthy
      this.healthCheckService.setQueueStatus(false, {
        summary: {
          totalQueues: 0,
          healthyQueues: 0,
          totalWorkers: 0,
          runningWorkers: 0,
          totalJobs: {
            waiting: 0,
            active: 0,
            completed: 0,
            failed: 0,
            delayed: 0,
          },
        },
        queues: [],
        workers: [],
      });
    }
  }

  /**
   * Perform detailed queue metrics collection
   * Runs every 5 minutes
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async collectQueueMetrics(): Promise<void> {
    if (!this.isMonitoring) {
      return;
    }

    try {
      this.logger.debug('Collecting queue metrics');

      const [queueMetrics, workerMetrics] = await Promise.all([
        this.queueHealthService.getQueueMetrics(),
        this.queueHealthService.getWorkerMetrics(),
      ]);

      // Log metrics for monitoring systems to pick up
      this.logger.log('Queue metrics collected', {
        timestamp: this.dateTimeUtils.getCurrentUtcDateTime(),
        queues: queueMetrics,
        workers: workerMetrics,
      });

      // Check for concerning metrics
      this.checkForAlerts(queueMetrics, workerMetrics);
    } catch (error) {
      this.logger.error('Failed to collect queue metrics:', error);
    }
  }

  /**
   * Check for concerning metrics and log alerts
   */
  private checkForAlerts(
    queueMetrics: Array<{
      queueName: string;
      waiting: number;
      active: number;
      completed: number;
      failed: number;
      delayed: number;
      paused: boolean;
      throughput?: number;
    }>,
    workerMetrics: Array<{
      workerName: string;
      queueName: string;
      isRunning: boolean;
      concurrency: number;
      processing: number;
      uptime?: number;
    }>
  ): void {
    // Check for high queue backlogs
    const highBacklogQueues = queueMetrics.filter(q => q.waiting > 1000);
    if (highBacklogQueues.length > 0) {
      this.logger.warn('High queue backlogs detected', {
        queues: highBacklogQueues.map(q => ({
          name: q.queueName,
          waiting: q.waiting,
        })),
      });
    }

    // Check for high failure rates
    const highFailureQueues = queueMetrics.filter(q => {
      const total = q.completed + q.failed;
      return total > 0 && (q.failed / total) > 0.1; // More than 10% failure rate
    });
    if (highFailureQueues.length > 0) {
      this.logger.warn('High failure rates detected', {
        queues: highFailureQueues.map(q => ({
          name: q.queueName,
          failed: q.failed,
          completed: q.completed,
          failureRate: ((q.failed / (q.completed + q.failed)) * 100).toFixed(2) + '%',
        })),
      });
    }

    // Check for paused queues
    const pausedQueues = queueMetrics.filter(q => q.paused);
    if (pausedQueues.length > 0) {
      this.logger.warn('Paused queues detected', {
        queues: pausedQueues.map(q => q.queueName),
      });
    }

    // Check for stopped workers
    const stoppedWorkers = workerMetrics.filter(w => !w.isRunning);
    if (stoppedWorkers.length > 0) {
      this.logger.warn('Stopped workers detected', {
        workers: stoppedWorkers.map(w => ({
          name: w.workerName,
          queue: w.queueName,
        })),
      });
    }
  }

  /**
   * Get current monitoring status
   */
  isMonitoringActive(): boolean {
    return this.isMonitoring;
  }

  /**
   * Force a health check (for testing or manual triggers)
   */
  async forceHealthCheck(): Promise<{
    isHealthy: boolean;
    summary: any;
    timestamp: string;
  }> {
    const systemHealth = await this.queueHealthService.getSystemHealthStatus();
    
    // Update health check service
    this.healthCheckService.setQueueStatus(systemHealth.isHealthy, {
      summary: systemHealth.summary,
      queues: systemHealth.queues,
      workers: systemHealth.workers,
    });

    return {
      isHealthy: systemHealth.isHealthy,
      summary: systemHealth.summary,
      timestamp: systemHealth.timestamp,
    };
  }
}
