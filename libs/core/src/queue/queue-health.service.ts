import { Injectable, Logger } from '@nestjs/common';
import { QueueService } from './queue.service';
import { DateTimeUtilsService } from '@app/utils';
import { type QueueHealthStatus, type WorkerHealthStatus } from './queue.error';

/**
 * Queue health monitoring service
 * Provides health check functionality for queues and workers
 */
@Injectable()
export class QueueHealthService {
  private readonly logger = new Logger(QueueHealthService.name);
  private readonly workers = new Map<string, () => Promise<WorkerHealthStatus>>();

  constructor(
    private readonly queueService: QueueService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  /**
   * Register a worker health check function
   */
  registerWorkerHealthCheck(workerName: string, healthCheckFn: () => Promise<WorkerHealthStatus>): void {
    this.workers.set(workerName, healthCheckFn);
    this.logger.log(`Worker health check registered for: ${workerName}`);
  }

  /**
   * Unregister a worker health check
   */
  unregisterWorkerHealthCheck(workerName: string): void {
    this.workers.delete(workerName);
    this.logger.log(`Worker health check unregistered for: ${workerName}`);
  }

  /**
   * Get health status for all queues
   */
  async getQueuesHealthStatus(): Promise<QueueHealthStatus[]> {
    try {
      return await this.queueService.getAllQueuesHealthStatus();
    } catch (error) {
      this.logger.error('Failed to get queues health status:', error);
      return [];
    }
  }

  /**
   * Get health status for all registered workers
   */
  async getWorkersHealthStatus(): Promise<WorkerHealthStatus[]> {
    const workerStatuses: WorkerHealthStatus[] = [];

    for (const [workerName, healthCheckFn] of this.workers.entries()) {
      try {
        const status = await healthCheckFn();
        workerStatuses.push(status);
      } catch (error) {
        this.logger.error(`Failed to get health status for worker '${workerName}':`, error);
        workerStatuses.push({
          name: workerName,
          queueName: 'unknown',
          isRunning: false,
          concurrency: 0,
          processing: 0,
          lastError: error instanceof Error ? error.message : 'Unknown error',
          lastErrorAt: this.dateTimeUtils.getCurrentUtcDateTime(),
        });
      }
    }

    return workerStatuses;
  }

  /**
   * Get comprehensive health status for the entire queue system
   */
  async getSystemHealthStatus(): Promise<{
    isHealthy: boolean;
    queues: QueueHealthStatus[];
    workers: WorkerHealthStatus[];
    summary: {
      totalQueues: number;
      healthyQueues: number;
      totalWorkers: number;
      runningWorkers: number;
      totalJobs: {
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        delayed: number;
      };
    };
    timestamp: string;
  }> {
    const [queues, workers] = await Promise.all([
      this.getQueuesHealthStatus(),
      this.getWorkersHealthStatus(),
    ]);

    const healthyQueues = queues.filter(q => q.isHealthy).length;
    const runningWorkers = workers.filter(w => w.isRunning).length;

    const totalJobs = queues.reduce(
      (acc, queue) => ({
        waiting: acc.waiting + queue.waiting,
        active: acc.active + queue.active,
        completed: acc.completed + queue.completed,
        failed: acc.failed + queue.failed,
        delayed: acc.delayed + queue.delayed,
      }),
      { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 }
    );

    const isHealthy = healthyQueues === queues.length && runningWorkers === workers.length;

    return {
      isHealthy,
      queues,
      workers,
      summary: {
        totalQueues: queues.length,
        healthyQueues,
        totalWorkers: workers.length,
        runningWorkers,
        totalJobs,
      },
      timestamp: this.dateTimeUtils.getCurrentUtcDateTime(),
    };
  }

  /**
   * Get health status for a specific queue
   */
  async getQueueHealthStatus(queueName: string): Promise<QueueHealthStatus | null> {
    try {
      const queues = await this.getQueuesHealthStatus();
      return queues.find(q => q.name === queueName) || null;
    } catch (error) {
      this.logger.error(`Failed to get health status for queue '${queueName}':`, error);
      return null;
    }
  }

  /**
   * Get health status for a specific worker
   */
  async getWorkerHealthStatus(workerName: string): Promise<WorkerHealthStatus | null> {
    try {
      const healthCheckFn = this.workers.get(workerName);
      if (!healthCheckFn) {
        return null;
      }
      return await healthCheckFn();
    } catch (error) {
      this.logger.error(`Failed to get health status for worker '${workerName}':`, error);
      return {
        name: workerName,
        queueName: 'unknown',
        isRunning: false,
        concurrency: 0,
        processing: 0,
        lastError: error instanceof Error ? error.message : 'Unknown error',
        lastErrorAt: this.dateTimeUtils.getCurrentUtcDateTime(),
      };
    }
  }

  /**
   * Check if the queue system is healthy
   */
  async isSystemHealthy(): Promise<boolean> {
    try {
      const systemHealth = await this.getSystemHealthStatus();
      return systemHealth.isHealthy;
    } catch (error) {
      this.logger.error('Failed to check system health:', error);
      return false;
    }
  }

  /**
   * Get queue metrics for monitoring
   */
  async getQueueMetrics(): Promise<{
    queueName: string;
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: boolean;
    throughput?: number; // jobs per minute
  }[]> {
    const queues = await this.getQueuesHealthStatus();
    
    return queues.map(queue => ({
      queueName: queue.name,
      waiting: queue.waiting,
      active: queue.active,
      completed: queue.completed,
      failed: queue.failed,
      delayed: queue.delayed,
      paused: queue.paused,
      // TODO: Calculate throughput based on historical data
      throughput: 0,
    }));
  }

  /**
   * Get worker metrics for monitoring
   */
  async getWorkerMetrics(): Promise<{
    workerName: string;
    queueName: string;
    isRunning: boolean;
    concurrency: number;
    processing: number;
    uptime?: number; // in seconds
  }[]> {
    const workers = await this.getWorkersHealthStatus();
    
    return workers.map(worker => ({
      workerName: worker.name,
      queueName: worker.queueName,
      isRunning: worker.isRunning,
      concurrency: worker.concurrency,
      processing: worker.processing,
      // TODO: Calculate uptime based on start time
      uptime: 0,
    }));
  }
}
