import { Test, TestingModule } from '@nestjs/testing';
import { BaseWorkerService } from '../base-worker.service';
import { DateTimeUtilsService } from '@app/utils';
import { QueueError, type JobResult } from '../queue.error';
import { DEFAULT_WORKER_OPTIONS } from '../queue.config';
import type { Job } from 'bullmq';

// Mock Redis and BullMQ
jest.mock('ioredis');
jest.mock('bullmq');

// Test implementation of BaseWorkerService
class TestWorkerService extends BaseWorkerService<{ testData: string }> {
  constructor(dateTimeUtils: DateTimeUtilsService) {
    const mockConnectionConfig = {
      host: 'localhost',
      port: 6379,
      db: 0,
      keyPrefix: 'test:',
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
    };
    
    super('test-queue', mockConnectionConfig, DEFAULT_WORKER_OPTIONS, dateTimeUtils);
  }

  protected async processJob(job: Job<{ testData: string }>): Promise<JobResult<{ result: string }>> {
    // Mock job processing
    return {
      success: true,
      data: { result: `processed-${job.data.testData}` },
      jobId: job.id || 'unknown',
      queueName: this.queueName,
      processedAt: '2024-01-01T00:00:00.000Z',
      duration: 100,
      attempts: 1,
    };
  }
}

describe('BaseWorkerService', () => {
  let service: TestWorkerService;
  let dateTimeUtils: DateTimeUtilsService;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        {
          provide: DateTimeUtilsService,
          useValue: {
            getCurrentUtcDateTime: jest.fn().mockReturnValue('2024-01-01T00:00:00.000Z'),
            getNewDate: jest.fn().mockReturnValue(new Date('2024-01-01T00:00:00.000Z')),
          },
        },
      ],
    }).compile();

    dateTimeUtils = module.get<DateTimeUtilsService>(DateTimeUtilsService);
    service = new TestWorkerService(dateTimeUtils);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should initialize with correct queue name', () => {
      expect(service['queueName']).toBe('test-queue');
    });

    it('should not be running initially', () => {
      expect(service['isRunning']).toBe(false);
    });
  });

  describe('startWorker', () => {
    it('should start the worker successfully', async () => {
      // Mock Worker constructor and methods
      const mockWorker = {
        on: jest.fn(),
        close: jest.fn().mockResolvedValue(undefined),
      };

      // Mock Redis constructor
      const mockConnection = {
        disconnect: jest.fn().mockResolvedValue(undefined),
      };

      // Mock the constructors
      const { Worker } = require('bullmq');
      const { Redis } = require('ioredis');
      
      Worker.mockImplementation(() => mockWorker);
      Redis.mockImplementation(() => mockConnection);

      await service.startWorker();

      expect(service['isRunning']).toBe(true);
      expect(Worker).toHaveBeenCalledWith(
        'test-queue',
        expect.any(Function),
        expect.objectContaining({
          connection: mockConnection,
          concurrency: DEFAULT_WORKER_OPTIONS.concurrency,
        })
      );
    });

    it('should not start if already running', async () => {
      service['isRunning'] = true;

      await service.startWorker();

      // Should not create new worker
      expect(service['isRunning']).toBe(true);
    });

    it('should throw QueueError when start fails', async () => {
      const { Redis } = require('ioredis');
      Redis.mockImplementation(() => {
        throw new Error('Connection failed');
      });

      await expect(service.startWorker()).rejects.toThrow(QueueError);
    });
  });

  describe('stopWorker', () => {
    beforeEach(async () => {
      // Setup a running worker
      const mockWorker = {
        on: jest.fn(),
        close: jest.fn().mockResolvedValue(undefined),
      };
      const mockConnection = {
        disconnect: jest.fn().mockResolvedValue(undefined),
      };

      const { Worker } = require('bullmq');
      const { Redis } = require('ioredis');
      
      Worker.mockImplementation(() => mockWorker);
      Redis.mockImplementation(() => mockConnection);

      await service.startWorker();
    });

    it('should stop the worker successfully', async () => {
      await service.stopWorker();

      expect(service['isRunning']).toBe(false);
      expect(service['worker'].close).toHaveBeenCalled();
      expect(service['connection'].disconnect).toHaveBeenCalled();
    });

    it('should not stop if not running', async () => {
      service['isRunning'] = false;

      await service.stopWorker();

      // Should handle gracefully
      expect(service['isRunning']).toBe(false);
    });
  });

  describe('processJobWrapper', () => {
    let mockJob: Partial<Job<{ testData: string }>>;

    beforeEach(() => {
      mockJob = {
        id: 'test-job-1',
        name: 'test-job',
        data: { testData: 'test' },
        attemptsMade: 1,
        updateProgress: jest.fn().mockResolvedValue(undefined),
      };
    });

    it('should process job successfully', async () => {
      const result = await service['processJobWrapper'](mockJob as Job<{ testData: string }>);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ result: 'processed-test' });
      expect(result.jobId).toBe('test-job-1');
      expect(result.queueName).toBe('test-queue');
      expect(mockJob.updateProgress).toHaveBeenCalledWith(0);
      expect(mockJob.updateProgress).toHaveBeenCalledWith(100);
    });

    it('should handle job processing errors', async () => {
      // Override processJob to throw error
      service['processJob'] = jest.fn().mockRejectedValue(new Error('Processing failed'));

      await expect(
        service['processJobWrapper'](mockJob as Job<{ testData: string }>)
      ).rejects.toThrow('Processing failed');

      expect(service['lastError']).toBe('Processing failed');
      expect(service['lastErrorAt']).toBe('2024-01-01T00:00:00.000Z');
    });
  });

  describe('getHealthStatus', () => {
    it('should return health status for running worker', async () => {
      service['isRunning'] = true;
      service['lastProcessedAt'] = '2024-01-01T00:00:00.000Z';

      // Mock worker.getNextJob
      service['worker'] = {
        getNextJob: jest.fn().mockResolvedValue(0),
      } as any;

      const status = await service.getHealthStatus();

      expect(status).toEqual({
        name: 'TestWorkerService',
        queueName: 'test-queue',
        isRunning: true,
        concurrency: DEFAULT_WORKER_OPTIONS.concurrency,
        processing: 0,
        lastProcessedAt: '2024-01-01T00:00:00.000Z',
        lastError: undefined,
        lastErrorAt: undefined,
      });
    });

    it('should return health status for stopped worker', async () => {
      service['isRunning'] = false;
      service['lastError'] = 'Worker stopped';
      service['lastErrorAt'] = '2024-01-01T00:00:00.000Z';

      const status = await service.getHealthStatus();

      expect(status.isRunning).toBe(false);
      expect(status.lastError).toBe('Worker stopped');
    });

    it('should handle health check errors', async () => {
      service['isRunning'] = true;
      service['worker'] = {
        getNextJob: jest.fn().mockRejectedValue(new Error('Health check failed')),
      } as any;

      const status = await service.getHealthStatus();

      expect(status.isRunning).toBe(false);
      expect(status.lastError).toBe('Health check failed');
    });
  });

  describe('event handlers', () => {
    let mockJob: Partial<Job<{ testData: string }>>;

    beforeEach(() => {
      mockJob = {
        id: 'test-job-1',
        name: 'test-job',
        data: { testData: 'test' },
        attemptsMade: 1,
      };
    });

    it('should handle job completion', () => {
      const result: JobResult<unknown> = {
        success: true,
        data: { result: 'processed' },
        jobId: 'test-job-1',
        queueName: 'test-queue',
        processedAt: '2024-01-01T00:00:00.000Z',
        duration: 100,
        attempts: 1,
      };

      // Should not throw
      expect(() => {
        service['onJobCompleted'](mockJob as Job<{ testData: string }>, result);
      }).not.toThrow();
    });

    it('should handle job failure', () => {
      const error = new Error('Job failed');

      // Should not throw
      expect(() => {
        service['onJobFailed'](mockJob as Job<{ testData: string }>, error);
      }).not.toThrow();
    });

    it('should handle job progress', () => {
      // Should not throw
      expect(() => {
        service['onJobProgress'](mockJob as Job<{ testData: string }>, 50);
      }).not.toThrow();
    });

    it('should handle stalled jobs', () => {
      // Should not throw
      expect(() => {
        service['onJobStalled']('test-job-1');
      }).not.toThrow();
    });

    it('should handle worker errors', () => {
      const error = new Error('Worker error');

      service['onWorkerError'](error);

      expect(service['lastError']).toBe('Worker error');
      expect(service['lastErrorAt']).toBe('2024-01-01T00:00:00.000Z');
    });
  });

  describe('onModuleDestroy', () => {
    it('should stop worker on module destroy', async () => {
      service.stopWorker = jest.fn().mockResolvedValue(undefined);

      await service.onModuleDestroy();

      expect(service.stopWorker).toHaveBeenCalled();
    });
  });
});
