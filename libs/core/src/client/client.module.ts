import { Modu<PERSON> } from '@nestjs/common';
import { AnalyserClientProvider } from './analyser-client.provider';
import { OmsClientProvider } from './oms-client.provider';
import { TickerClientProvider } from './ticker-client.provider';
import { SimulatorClientProvider } from './simulator-client.provider';
import { ApiClientProvider } from './api-client.provider';

@Module({
  imports: [],
  providers: [
    AnalyserClientProvider,
    OmsClientProvider,
    TickerClientProvider,
    SimulatorClientProvider,
    ApiClientProvider,
  ],
  exports: [
    AnalyserClientProvider,
    OmsClientProvider,
    TickerClientProvider,
    SimulatorClientProvider,
    ApiClientProvider,
  ],
})
export class ClientModule {}
