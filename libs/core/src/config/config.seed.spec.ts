import { DEFAULT_CONFIGS, CONFIG_DESCRIPTIONS } from './config.schema';

describe('Configuration Seeding', () => {
  describe('DEFAULT_CONFIGS', () => {
    it('should have all required service configurations', () => {
      expect(DEFAULT_CONFIGS.service).toBeDefined();
      expect(DEFAULT_CONFIGS.service.API_BASE_URL).toBe('http://localhost:3000');
    });

    it('should have all required broker configurations', () => {
      expect(DEFAULT_CONFIGS.broker).toBeDefined();
      expect(DEFAULT_CONFIGS.broker.BROKER_KEY_ROTATION_INTERVAL).toBe(86400000);
      expect(DEFAULT_CONFIGS.broker.BROKER_HEALTH_CHECK_INTERVAL).toBe(30000);
      expect(DEFAULT_CONFIGS.broker.BROKER_CONNECTION_TIMEOUT).toBe(10000);
      expect(DEFAULT_CONFIGS.broker.BROKER_MAX_RETRY_ATTEMPTS).toBe(3);
      expect(DEFAULT_CONFIGS.broker.BROKER_SESSION_TIMEOUT).toBe(3600000);
    });

    it('should have all required security configurations', () => {
      expect(DEFAULT_CONFIGS.security).toBeDefined();
      expect(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_REQUESTS).toBe(100);
      expect(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_WINDOW).toBe(60000);
    });

    it('should have all required database configurations', () => {
      expect(DEFAULT_CONFIGS.database).toBeDefined();
      expect(DEFAULT_CONFIGS.database.QUESTDB_MAX_CONNECTIONS).toBe(10);
    });
  });

  describe('CONFIG_DESCRIPTIONS', () => {
    it('should have descriptions for all configuration keys', () => {
      const requiredKeys = [
        'API_BASE_URL',
        'ANALYSER_SERVICE_URL',
        'OMS_SERVICE_URL',
        'TICKER_SERVICE_URL',
        'SIMULATOR_SERVICE_URL',
        'BROKER_KEY_ROTATION_INTERVAL',
        'BROKER_HEALTH_CHECK_INTERVAL',
        'BROKER_CONNECTION_TIMEOUT',
        'BROKER_MAX_RETRY_ATTEMPTS',
        'BROKER_SESSION_TIMEOUT',
        'BROKER_RATE_LIMIT_REQUESTS',
        'BROKER_RATE_LIMIT_WINDOW',
        'QUESTDB_MAX_CONNECTIONS',
      ];

      for (const key of requiredKeys) {
        expect(CONFIG_DESCRIPTIONS[key as keyof typeof CONFIG_DESCRIPTIONS]).toBeDefined();
        expect(typeof CONFIG_DESCRIPTIONS[key as keyof typeof CONFIG_DESCRIPTIONS]).toBe('string');
        expect(CONFIG_DESCRIPTIONS[key as keyof typeof CONFIG_DESCRIPTIONS].length).toBeGreaterThan(0);
      }
    });

    it('should have meaningful descriptions', () => {
      expect(CONFIG_DESCRIPTIONS.API_BASE_URL).toContain('Base URL');
      expect(CONFIG_DESCRIPTIONS.BROKER_HEALTH_CHECK_INTERVAL).toContain('health check');
      expect(CONFIG_DESCRIPTIONS.BROKER_RATE_LIMIT_REQUESTS).toContain('rate limit');
      expect(CONFIG_DESCRIPTIONS.QUESTDB_MAX_CONNECTIONS).toContain('QuestDB');
    });
  });

  describe('Configuration Validation', () => {
    it('should have valid timeout values', () => {
      // All timeout values should be positive numbers
      expect(DEFAULT_CONFIGS.broker.BROKER_HEALTH_CHECK_INTERVAL).toBeGreaterThan(0);
      expect(DEFAULT_CONFIGS.broker.BROKER_CONNECTION_TIMEOUT).toBeGreaterThan(0);
      expect(DEFAULT_CONFIGS.broker.BROKER_SESSION_TIMEOUT).toBeGreaterThan(0);
      expect(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_WINDOW).toBeGreaterThan(0);
    });

    it('should have valid count values', () => {
      // All count values should be positive integers
      expect(DEFAULT_CONFIGS.broker.BROKER_MAX_RETRY_ATTEMPTS).toBeGreaterThan(0);
      expect(Number.isInteger(DEFAULT_CONFIGS.broker.BROKER_MAX_RETRY_ATTEMPTS)).toBe(true);
      
      expect(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_REQUESTS).toBeGreaterThan(0);
      expect(Number.isInteger(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_REQUESTS)).toBe(true);
      
      expect(DEFAULT_CONFIGS.database.QUESTDB_MAX_CONNECTIONS).toBeGreaterThan(0);
      expect(Number.isInteger(DEFAULT_CONFIGS.database.QUESTDB_MAX_CONNECTIONS)).toBe(true);
    });

    it('should have reasonable default values', () => {
      // Health check interval should be reasonable (30 seconds)
      expect(DEFAULT_CONFIGS.broker.BROKER_HEALTH_CHECK_INTERVAL).toBe(30000);
      
      // Connection timeout should be reasonable (10 seconds)
      expect(DEFAULT_CONFIGS.broker.BROKER_CONNECTION_TIMEOUT).toBe(10000);
      
      // Rate limit should be reasonable (100 requests per minute)
      expect(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_REQUESTS).toBe(100);
      expect(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_WINDOW).toBe(60000);
      
      // Key rotation should be daily
      expect(DEFAULT_CONFIGS.broker.BROKER_KEY_ROTATION_INTERVAL).toBe(86400000); // 24 hours
      
      // Session timeout should be 1 hour
      expect(DEFAULT_CONFIGS.broker.BROKER_SESSION_TIMEOUT).toBe(3600000); // 1 hour
    });
  });

  describe('Configuration Categories', () => {
    it('should categorize configurations correctly', () => {
      // Service configurations should be URLs or service-related
      expect(DEFAULT_CONFIGS.service.API_BASE_URL).toMatch(/^https?:\/\//);
      
      // Broker configurations should be numeric values for timeouts/limits
      expect(typeof DEFAULT_CONFIGS.broker.BROKER_HEALTH_CHECK_INTERVAL).toBe('number');
      expect(typeof DEFAULT_CONFIGS.broker.BROKER_CONNECTION_TIMEOUT).toBe('number');
      expect(typeof DEFAULT_CONFIGS.broker.BROKER_MAX_RETRY_ATTEMPTS).toBe('number');
      
      // Security configurations should be numeric limits
      expect(typeof DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_REQUESTS).toBe('number');
      expect(typeof DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_WINDOW).toBe('number');
      
      // Database configurations should be connection-related
      expect(typeof DEFAULT_CONFIGS.database.QUESTDB_MAX_CONNECTIONS).toBe('number');
    });
  });

  describe('Environment Compatibility', () => {
    it('should have localhost URLs for local development', () => {
      expect(DEFAULT_CONFIGS.service.API_BASE_URL).toBe('http://localhost:3000');
    });

    it('should have development-friendly timeouts', () => {
      // Timeouts should not be too aggressive for development
      expect(DEFAULT_CONFIGS.broker.BROKER_CONNECTION_TIMEOUT).toBeGreaterThanOrEqual(5000); // At least 5 seconds
      expect(DEFAULT_CONFIGS.broker.BROKER_HEALTH_CHECK_INTERVAL).toBeGreaterThanOrEqual(10000); // At least 10 seconds
    });

    it('should have reasonable rate limits for development', () => {
      // Rate limits should not be too restrictive for development
      expect(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_REQUESTS).toBeGreaterThanOrEqual(50);
      expect(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_WINDOW).toBeGreaterThanOrEqual(60000); // At least 1 minute
    });
  });

  describe('Configuration Consistency', () => {
    it('should have consistent naming patterns', () => {
      const brokerKeys = Object.keys(DEFAULT_CONFIGS.broker);
      
      // All broker keys should start with BROKER_
      for (const key of brokerKeys) {
        expect(key).toMatch(/^BROKER_/);
      }
      
      // Timeout keys should end with _TIMEOUT
      const timeoutKeys = brokerKeys.filter(key => key.includes('TIMEOUT'));
      for (const key of timeoutKeys) {
        expect(key).toMatch(/_TIMEOUT$/);
      }
      
      // Interval keys should end with _INTERVAL
      const intervalKeys = brokerKeys.filter(key => key.includes('INTERVAL'));
      for (const key of intervalKeys) {
        expect(key).toMatch(/_INTERVAL$/);
      }
    });

    it('should have descriptions matching configuration keys', () => {
      // Every default config should have a corresponding description
      const allConfigKeys = [
        ...Object.keys(DEFAULT_CONFIGS.service),
        ...Object.keys(DEFAULT_CONFIGS.broker),
        ...Object.keys(DEFAULT_CONFIGS.security),
        ...Object.keys(DEFAULT_CONFIGS.database),
      ];

      for (const key of allConfigKeys) {
        expect(CONFIG_DESCRIPTIONS[key as keyof typeof CONFIG_DESCRIPTIONS]).toBeDefined();
      }
    });
  });
});
