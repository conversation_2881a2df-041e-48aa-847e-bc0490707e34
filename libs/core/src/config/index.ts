// ==================== MODULE EXPORTS ====================
export { ConfigModule } from './config.module';

// ==================== SERVICE EXPORTS ====================
export { ConfigService } from './config.service';
export { ConfigRepository } from './config.repository';

// ==================== SCHEMA EXPORTS ====================
export {
  // Base schemas
  baseConfigSchema,
  createConfigSchema,
  updateConfigSchema,
  configQuerySchema,
  bulkConfigSchema,
  
  // Configuration schemas
  serviceConfigSchema,
  brokerConfigSchema,
  securityConfigSchema,
  databaseConfigSchema,
  appConfigSchema,
  
  // Enum schemas
  ConfigCategoryEnum,
  ConfigDataTypeEnum,
  
  // Constants
  DEFAULT_CONFIGS,
  CONFIG_DESCRIPTIONS,
} from './config.schema';

// ==================== TYPE EXPORTS ====================
export type {
  Config,
  CreateConfig,
  UpdateConfig,
  ConfigCategory,
  ConfigDataType,
  ServiceConfig,
  BrokerConfig,
  SecurityConfig,
  DatabaseConfig,
  AppConfig,
  ConfigQuery,
  BulkConfig,
} from './config.schema';

// ==================== MODEL EXPORTS ====================
export { ConfigTable, CONFIG_TABLE_METADATA } from './config.model';
export type { ConfigTableSelect, ConfigTableInsert } from './config.model';

// ==================== ERROR EXPORTS ====================
export {
  ConfigError,
  ConfigNotFoundError,
  DuplicateConfigKeyError,
  InvalidConfigValueError,
  ConfigInactiveError,
  EnvironmentMismatchError,
  ConfigErrorEnum,
  createConfigValidationError,
  createConfigCacheError,
  createConfigDatabaseError,
} from './config.error';

export type { ConfigErrorEnumType } from './config.error';
