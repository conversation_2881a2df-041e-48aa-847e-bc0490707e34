import { Injectable } from '@nestjs/common';
import { eq, and, inArray, type InferInsertModel, type SQL } from 'drizzle-orm';
import { BaseRepository } from '@app/common/repository';
import { DrizzleService } from '../drizzle/drizzle.service';
import { UtilsService, DbUtilsService } from '@app/utils';
import { ConfigTable } from './config.model';
import { Config, CreateConfig, UpdateConfig, ConfigQuery } from './config.schema';
import { 
  ConfigError, 
  ConfigNotFoundError, 
  DuplicateConfigKeyError,
  ConfigInactiveError,
  createConfigDatabaseError 
} from './config.error';
import type { Environment } from '@app/common/constants';

// Type for ConfigTable insert operations
type ConfigInsert = InferInsertModel<typeof ConfigTable>;

/**
 * Configuration repository providing data access operations for application configuration
 * Extends BaseRepository with configuration-specific functionality including environment filtering
 */
@Injectable()
export class ConfigRepository extends BaseRepository<Config, CreateConfig, UpdateConfig> {
  constructor(
    drizzleService: DrizzleService,
    utilsService: UtilsService,
    dbUtils: DbUtilsService,
  ) {
    super(drizzleService, utilsService, dbUtils, 'ConfigRepository');
  }

  // ==================== ABSTRACT METHOD IMPLEMENTATIONS ====================

  protected getTable() {
    return ConfigTable;
  }

  protected getTableName(): string {
    return 'configurations';
  }

  // ==================== CONFIGURATION-SPECIFIC METHODS ====================

  /**
   * Find configuration by key and environment
   * @param key - Configuration key
   * @param environment - Target environment
   * @param includeInactive - Whether to include inactive configurations
   * @returns Promise<Config | null> - Configuration or null if not found
   */
  async findByKey(key: string, environment: Environment, includeInactive = false): Promise<Config | null> {
    return this.executeWithErrorHandling('findByKey', async () => {
      this.logOperation('findByKey', { key, environment, includeInactive });

      const conditions = [
        eq(ConfigTable.key, key),
        eq(ConfigTable.environment, environment),
      ];

      if (!includeInactive) {
        conditions.push(eq(ConfigTable.isActive, true));
      }

      const [result] = await this.getDb()
        .select()
        .from(ConfigTable)
        .where(and(...conditions))
        .limit(1);

      return result as Config || null;
    });
  }

  /**
   * Find configurations by multiple keys for a specific environment
   * @param keys - Array of configuration keys
   * @param environment - Target environment
   * @param includeInactive - Whether to include inactive configurations
   * @returns Promise<Config[]> - Array of configurations
   */
  async findByKeys(keys: string[], environment: Environment, includeInactive = false): Promise<Config[]> {
    return this.executeWithErrorHandling('findByKeys', async () => {
      this.logOperation('findByKeys', { keys, environment, includeInactive });

      if (keys.length === 0) {
        return [];
      }

      const conditions = [
        inArray(ConfigTable.key, keys),
        eq(ConfigTable.environment, environment),
      ];

      if (!includeInactive) {
        conditions.push(eq(ConfigTable.isActive, true));
      }

      const results = await this.getDb()
        .select()
        .from(ConfigTable)
        .where(and(...conditions));

      return results as Config[];
    });
  }

  /**
   * Find configurations by category and environment
   * @param category - Configuration category
   * @param environment - Target environment
   * @param includeInactive - Whether to include inactive configurations
   * @returns Promise<Config[]> - Array of configurations
   */
  async findByCategory(category: string, environment: Environment, includeInactive = false): Promise<Config[]> {
    return this.executeWithErrorHandling('findByCategory', async () => {
      this.logOperation('findByCategory', { category, environment, includeInactive });

      const conditions = [
        eq(ConfigTable.category, category),
        eq(ConfigTable.environment, environment),
      ];

      if (!includeInactive) {
        conditions.push(eq(ConfigTable.isActive, true));
      }

      const results = await this.getDb()
        .select()
        .from(ConfigTable)
        .where(and(...conditions));

      return results as Config[];
    });
  }

  /**
   * Find all configurations for a specific environment
   * @param environment - Target environment
   * @param includeInactive - Whether to include inactive configurations
   * @returns Promise<Config[]> - Array of configurations
   */
  async findByEnvironment(environment: Environment, includeInactive = false): Promise<Config[]> {
    return this.executeWithErrorHandling('findByEnvironment', async () => {
      this.logOperation('findByEnvironment', { environment, includeInactive });

      const conditions = [eq(ConfigTable.environment, environment)];

      if (!includeInactive) {
        conditions.push(eq(ConfigTable.isActive, true));
      }

      const results = await this.getDb()
        .select()
        .from(ConfigTable)
        .where(and(...conditions));

      return results as Config[];
    });
  }

  /**
   * Query configurations with flexible filtering
   * @param query - Query parameters
   * @returns Promise<Config[]> - Array of configurations
   */
  async query(query: ConfigQuery): Promise<Config[]> {
    return this.executeWithErrorHandling('query', async () => {
      this.logOperation('query', { query });

      const conditions: SQL<unknown>[] = [];

      if (query.category) {
        conditions.push(eq(ConfigTable.category, query.category));
      }

      if (query.environment) {
        conditions.push(eq(ConfigTable.environment, query.environment));
      }

      if (query.isActive !== undefined) {
        conditions.push(eq(ConfigTable.isActive, query.isActive));
      }

      if (query.keys && query.keys.length > 0) {
        conditions.push(inArray(ConfigTable.key, query.keys));
      }

      const results = await this.getDb()
        .select()
        .from(ConfigTable)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      return results as Config[];
    });
  }

  /**
   * Create configuration with duplicate key validation
   * @param data - Configuration creation data
   * @returns Promise<Config> - Created configuration
   */
  async create(data: CreateConfig): Promise<Config> {
    return this.executeWithErrorHandling('create', async () => {
      this.logOperation('create', { data });

      // Check for duplicate key in the same environment
      const existing = await this.findByKey(data.key, data.environment, true);
      if (existing) {
        throw new DuplicateConfigKeyError(data.key, data.environment);
      }

      // Create the configuration
      return super.create(data);
    });
  }

  /**
   * Update configuration value by key and environment
   * @param key - Configuration key
   * @param environment - Target environment
   * @param updates - Update data
   * @returns Promise<Config> - Updated configuration
   */
  async updateByKey(key: string, environment: Environment, updates: UpdateConfig): Promise<Config> {
    return this.executeWithErrorHandling('updateByKey', async () => {
      this.logOperation('updateByKey', { key, environment, updates });

      // Find the configuration
      const existing = await this.findByKey(key, environment, true);
      if (!existing) {
        throw new ConfigNotFoundError(key, environment);
      }

      // Update using the base repository method
      return this.update(existing.id, updates);
    });
  }

  /**
   * Activate configuration by key and environment
   * @param key - Configuration key
   * @param environment - Target environment
   * @returns Promise<Config> - Activated configuration
   */
  async activate(key: string, environment: Environment): Promise<Config> {
    return this.updateByKey(key, environment, { isActive: true });
  }

  /**
   * Deactivate configuration by key and environment
   * @param key - Configuration key
   * @param environment - Target environment
   * @returns Promise<Config> - Deactivated configuration
   */
  async deactivate(key: string, environment: Environment): Promise<Config> {
    return this.updateByKey(key, environment, { isActive: false });
  }

  /**
   * Bulk create configurations
   * @param configs - Array of configuration creation data
   * @returns Promise<Config[]> - Created configurations
   */
  async bulkCreate(configs: CreateConfig[]): Promise<Config[]> {
    return this.executeWithErrorHandling('bulkCreate', async () => {
      this.logOperation('bulkCreate', { count: configs.length });

      if (configs.length === 0) {
        return [];
      }

      // Add audit fields to all configurations
      const auditedConfigs = configs.map(config => 
        this.addCreateAuditFields(config as Record<string, unknown>)
      );

      const results = await this.getDb()
        .insert(ConfigTable)
        .values(auditedConfigs as ConfigInsert[])
        .returning();

      this.logger.log(`Bulk created ${results.length} configurations`);
      return results as Config[];
    });
  }

  /**
   * Delete configuration by key and environment
   * @param key - Configuration key
   * @param environment - Target environment
   * @returns Promise<void>
   */
  async deleteByKey(key: string, environment: Environment): Promise<void> {
    return this.executeWithErrorHandling('deleteByKey', async () => {
      this.logOperation('deleteByKey', { key, environment });

      // Find the configuration
      const existing = await this.findByKey(key, environment, true);
      if (!existing) {
        throw new ConfigNotFoundError(key, environment);
      }

      // Delete using the base repository method
      await this.delete(existing.id);
    });
  }
}
