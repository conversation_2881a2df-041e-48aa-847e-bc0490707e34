import { Injectable } from '@nestjs/common';
import { DateTimeUtilsService } from '@app/utils';
import type { QueueHealthStatus, WorkerHealthStatus } from '../queue/queue.error';

/**
 * Health status interface for detailed health information
 */
export interface HealthStatus {
  appStatus: boolean;
  postgresStatus: boolean;
  questdbStatus: boolean;
  queueStatus: boolean;
}

/**
 * Detailed health information with timestamps and statistics
 */
export interface DetailedHealthStatus extends HealthStatus {
  lastUpdated: string;
  services: {
    postgres: {
      status: boolean;
      lastCheck?: string;
    };
    questdb: {
      status: boolean;
      lastCheck?: string;
      connectionStats?: {
        totalConnections: number;
        idleConnections: number;
        waitingCount: number;
        healthCheckCount: number;
        failedHealthChecks: number;
      };
    };
    queues: {
      status: boolean;
      lastCheck?: string;
      summary?: {
        totalQueues: number;
        healthyQueues: number;
        totalWorkers: number;
        runningWorkers: number;
      };
      queues?: QueueHealthStatus[];
      workers?: WorkerHealthStatus[];
    };
  };
}

@Injectable()
export class HealthCheckService {
  private appStatus: boolean = false;
  private postgresStatus: boolean = false;
  private questdbStatus: boolean = false;
  private queueStatus: boolean = false;
  private lastPostgresCheck?: Date;
  private lastQuestdbCheck?: Date;
  private lastQueueCheck?: Date;
  private questdbConnectionStats?: DetailedHealthStatus['services']['questdb']['connectionStats'];
  private queueHealthData?: {
    summary: DetailedHealthStatus['services']['queues']['summary'];
    queues: QueueHealthStatus[];
    workers: WorkerHealthStatus[];
  };

  constructor(private readonly dateTimeUtils: DateTimeUtilsService) {}

  /**
   * Calculate overall app status based on individual service statuses
   */
  private setAppStatus(): void {
    // App is healthy if PostgreSQL, QuestDB, and Queue system are healthy
    this.appStatus = this.postgresStatus && this.questdbStatus && this.queueStatus;
  }

  /**
   * Get overall application status
   */
  getAppStatus(): boolean {
    return this.appStatus;
  }

  /**
   * Set PostgreSQL status
   */
  setPostgresStatus(status: boolean): void {
    this.postgresStatus = status;
    this.lastPostgresCheck = this.dateTimeUtils.getNewDate();
    this.setAppStatus();
  }

  /**
   * Set QuestDB status
   */
  setQuestdbStatus(
    status: boolean,
    connectionStats?: DetailedHealthStatus['services']['questdb']['connectionStats'],
  ): void {
    this.questdbStatus = status;
    this.lastQuestdbCheck = this.dateTimeUtils.getNewDate();
    if (connectionStats) {
      this.questdbConnectionStats = connectionStats;
    }
    this.setAppStatus();
  }

  /**
   * Set Queue system status
   */
  setQueueStatus(
    status: boolean,
    healthData?: {
      summary: DetailedHealthStatus['services']['queues']['summary'];
      queues: QueueHealthStatus[];
      workers: WorkerHealthStatus[];
    },
  ): void {
    this.queueStatus = status;
    this.lastQueueCheck = this.dateTimeUtils.getNewDate();
    if (healthData) {
      this.queueHealthData = healthData;
    }
    this.setAppStatus();
  }

  /**
   * Get basic status details
   */
  getAllStatusDetails(): HealthStatus {
    return {
      appStatus: this.appStatus,
      postgresStatus: this.postgresStatus,
      questdbStatus: this.questdbStatus,
      queueStatus: this.queueStatus,
    };
  }

  /**
   * Get detailed health status with timestamps and statistics
   */
  getDetailedHealthStatus(): DetailedHealthStatus {
    return {
      appStatus: this.appStatus,
      postgresStatus: this.postgresStatus,
      questdbStatus: this.questdbStatus,
      queueStatus: this.queueStatus,
      lastUpdated: this.dateTimeUtils.getUtcNow(),
      services: {
        postgres: {
          status: this.postgresStatus,
          lastCheck: this.lastPostgresCheck?.toISOString(),
        },
        questdb: {
          status: this.questdbStatus,
          lastCheck: this.lastQuestdbCheck?.toISOString(),
          connectionStats: this.questdbConnectionStats,
        },
        queues: {
          status: this.queueStatus,
          lastCheck: this.lastQueueCheck?.toISOString(),
          summary: this.queueHealthData?.summary,
          queues: this.queueHealthData?.queues,
          workers: this.queueHealthData?.workers,
        },
      },
    };
  }

  /**
   * Get QuestDB specific status
   */
  getQuestdbStatus(): boolean {
    return this.questdbStatus;
  }

  /**
   * Get PostgreSQL specific status
   */
  getPostgresStatus(): boolean {
    return this.postgresStatus;
  }

  /**
   * Get Queue system specific status
   */
  getQueueStatus(): boolean {
    return this.queueStatus;
  }

  /**
   * Get Queue health data
   */
  getQueueHealthData(): {
    summary?: DetailedHealthStatus['services']['queues']['summary'];
    queues?: QueueHealthStatus[];
    workers?: WorkerHealthStatus[];
  } | undefined {
    return this.queueHealthData;
  }
}
