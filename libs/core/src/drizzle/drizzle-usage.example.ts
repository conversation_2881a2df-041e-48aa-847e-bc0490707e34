import { Injectable } from '@nestjs/common';
import { DrizzleService } from './drizzle.service';
import { eq } from 'drizzle-orm';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { BrokerAccountTable } from '@app/broker';
import { DatabaseError, DatabaseErrorEnum } from './database.error';

/**
 * Example service demonstrating how to use the refactored DrizzleService
 * with enhanced error handling, dayjs integration, and EnvService configuration
 * 
 * @version 2.0.0 - Updated for EnvService integration
 * @since 2024-01-15
 */
@Injectable()
export class ExampleDrizzleUsageService {
  constructor(
    private readonly drizzleService: DrizzleService,
    private readonly datetimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService
  ) {}

  /**
   * Example: Using the Drizzle ORM directly with enhanced error handling
   */
  async findBrokerCredentials(userId: string) {
    try {
      // Get the database instance - now configured via EnvService
      const db = this.drizzleService.getDb();

      // Use Drizzle ORM for type-safe queries
      const credentials = await db
        .select()
        .from(BrokerAccountTable)
        .where(eq(BrokerAccountTable.userId, userId))
        .limit(1);

      return credentials[0] || null;
    } catch (error) {
      // Enhanced error handling with domain-specific errors
      if (error instanceof DatabaseError) {
        // Database-specific error handling
        throw error; // Re-throw with context preserved
      }
      
      // Wrap unexpected errors
      throw new DatabaseError(
        'POSTGRES_CONNECTION_NOT_ESTABLISHED',
        'DATABASE' as const,
        {
          message: `Failed to fetch broker credentials: ${this.errorUtils.getErrorMessage(error)}`,
          cause: error,
        }
      );
    }
  }

  /**
   * Example: Using the enhanced executeQuery method with proper result typing
   */
  async getConnectionStats() {
    try {
      // Using the enhanced executeQuery method with QueryResult interface
      const result = await this.drizzleService.executeQuery<{ total_connections: number }>(
        'SELECT COUNT(*) as total_connections FROM pg_stat_activity WHERE state = $1',
        ['active'],
      );

      return {
        activeConnections: result.data[0]?.total_connections || 0,
        queryExecutedAt: result.executedAt, // UTC timestamp from UtilsService
        rowCount: result.rowCount,
        serviceStats: this.drizzleService.getConnectionStats(),
        isHealthy: this.drizzleService.isConnectionHealthy(),
      };
    } catch (error) {
      if (error instanceof DatabaseError) {
        throw error;
      }
      
      throw new DatabaseError(
        DatabaseErrorEnum.enum.POSTGRES_QUERY_FAILED,
        'DATABASE' as const,
        {
          message: `Failed to get connection stats: ${this.errorUtils.getErrorMessage(error)}`,
          cause: error,
        }
      );
    }
  }

  /**
   * Example: Health check method with comprehensive status
   */
  async checkDatabaseHealth() {
    try {
      const connectionTest = await this.drizzleService.testConnection();
      
      return {
        isConnected: this.drizzleService.isConnectionHealthy(),
        connectionStats: this.drizzleService.getConnectionStats(),
        canExecuteQuery: connectionTest,
                 timestamp: this.datetimeUtils.getUtcNow(), // UTC timestamp from datetimeUtils
        status: connectionTest ? 'healthy' : 'unhealthy',
      };
    } catch (error) {
      return {
        isConnected: false,
        connectionStats: this.drizzleService.getConnectionStats(),
        canExecuteQuery: false,
                 timestamp: this.datetimeUtils.getUtcNow(),
         status: 'error',
         error: this.errorUtils.getErrorMessage(error),
       };
     }
   }

   /**
    * Example: Using UTC timestamp helpers for data storage
    * Note: This example demonstrates timestamp handling - adjust field names according to actual schema
    */
   demonstrateTimestampUsage() {
     try {
       // Using DatetimeUtilService for UTC timestamp creation
       const now = this.datetimeUtils.getUtcNow();
       
       return {
         currentUtcTimestamp: now,
         formattedTimestamp: now,
         note: 'All timestamps stored as UTC strings for consistency',
       };
     } catch (error) {
       if (error instanceof DatabaseError) {
         throw error;
       }
       
       throw new DatabaseError(
         DatabaseErrorEnum.enum.POSTGRES_QUERY_FAILED,
         'DATABASE' as const,
         {
           message: `Failed to demonstrate timestamp usage: ${this.errorUtils.getErrorMessage(error)}`,
           cause: error,
         }
       );
     }
   }

   /**
    * Example: Demonstrating configuration access (EnvService integration)
    * Note: Direct configuration access should be done via EnvService, 
    * but DrizzleService provides connection status information
    */
   getDatabaseInfo() {
    return {
      connectionHealth: this.drizzleService.isConnectionHealthy(),
      connectionStats: this.drizzleService.getConnectionStats(),
      // Note: Sensitive config values are not exposed
      // Use EnvService directly if you need environment configuration
    };
  }
}
