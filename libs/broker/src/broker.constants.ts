// ==================== BROKER CONFIGURATION CONSTANTS ====================

import { BrokerTypeEnum } from '@app/common/constants';

/**
 * Broker-specific configuration constants
 */
export const BROKER_CONFIG = {
  [BrokerTypeEnum.enum.ZERODHA]: {
    name: 'Zerodha Kite',
    baseUrl: 'https://api.kite.trade',
    websocketUrl: 'wss://ws.kite.trade',
    loginUrl: 'https://kite.zerodha.com/connect/login',
    features: ['trading', 'market_data', 'historical_data', 'symbol_master'],
    limits: {
      maxOrdersPerSecond: 10,
      maxSubscriptions: 3000,
      maxHistoricalDays: 2000,
      connectionTimeout: 30000,
      requestTimeout: 10000,
    },
    exchanges: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'],
  },
  [BrokerTypeEnum.enum.ALICE_BLUE]: {
    name: '<PERSON>',
    baseUrl: 'https://ant.aliceblueonline.com/rest/AliceBlueAPIService',
    websocketUrl: 'wss://ws1.aliceblueonline.com/NorenWS/',
    features: ['trading', 'market_data', 'symbol_master'],
    limits: {
      maxOrdersPerSecond: 5,
      maxSubscriptions: 1000,
      maxHistoricalDays: 365,
      connectionTimeout: 30000,
      requestTimeout: 15000,
    },
    exchanges: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'],
  },
  [BrokerTypeEnum.enum.FINVASIA]: {
    name: 'Finvasia Shoonya',
    baseUrl: 'https://api.shoonya.com',
    websocketUrl: 'wss://api.shoonya.com/NorenWSTP/',
    features: ['trading', 'market_data', 'historical_data', 'symbol_master'],
    limits: {
      maxOrdersPerSecond: 10,
      maxSubscriptions: 1000,
      maxHistoricalDays: 365,
      connectionTimeout: 30000,
      requestTimeout: 10000,
    },
    exchanges: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'],
  },
  [BrokerTypeEnum.enum.UPSTOX]: {
    name: 'Upstox',
    baseUrl: 'https://api.upstox.com/v2',
    websocketUrl: 'wss://ws.upstox.com/v2/feed',
    features: ['trading', 'market_data', 'historical_data', 'symbol_master'],
    limits: {
      maxOrdersPerSecond: 10,
      maxSubscriptions: 5000,
      maxHistoricalDays: 365,
      connectionTimeout: 30000,
      requestTimeout: 10000,
    },
    exchanges: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'],
  },
  [BrokerTypeEnum.enum.DHAN]: {
    name: 'Dhan',
    baseUrl: 'https://api.dhan.co',
    websocketUrl: 'wss://api.dhan.co/v2/websocket/feed',
    features: ['trading', 'market_data', 'historical_data', 'symbol_master'],
    limits: {
      maxOrdersPerSecond: 10,
      maxSubscriptions: 1000,
      maxHistoricalDays: 365,
      connectionTimeout: 30000,
      requestTimeout: 10000,
    },
    exchanges: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'],
  },
  [BrokerTypeEnum.enum.GROW]: {
    name: 'Groww',
    baseUrl: 'https://groww.in/v1/api',
    websocketUrl: 'wss://groww.in/v1/api/ws',
    features: ['trading', 'market_data', 'symbol_master'],
    limits: {
      maxOrdersPerSecond: 5,
      maxSubscriptions: 500,
      maxHistoricalDays: 180,
      connectionTimeout: 30000,
      requestTimeout: 15000,
    },
    exchanges: ['NSE', 'BSE', 'NFO'],
  },
  [BrokerTypeEnum.enum.ANGEL_ONE]: {
    name: 'Angel One',
    baseUrl: 'https://apiconnect.angelbroking.com',
    websocketUrl: 'wss://smartapisocket.angelone.in/smart-stream',
    features: ['trading', 'market_data', 'historical_data', 'symbol_master'],
    limits: {
      maxOrdersPerSecond: 10,
      maxSubscriptions: 1000,
      maxHistoricalDays: 365,
      connectionTimeout: 30000,
      requestTimeout: 10000,
    },
    exchanges: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'],
  },
} as const;

// ==================== COMMON CONSTANTS ====================

/**
 * Default connection settings
 */
export const DEFAULT_CONNECTION_CONFIG = {
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
  heartbeatInterval: 30000, // 30 seconds
  reconnectInterval: 5000, // 5 seconds
  maxReconnectAttempts: 10,
} as const;

/**
 * Market data subscription modes
 */
export const SUBSCRIPTION_MODES = {
  LTP: 'ltp', // Last Traded Price only
  QUOTE: 'quote', // LTP + Market depth
  FULL: 'full', // Complete market data
} as const;

/**
 * Common symbol formats
 */
export const SYMBOL_FORMATS = {
  PATTERNTRADE: /^[A-Z]+:[A-Z0-9_-]+$/,
  NSE_EQUITY: /^[A-Z0-9_-]+$/,
  BSE_EQUITY: /^[0-9]+$/,
  NFO_FUTURES: /^[A-Z0-9_-]+[0-9]{2}[A-Z]{3}FUT$/,
  NFO_OPTIONS: /^[A-Z0-9_-]+[0-9]{2}[A-Z]{3}[0-9]+[CP]E$/,
} as const;

/**
 * Exchange mappings
 */
export const EXCHANGE_MAPPINGS = {
  NSE: 'NSE',
  BSE: 'BSE',
  NFO: 'NFO', // NSE Futures & Options
  BFO: 'BFO', // BSE Futures & Options
  CDS: 'CDS', // Currency Derivatives
  MCX: 'MCX', // Multi Commodity Exchange
} as const;

/**
 * Order validation constants
 */
export const ORDER_LIMITS = {
  MIN_QUANTITY: 1,
  MAX_QUANTITY: 999999,
  MIN_PRICE: 0.01,
  MAX_PRICE: 999999.99,
  MIN_TRIGGER_PRICE: 0.01,
  MAX_TRIGGER_PRICE: 999999.99,
  MAX_DISCLOSED_QUANTITY_PERCENT: 25, // 25% of total quantity
} as const;

/**
 * Market timings (IST)
 */
export const MARKET_TIMINGS = {
  EQUITY: {
    PRE_OPEN_START: '09:00:00',
    PRE_OPEN_END: '09:15:00',
    NORMAL_START: '09:15:00',
    NORMAL_END: '15:30:00',
    POST_CLOSE_START: '15:40:00',
    POST_CLOSE_END: '16:00:00',
  },
  DERIVATIVES: {
    NORMAL_START: '09:15:00',
    NORMAL_END: '15:30:00',
  },
  CURRENCY: {
    NORMAL_START: '09:00:00',
    NORMAL_END: '17:00:00',
  },
  COMMODITY: {
    NORMAL_START: '09:00:00',
    NORMAL_END: '23:30:00',
  },
} as const;

/**
 * Error retry configurations
 */
export const RETRY_CONFIG = {
  CONNECTION_ERRORS: {
    maxRetries: 5,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
  },
  API_ERRORS: {
    maxRetries: 3,
    baseDelay: 500,
    maxDelay: 5000,
    backoffMultiplier: 1.5,
  },
  WEBSOCKET_ERRORS: {
    maxRetries: 10,
    baseDelay: 2000,
    maxDelay: 60000,
    backoffMultiplier: 1.5,
  },
} as const;

/**
 * Cache TTL configurations (in seconds)
 */
export const CACHE_TTL = {
  SYMBOL_MASTER: 24 * 60 * 60, // 24 hours
  ACCOUNT_INFO: 5 * 60, // 5 minutes
  POSITIONS: 30, // 30 seconds
  HOLDINGS: 60, // 1 minute
  ORDERS: 10, // 10 seconds
  TICK_DATA: 1, // 1 second
} as const;

/**
 * WebSocket message types
 */
export const WS_MESSAGE_TYPES = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  SUBSCRIBE: 'subscribe',
  UNSUBSCRIBE: 'unsubscribe',
  TICK: 'tick',
  ERROR: 'error',
  HEARTBEAT: 'heartbeat',
  ORDER_UPDATE: 'order_update',
} as const;

/**
 * Broker priority for auto-selection
 */
export const BROKER_PRIORITY = {
  [BrokerTypeEnum.enum.ZERODHA]: 100,
  [BrokerTypeEnum.enum.UPSTOX]: 90,
  [BrokerTypeEnum.enum.ANGEL_ONE]: 80,
  [BrokerTypeEnum.enum.DHAN]: 70,
  [BrokerTypeEnum.enum.FINVASIA]: 60,
  [BrokerTypeEnum.enum.ALICE_BLUE]: 50,
  [BrokerTypeEnum.enum.GROW]: 40,
} as const;
