import {
  pgTable,
  varchar,
  boolean,
  text,
  index,
  uniqueIndex,
  timestamp,
  integer,
  decimal,
  date,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { baseModel } from '@app/common/models';

// ==================== BROKER ACCOUNTS TABLE ====================

export const BrokerAccountTable = pgTable(
  'broker_accounts',
  {
    ...baseModel,
    userId: text('user_id').notNull(),
    brokerType: varchar('broker_type', { length: 50 }).notNull(),
    accountName: varchar('account_name', { length: 255 }).notNull(),
    isAdminAccount: boolean('is_admin_account').default(false).notNull(),

    // Encrypted credential fields
    encryptedApiKey: text('encrypted_api_key').notNull(),
    encryptedApiSecret: text('encrypted_api_secret').notNull(),
    encryptedAccessToken: text('encrypted_access_token'),
    encryptedRefreshToken: text('encrypted_refresh_token'),

    // Connection status fields
    connectionStatus: varchar('connection_status', { length: 50 }).default('DISCONNECTED').notNull(),
    lastConnectedAt: timestamp('last_connected_at'),
    lastError: text('last_error'),
  },
  (table) => [
    // ==================== EXISTING INDEXES ====================
    index('broker_accounts_user_id_idx').on(table.userId),
    index('broker_accounts_broker_type_idx').on(table.brokerType),
    index('broker_accounts_connection_status_idx').on(table.connectionStatus),
    uniqueIndex('broker_accounts_user_broker_unique_idx').on(table.userId, table.brokerType),
    index('broker_accounts_admin_accounts_idx').on(table.isAdminAccount),

    // ==================== PERFORMANCE OPTIMIZATION INDEXES ====================

    // Composite index for filtering by user and connection status (common query pattern)
    index('broker_accounts_user_status_idx').on(table.userId, table.connectionStatus),

    // Composite index for filtering by broker type and connection status (dashboard queries)
    index('broker_accounts_type_status_idx').on(table.brokerType, table.connectionStatus),

    // Composite index for admin account filtering with connection status
    index('broker_accounts_admin_status_idx').on(table.isAdminAccount, table.connectionStatus),

    // Index for last connected timestamp (for connection monitoring queries)
    index('broker_accounts_last_connected_idx').on(table.lastConnectedAt),

    // Composite index for active accounts filtering (user + connected status + broker type)
    index('broker_accounts_active_accounts_idx').on(table.userId, table.connectionStatus, table.brokerType),

    // Index for account name searches (case-insensitive)
    index('broker_accounts_account_name_idx').on(table.accountName),

    // Composite index for audit queries (created_at + user_id)
    index('broker_accounts_audit_idx').on(table.createdAt, table.userId),

    // Composite index for error tracking (connection_status + last_error presence)
    index('broker_accounts_error_tracking_idx').on(table.connectionStatus, table.lastError),

    // Partial index for connected accounts only (reduces index size)
    index('broker_accounts_connected_only_idx')
      .on(table.userId, table.brokerType, table.lastConnectedAt)
      .where(sql`${table.connectionStatus} = 'CONNECTED'`),

    // Partial index for admin accounts only
    index('broker_accounts_admin_only_idx')
      .on(table.brokerType, table.connectionStatus, table.lastConnectedAt)
      .where(sql`${table.isAdminAccount} = true`),
  ],
);

// ==================== BROKER CONNECTION STATUS TABLE ====================

export const BrokerConnectionStatusTable = pgTable(
  'broker_connection_status',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    brokerAccountId: integer('broker_account_id')
      .notNull()
      .references(() => BrokerAccountTable.id, { onDelete: 'cascade' }),
    status: varchar('status', { length: 50 }).notNull(),
    statusChangedAt: timestamp('status_changed_at').defaultNow().notNull(),
    errorMessage: text('error_message'),
    retryCount: integer('retry_count').default(0).notNull(),
    nextRetryAt: timestamp('next_retry_at'),
  },
  (table) => [
    // ==================== EXISTING INDEXES ====================
    index('connection_status_account_idx').on(table.brokerAccountId),
    index('connection_status_timestamp_idx').on(table.statusChangedAt),

    // ==================== PERFORMANCE OPTIMIZATION INDEXES ====================

    // Composite index for status monitoring queries (account + status + timestamp)
    index('connection_status_monitoring_idx').on(table.brokerAccountId, table.status, table.statusChangedAt),

    // Index for retry scheduling queries
    index('connection_status_retry_schedule_idx').on(table.nextRetryAt, table.retryCount),

    // Composite index for error analysis (status + error presence + timestamp)
    index('connection_status_error_analysis_idx').on(table.status, table.errorMessage, table.statusChangedAt),

    // Index for recent status changes (last 24 hours queries)
    index('connection_status_recent_idx').on(table.statusChangedAt, table.status),

    // Partial index for failed connections only
    index('connection_status_failed_only_idx')
      .on(table.brokerAccountId, table.statusChangedAt, table.retryCount)
      .where(sql`${table.status} IN ('FAILED', 'ERROR', 'DISCONNECTED')`),

    // Partial index for pending retries
    index('connection_status_pending_retry_idx')
      .on(table.nextRetryAt, table.brokerAccountId)
      .where(sql`${table.nextRetryAt} IS NOT NULL`),
  ],
);

// ==================== BROKER HEALTH STATISTICS TABLE ====================

export const BrokerHealthStatsTable = pgTable(
  'broker_health_stats',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    brokerAccountId: integer('broker_account_id')
      .notNull()
      .references(() => BrokerAccountTable.id, { onDelete: 'cascade' }),

    // Connection metrics
    totalConnections: integer('total_connections').default(0).notNull(),
    successfulConnections: integer('successful_connections').default(0).notNull(),
    failedConnections: integer('failed_connections').default(0).notNull(),
    currentUptimeSeconds: integer('current_uptime_seconds').default(0).notNull(),
    totalDowntimeSeconds: integer('total_downtime_seconds').default(0).notNull(),

    // Performance metrics
    averageResponseTimeMs: decimal('average_response_time_ms', { precision: 10, scale: 2 }),
    lastHeartbeatAt: timestamp('last_heartbeat_at'),
    lastErrorAt: timestamp('last_error_at'),
    lastErrorMessage: text('last_error_message'),

    // Timestamps
    statsDate: date('stats_date').defaultNow().notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => [
    // ==================== EXISTING INDEXES ====================
    index('health_stats_account_idx').on(table.brokerAccountId),
    index('health_stats_date_idx').on(table.statsDate),
    uniqueIndex('health_stats_account_date_unique_idx').on(table.brokerAccountId, table.statsDate),

    // ==================== PERFORMANCE OPTIMIZATION INDEXES ====================

    // Composite index for date range queries with account filtering
    index('health_stats_date_range_idx').on(table.statsDate, table.brokerAccountId, table.updatedAt),

    // Index for performance monitoring (response time analysis)
    index('health_stats_performance_idx').on(table.averageResponseTimeMs, table.statsDate),

    // Index for uptime analysis
    index('health_stats_uptime_idx').on(table.currentUptimeSeconds, table.totalDowntimeSeconds, table.statsDate),

    // Index for error tracking and analysis
    index('health_stats_error_tracking_idx').on(table.lastErrorAt, table.brokerAccountId),

    // Index for heartbeat monitoring
    index('health_stats_heartbeat_idx').on(table.lastHeartbeatAt, table.brokerAccountId),

    // Composite index for success rate calculations
    index('health_stats_success_rate_idx').on(table.successfulConnections, table.totalConnections, table.statsDate),

    // Index for recent statistics (last 30 days)
    index('health_stats_recent_idx').on(table.statsDate, table.updatedAt),

    // Partial index for accounts with errors only
    index('health_stats_with_errors_idx')
      .on(table.brokerAccountId, table.lastErrorAt, table.statsDate)
      .where(sql`${table.lastErrorAt} IS NOT NULL`),

    // Partial index for high-performance accounts (low response time)
    index('health_stats_high_performance_idx')
      .on(table.brokerAccountId, table.averageResponseTimeMs, table.statsDate)
      .where(sql`${table.averageResponseTimeMs} < 1000`), // Less than 1 second
  ],
);

// ==================== PERFORMANCE MONITORING VIEWS ====================

/**
 * SQL view definitions for common performance queries
 * These will be created as database views for optimized reporting
 */
export const PerformanceViews = {
  // Active broker accounts summary
  activeBrokerAccountsSummary: `
    CREATE OR REPLACE VIEW active_broker_accounts_summary AS
    SELECT 
      ba.broker_type,
      COUNT(*) as total_accounts,
      COUNT(CASE WHEN ba.connection_status = 'CONNECTED' THEN 1 END) as connected_accounts,
      COUNT(CASE WHEN ba.connection_status = 'FAILED' THEN 1 END) as failed_accounts,
      COUNT(CASE WHEN ba.is_admin_account = true THEN 1 END) as admin_accounts,
      AVG(EXTRACT(EPOCH FROM (NOW() - ba.last_connected_at))/3600) as avg_hours_since_connection
    FROM broker_accounts ba
    WHERE ba.deleted_at IS NULL
    GROUP BY ba.broker_type;
  `,

  // Daily health statistics summary
  dailyHealthStatsSummary: `
    CREATE OR REPLACE VIEW daily_health_stats_summary AS
    SELECT 
      bhs.stats_date,
      COUNT(DISTINCT bhs.broker_account_id) as accounts_tracked,
      AVG(bhs.average_response_time_ms) as avg_response_time,
      SUM(bhs.total_connections) as total_connections,
      SUM(bhs.successful_connections) as successful_connections,
      SUM(bhs.failed_connections) as failed_connections,
      AVG(bhs.current_uptime_seconds) as avg_uptime_seconds,
      COUNT(CASE WHEN bhs.last_error_at IS NOT NULL THEN 1 END) as accounts_with_errors
    FROM broker_health_stats bhs
    GROUP BY bhs.stats_date
    ORDER BY bhs.stats_date DESC;
  `,

  // Connection status timeline
  connectionStatusTimeline: `
    CREATE OR REPLACE VIEW connection_status_timeline AS
    SELECT 
      bcs.broker_account_id,
      ba.broker_type,
      ba.account_name,
      bcs.status,
      bcs.status_changed_at,
      bcs.error_message,
      bcs.retry_count,
      LAG(bcs.status) OVER (PARTITION BY bcs.broker_account_id ORDER BY bcs.status_changed_at) as previous_status,
      EXTRACT(EPOCH FROM (bcs.status_changed_at - LAG(bcs.status_changed_at) OVER (PARTITION BY bcs.broker_account_id ORDER BY bcs.status_changed_at)))/60 as minutes_in_previous_status
    FROM broker_connection_status bcs
    JOIN broker_accounts ba ON bcs.broker_account_id = ba.id
    WHERE ba.deleted_at IS NULL
    ORDER BY bcs.broker_account_id, bcs.status_changed_at DESC;
  `,
};

// ==================== PERFORMANCE OPTIMIZATION QUERIES ====================

/**
 * Pre-defined optimized queries for common operations
 */
export const OptimizedQueries = {
  // Get active accounts with latest health stats
  getActiveAccountsWithHealth: `
    SELECT 
      ba.id,
      ba.user_id,
      ba.broker_type,
      ba.account_name,
      ba.connection_status,
      ba.last_connected_at,
      bhs.average_response_time_ms,
      bhs.current_uptime_seconds,
      bhs.last_heartbeat_at
    FROM broker_accounts ba
    LEFT JOIN LATERAL (
      SELECT * FROM broker_health_stats 
      WHERE broker_account_id = ba.id 
      ORDER BY stats_date DESC 
      LIMIT 1
    ) bhs ON true
    WHERE ba.deleted_at IS NULL 
      AND ba.connection_status = 'CONNECTED'
    ORDER BY ba.last_connected_at DESC;
  `,

  // Get accounts requiring attention (errors, long disconnection)
  getAccountsRequiringAttention: `
    SELECT 
      ba.id,
      ba.user_id,
      ba.broker_type,
      ba.account_name,
      ba.connection_status,
      ba.last_connected_at,
      ba.last_error,
      bcs.retry_count,
      bcs.next_retry_at,
      EXTRACT(EPOCH FROM (NOW() - ba.last_connected_at))/3600 as hours_disconnected
    FROM broker_accounts ba
    LEFT JOIN LATERAL (
      SELECT * FROM broker_connection_status 
      WHERE broker_account_id = ba.id 
      ORDER BY status_changed_at DESC 
      LIMIT 1
    ) bcs ON true
    WHERE ba.deleted_at IS NULL 
      AND (
        ba.connection_status IN ('FAILED', 'ERROR', 'DISCONNECTED')
        OR ba.last_connected_at < NOW() - INTERVAL '1 hour'
        OR bcs.retry_count > 3
      )
    ORDER BY ba.last_connected_at ASC;
  `,

  // Get broker performance summary for dashboard
  getBrokerPerformanceSummary: `
    SELECT 
      ba.broker_type,
      COUNT(*) as total_accounts,
      COUNT(CASE WHEN ba.connection_status = 'CONNECTED' THEN 1 END) as connected_count,
      ROUND(AVG(bhs.average_response_time_ms), 2) as avg_response_time,
      ROUND(AVG(bhs.current_uptime_seconds)/3600, 2) as avg_uptime_hours,
      COUNT(CASE WHEN bhs.last_error_at > NOW() - INTERVAL '24 hours' THEN 1 END) as recent_errors
    FROM broker_accounts ba
    LEFT JOIN LATERAL (
      SELECT * FROM broker_health_stats 
      WHERE broker_account_id = ba.id 
      ORDER BY stats_date DESC 
      LIMIT 1
    ) bhs ON true
    WHERE ba.deleted_at IS NULL
    GROUP BY ba.broker_type
    ORDER BY connected_count DESC;
  `,
};

// ==================== TYPE DEFINITIONS ====================

// Import enums from common constants
export { BrokerTypeEnum, BrokerConnectionStatusEnum } from '@app/common/constants';
export type { BrokerType, BrokerConnectionStatus } from '@app/common/constants';

// TypeScript types for the tables
export type BrokerAccount = typeof BrokerAccountTable.$inferSelect;
export type NewBrokerAccount = typeof BrokerAccountTable.$inferInsert;

export type BrokerConnectionStatusRecord = typeof BrokerConnectionStatusTable.$inferSelect;
export type NewBrokerConnectionStatusRecord = typeof BrokerConnectionStatusTable.$inferInsert;

export type BrokerHealthStats = typeof BrokerHealthStatsTable.$inferSelect;
export type NewBrokerHealthStats = typeof BrokerHealthStatsTable.$inferInsert;

// Performance monitoring types
export interface ActiveAccountsSummary {
  brokerType: string;
  totalAccounts: number;
  connectedAccounts: number;
  failedAccounts: number;
  adminAccounts: number;
  avgHoursSinceConnection: number;
}

export interface DailyHealthSummary {
  statsDate: string;
  accountsTracked: number;
  avgResponseTime: number;
  totalConnections: number;
  successfulConnections: number;
  failedConnections: number;
  avgUptimeSeconds: number;
  accountsWithErrors: number;
}

export interface ConnectionStatusTimelineEntry {
  brokerAccountId: number;
  brokerType: string;
  accountName: string;
  status: string;
  statusChangedAt: Date;
  errorMessage?: string;
  retryCount: number;
  previousStatus?: string;
  minutesInPreviousStatus?: number;
}

export interface AccountRequiringAttention {
  id: number;
  userId: string;
  brokerType: string;
  accountName: string;
  connectionStatus: string;
  lastConnectedAt?: Date;
  lastError?: string;
  retryCount?: number;
  nextRetryAt?: Date;
  hoursDisconnected?: number;
}

export interface BrokerPerformanceSummary {
  brokerType: string;
  totalAccounts: number;
  connectedCount: number;
  avgResponseTime: number;
  avgUptimeHours: number;
  recentErrors: number;
}
