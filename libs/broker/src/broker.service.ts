import { Injectable, Logger } from '@nestjs/common';
import {
  BrokerRepository,
  type BrokerAccount,
  type CreateBrokerAccountData as CreateBrokerAccountRepositoryData,
  type UpdateBrokerAccountData as UpdateBrokerAccountRepositoryData,
} from './broker.repository';
import { BrokerError, BrokerErrorEnum } from './broker.error';
import { BrokerType, BrokerConnectionStatus } from '@app/common/constants';
import { ErrorDomainEnum } from '@app/common/errors';
import { AuditLoggingService } from '@app/common/security';
import { BROKER_CONFIG } from './broker.constants';
import type {
  CreateBrokerAccountData,
  UpdateBrokerAccountData,
  BrokerAccountFilters,
  BrokerCredentials,
  BrokerAccountResponse,
  ConnectionHealth,
} from './broker.types';
import { DateTimeUtilsService } from '@app/utils/datetime-utils.service';

/**
 * Broker service implementing core business logic for broker account management
 *
 * Requirements:
 * - 1.1: Admin role validation for broker account operations
 * - 1.2: Secure broker account lifecycle management
 * - 1.5: CRUD operations with proper error handling
 * - 7.1: NestJS framework with dependency injection
 * - 7.6: Comprehensive unit tests with mocked dependencies
 */
@Injectable()
export class BrokerService {
  private readonly logger = new Logger(BrokerService.name);

  constructor(
    private readonly brokerRepository: BrokerRepository,
    private readonly auditService: AuditLoggingService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  // ==================== BROKER ACCOUNT LIFECYCLE MANAGEMENT ====================

  /**
   * Create a new broker account with encrypted credentials
   * @param data - Broker account creation data
   * @returns Promise<BrokerAccountResponse> - Created broker account (without credentials)
   */
  async createAccount(data: CreateBrokerAccountData, correlationId?: string): Promise<BrokerAccountResponse> {
    const context = this.auditService.createLogContext('CREATE_ACCOUNT', {
      userId: data.userId,
      correlationId,
      metadata: {
        accountName: data.accountName,
        isAdminAccount: data.isAdminAccount,
        brokerType: data.brokerType,
      },
    });

    this.auditService.logOperationStart(context, 'BROKER');

    try {
      // Validate broker type is supported
      this.validateBrokerType(data.brokerType);

      // Validate credentials format
      this.validateCredentialsFormat(data.brokerType, data.credentials);

      // Check for duplicate account
      const existingAccount = await this.brokerRepository.findByUserIdAndBrokerType(data.userId, data.brokerType);
      if (existingAccount) {
        throw new BrokerError(BrokerErrorEnum.enum.DUPLICATE_ACCOUNT, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account already exists for user ${data.userId} and broker ${data.brokerType}`,
          context: {
            correlationId: context.correlationId,
            userId: data.userId,
            brokerType: data.brokerType,
            operation: 'CREATE_ACCOUNT',
          },
          isRetryable: false,
          severity: 'LOW',
        });
      }

      // Create account data for repository
      const createData: CreateBrokerAccountRepositoryData = {
        userId: data.userId,
        brokerType: data.brokerType,
        accountName: data.accountName,
        isAdminAccount: data.isAdminAccount || false,
        apiKey: data.credentials.apiKey,
        apiSecret: data.credentials.apiSecret,
        accessToken: data.credentials.accessToken,
        refreshToken: data.credentials.refreshToken,
      };

      // Create account in repository with retry logic
      const createdAccount = await this.brokerRepository.create(createData);

      // Log audit event for account creation
      this.auditService.logAuditEvent({
        eventType: 'ACCOUNT_CREATED',
        userId: data.userId,
        accountId: createdAccount.id.toString(),
        details: {
          brokerType: data.brokerType,
          accountName: data.accountName,
          isAdminAccount: data.isAdminAccount,
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        correlationId: context.correlationId,
        timestamp: this.dateTimeUtils.getNewDate(),
        severity: 'MEDIUM',
        category: 'OPERATION',
        module: 'BROKER',
      });

      // Return response without credentials
      return this.mapToResponse(createdAccount);
    } catch (error) {
      const brokerError =
        error instanceof BrokerError
          ? error.withContext({
              correlationId: context.correlationId,
              userId: data.userId,
              brokerType: data.brokerType,
              operation: 'CREATE_ACCOUNT',
            })
          : new BrokerError(BrokerErrorEnum.enum.ACCOUNT_CREATION_FAILED, ErrorDomainEnum.enum.BROKER, {
              message: 'Failed to create broker account',
              cause: error,
              context: {
                correlationId: context.correlationId,
                userId: data.userId,
                brokerType: data.brokerType,
                operation: 'CREATE_ACCOUNT',
              },
            });

      this.auditService.logOperationError(context, brokerError, 'BROKER');
      throw brokerError;
    }
  }

  /**
   * Find broker account by ID
   * @param id - Account ID
   * @returns Promise<BrokerAccountResponse | null> - Found account or null
   */
  async findAccountById(id: number): Promise<BrokerAccountResponse | null> {
    try {
      this.logger.log(`Finding broker account by ID: ${id}`);

      const account = await this.brokerRepository.findById(id);
      if (!account) {
        return null;
      }

      return this.mapToResponse(account);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to find broker account by ID ${id}: ${errorMessage}`, error);
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to find broker account with ID: ${id}`,
        cause: error,
      });
    }
  }

  /**
   * Find broker accounts by user ID
   * @param userId - User ID
   * @returns Promise<BrokerAccountResponse[]> - User's broker accounts
   */
  async findAccountsByUserId(userId: string): Promise<BrokerAccountResponse[]> {
    try {
      this.logger.log(`Finding broker accounts for user: ${userId}`);

      const accounts = await this.brokerRepository.findByUserId(userId);
      return accounts.map((account) => this.mapToResponse(account));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to find broker accounts for user ${userId}: ${errorMessage}`, error);
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to find broker accounts for user: ${userId}`,
        cause: error,
      });
    }
  }

  /**
   * Find broker account by user ID and broker type
   * @param userId - User ID
   * @param brokerType - Broker type
   * @returns Promise<BrokerAccountResponse | null> - Found account or null
   */
  async findAccountByUserIdAndBrokerType(
    userId: string,
    brokerType: BrokerType,
  ): Promise<BrokerAccountResponse | null> {
    try {
      this.logger.log(`Finding broker account for user: ${userId}, broker: ${brokerType}`);

      const account = await this.brokerRepository.findByUserIdAndBrokerType(userId, brokerType);
      if (!account) {
        return null;
      }

      return this.mapToResponse(account);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to find broker account for user ${userId} and broker ${brokerType}: ${errorMessage}`,
        error,
      );
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to find broker account for user ${userId} and broker ${brokerType}`,
        cause: error,
      });
    }
  }

  /**
   * Update broker account
   * @param id - Account ID
   * @param updates - Update data
   * @returns Promise<BrokerAccountResponse> - Updated account
   */
  async updateAccount(id: number, updates: UpdateBrokerAccountData): Promise<BrokerAccountResponse> {
    try {
      this.logger.log(`Updating broker account with ID: ${id}`);

      // Validate account exists
      const existingAccount = await this.brokerRepository.findById(id);
      if (!existingAccount) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account with ID ${id} not found`,
        });
      }

      // Validate credentials if provided
      if (updates.credentials) {
        this.validateCredentialsFormat(existingAccount.brokerType as BrokerType, updates.credentials);
      }

      // Prepare update data
      const updateData: UpdateBrokerAccountRepositoryData = {
        accountName: updates.accountName,
        apiKey: updates.credentials?.apiKey,
        apiSecret: updates.credentials?.apiSecret,
        accessToken: updates.credentials?.accessToken,
        refreshToken: updates.credentials?.refreshToken,
        connectionStatus: updates.connectionStatus,
        lastConnectedAt: updates.lastConnectedAt,
        lastError: updates.lastError,
      };

      // Update account in repository
      const updatedAccount = await this.brokerRepository.update(id, updateData);

      this.logger.log(`Successfully updated broker account with ID: ${id}`);

      return this.mapToResponse(updatedAccount);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to update broker account ${id}: ${errorMessage}`, error);
      if (error instanceof BrokerError) {
        throw error;
      }
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_UPDATE_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to update broker account with ID: ${id}`,
        cause: error,
      });
    }
  }

  /**
   * Delete broker account
   * @param id - Account ID
   * @returns Promise<void>
   */
  async deleteAccount(id: number): Promise<void> {
    try {
      this.logger.log(`Deleting broker account with ID: ${id}`);

      // Validate account exists
      const existingAccount = await this.brokerRepository.findById(id);
      if (!existingAccount) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account with ID ${id} not found`,
        });
      }

      // Delete account from repository
      await this.brokerRepository.delete(id);

      this.logger.log(`Successfully deleted broker account with ID: ${id}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to delete broker account ${id}: ${errorMessage}`, error);
      if (error instanceof BrokerError) {
        throw error;
      }
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_DELETION_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to delete broker account with ID: ${id}`,
        cause: error,
      });
    }
  }

  /**
   * Find broker accounts with filtering
   * @param filters - Filter criteria
   * @returns Promise<BrokerAccountResponse[]> - Filtered broker accounts
   */
  async findAccountsWithFilters(filters: BrokerAccountFilters): Promise<BrokerAccountResponse[]> {
    try {
      this.logger.log(`Finding broker accounts with filters:`, filters);

      const accounts = await this.brokerRepository.findWithFilters(filters);
      return accounts.map((account) => this.mapToResponse(account));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to find broker accounts with filters: ${errorMessage}`, error);
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
        message: 'Failed to find broker accounts with filters',
        cause: error,
      });
    }
  }

  /**
   * Find all admin broker accounts
   * @returns Promise<BrokerAccountResponse[]> - Admin broker accounts
   */
  async findAdminAccounts(): Promise<BrokerAccountResponse[]> {
    try {
      this.logger.log('Finding admin broker accounts');

      const accounts = await this.brokerRepository.findAdminAccounts();
      return accounts.map((account) => this.mapToResponse(account));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to find admin broker accounts: ${errorMessage}`, error);
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
        message: 'Failed to find admin broker accounts',
        cause: error,
      });
    }
  }

  /**
   * Find active broker accounts
   * @returns Promise<BrokerAccountResponse[]> - Active broker accounts
   */
  async findActiveAccounts(): Promise<BrokerAccountResponse[]> {
    try {
      this.logger.log('Finding active broker accounts');

      const accounts = await this.brokerRepository.findActiveAccounts();
      return accounts.map((account) => this.mapToResponse(account));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to find active broker accounts: ${errorMessage}`, error);
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
        message: 'Failed to find active broker accounts',
        cause: error,
      });
    }
  }

  // ==================== CREDENTIAL MANAGEMENT ====================

  /**
   * Get decrypted credentials for a broker account
   * @param id - Account ID
   * @returns Promise<BrokerCredentials> - Decrypted credentials
   */
  async getAccountCredentials(id: number): Promise<BrokerCredentials> {
    try {
      this.logger.log(`Getting credentials for broker account: ${id}`);

      const account = await this.brokerRepository.findById(id);
      if (!account) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account with ID ${id} not found`,
        });
      }

      return {
        apiKey: account.apiKey,
        apiSecret: account.apiSecret,
        accessToken: account.accessToken,
        refreshToken: account.refreshToken,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to get credentials for account ${id}: ${errorMessage}`, error);
      if (error instanceof BrokerError) {
        throw error;
      }
      throw new BrokerError(BrokerErrorEnum.enum.DECRYPTION_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to get credentials for account ${id}`,
        cause: error,
      });
    }
  }

  /**
   * Update credentials for a broker account
   * @param id - Account ID
   * @param credentials - New credentials
   * @returns Promise<void>
   */
  async updateAccountCredentials(id: number, credentials: Partial<BrokerCredentials>): Promise<void> {
    try {
      this.logger.log(`Updating credentials for broker account: ${id}`);

      const account = await this.brokerRepository.findById(id);
      if (!account) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account with ID ${id} not found`,
        });
      }

      // Validate credentials if provided
      if (credentials.apiKey || credentials.apiSecret) {
        this.validateCredentialsFormat(account.brokerType as BrokerType, credentials as BrokerCredentials);
      }

      // Update credentials
      await this.updateAccount(id, { credentials });

      this.logger.log(`Successfully updated credentials for broker account: ${id}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to update credentials for account ${id}: ${errorMessage}`, error);
      if (error instanceof BrokerError) {
        throw error;
      }
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_UPDATE_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to update credentials for account ${id}`,
        cause: error,
      });
    }
  }

  // ==================== CREDENTIAL VALIDATION ====================

  /**
   * Validate broker credentials format and basic requirements
   * @param brokerType - Broker type
   * @param credentials - Credentials to validate
   * @returns Promise<boolean> - True if valid
   */
  validateBrokerCredentials(brokerType: BrokerType, credentials: BrokerCredentials): boolean {
    try {
      this.logger.log(`Validating credentials for broker: ${brokerType}`);

      // Validate broker type is supported
      this.validateBrokerType(brokerType);

      // Validate credentials format
      this.validateCredentialsFormat(brokerType, credentials);

      // Additional broker-specific validation can be added here
      // For now, we'll just validate the format and return true
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.warn(`Credential validation failed for broker ${brokerType}: ${errorMessage}`);
      return false;
    }
  }

  // ==================== CONNECTION STATUS MANAGEMENT ====================

  /**
   * Update connection status for a broker account
   * @param id - Account ID
   * @param status - New connection status
   * @param error - Optional error message
   * @returns Promise<void>
   */
  async updateConnectionStatus(id: number, status: BrokerConnectionStatus, error?: string): Promise<void> {
    try {
      this.logger.log(`Updating connection status for account ${id} to: ${status}`);

      const updateData: UpdateBrokerAccountData = {
        connectionStatus: status,
        lastError: error,
      };

      if (status === 'CONNECTED') {
        updateData.lastConnectedAt = this.dateTimeUtils.getNewDate();
      }

      await this.updateAccount(id, updateData);

      this.logger.log(`Successfully updated connection status for account ${id}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to update connection status for account ${id}: ${errorMessage}`, error);
      throw error;
    }
  }

  /**
   * Get connection health for a broker account
   * @param id - Account ID
   * @returns Promise<ConnectionHealth> - Connection health information
   */
  async getConnectionHealth(id: number): Promise<ConnectionHealth> {
    try {
      this.logger.log(`Getting connection health for account: ${id}`);

      const account = await this.brokerRepository.findById(id);
      if (!account) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account with ID ${id} not found`,
        });
      }

      // Calculate uptime (simplified - in real implementation, this would come from health stats)
      const now = this.dateTimeUtils.getNewDate();
      const lastConnected = account.lastConnectedAt || this.dateTimeUtils.getNewDate(account.createdAt);
      const lastConnectedDate =
        typeof lastConnected === 'string' ? this.dateTimeUtils.getNewDate(lastConnected) : lastConnected;
      const uptime =
        account.connectionStatus === 'CONNECTED' ? Math.floor((now.getTime() - lastConnectedDate.getTime()) / 1000) : 0;

      return {
        accountId: id.toString(),
        status: account.connectionStatus as BrokerConnectionStatus,
        lastCheckedAt: now,
        errorMessage: account.lastError || undefined,
        uptime,
        failureCount: 0, // This would come from health stats in real implementation
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to get connection health for account ${id}: ${errorMessage}`, error);
      if (error instanceof BrokerError) {
        throw error;
      }
      throw new BrokerError(BrokerErrorEnum.enum.CONNECTION_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to get connection health for account ${id}`,
        cause: error,
      });
    }
  }

  // ==================== TOKEN MANAGEMENT ====================

  /**
   * Refresh tokens for a broker account
   * @param accountId - Account ID
   * @returns Promise<void>
   */
  async refreshTokens(accountId: number): Promise<void> {
    try {
      this.logger.log(`Refreshing tokens for account: ${accountId}`);

      const account = await this.brokerRepository.findById(accountId);
      if (!account) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account with ID ${accountId} not found`,
        });
      }

      // This is a placeholder - actual token refresh logic would be implemented
      // in the BrokerAuthService and called from here
      this.logger.log(`Token refresh for account ${accountId} - implementation pending`);

      // For now, we'll just log that the refresh was attempted
      // In real implementation, this would:
      // 1. Call the appropriate broker's token refresh API
      // 2. Update the stored tokens
      // 3. Update connection status if needed
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to refresh tokens for account ${accountId}: ${errorMessage}`, error);
      if (error instanceof BrokerError) {
        throw error;
      }
      throw new BrokerError(BrokerErrorEnum.enum.TOKEN_REFRESH_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to refresh tokens for account ${accountId}`,
        cause: error,
      });
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Validate that broker type is supported
   * @param brokerType - Broker type to validate
   * @private
   */
  private validateBrokerType(brokerType: BrokerType): void {
    if (!BROKER_CONFIG[brokerType]) {
      throw new BrokerError(BrokerErrorEnum.enum.UNSUPPORTED_OPERATION, ErrorDomainEnum.enum.BROKER, {
        message: `Unsupported broker type: ${brokerType}`,
      });
    }
  }

  /**
   * Validate credentials format for specific broker type
   * @param brokerType - Broker type
   * @param credentials - Credentials to validate
   * @private
   */
  private validateCredentialsFormat(brokerType: BrokerType, credentials: Partial<BrokerCredentials>): void {
    // Basic validation - all brokers need API key and secret
    if (!credentials.apiKey || !credentials.apiSecret) {
      throw new BrokerError(BrokerErrorEnum.enum.INVALID_CREDENTIALS, ErrorDomainEnum.enum.BROKER, {
        message: 'API key and secret are required',
      });
    }

    // Validate API key format
    if (typeof credentials.apiKey !== 'string' || credentials.apiKey.trim().length === 0) {
      throw new BrokerError(BrokerErrorEnum.enum.INVALID_CREDENTIALS, ErrorDomainEnum.enum.BROKER, {
        message: 'Invalid API key format',
      });
    }

    // Validate API secret format
    if (typeof credentials.apiSecret !== 'string' || credentials.apiSecret.trim().length === 0) {
      throw new BrokerError(BrokerErrorEnum.enum.INVALID_CREDENTIALS, ErrorDomainEnum.enum.BROKER, {
        message: 'Invalid API secret format',
      });
    }

    // Broker-specific validation
    switch (brokerType) {
      case 'ZERODHA':
        // Zerodha API keys are typically 32 characters
        if (credentials.apiKey.length !== 32) {
          throw new BrokerError(BrokerErrorEnum.enum.INVALID_CREDENTIALS, ErrorDomainEnum.enum.BROKER, {
            message: 'Zerodha API key must be 32 characters long',
          });
        }
        break;

      case 'UPSTOX':
        // Upstox API keys have specific format requirements
        if (!credentials.apiKey.match(/^[a-f0-9-]+$/)) {
          throw new BrokerError(BrokerErrorEnum.enum.INVALID_CREDENTIALS, ErrorDomainEnum.enum.BROKER, {
            message: 'Invalid Upstox API key format',
          });
        }
        break;

      // Add validation for other brokers as needed
      default:
        // Generic validation for other brokers
        if (credentials.apiKey.length < 8) {
          throw new BrokerError(BrokerErrorEnum.enum.INVALID_CREDENTIALS, ErrorDomainEnum.enum.BROKER, {
            message: 'API key must be at least 8 characters long',
          });
        }
        break;
    }
  }

  /**
   * Map decrypted broker account to response format (without credentials)
   * @param account - Decrypted broker account
   * @returns BrokerAccountResponse - Response format
   * @private
   */
  private mapToResponse(account: BrokerAccount): BrokerAccountResponse {
    return {
      id: account.id,
      userId: account.userId,
      brokerType: account.brokerType as BrokerType,
      accountName: account.accountName,
      isAdminAccount: account.isAdminAccount,
      connectionStatus: account.connectionStatus as BrokerConnectionStatus,
      lastConnectedAt: account.lastConnectedAt
        ? this.dateTimeUtils.getNewDate(account.lastConnectedAt).toISOString()
        : undefined,
      lastError: account.lastError || undefined,
      createdAt: this.dateTimeUtils.getNewDate(account.createdAt).toISOString(),
      updatedAt: this.dateTimeUtils.getNewDate(account.updatedAt).toISOString(),
    };
  }
}
