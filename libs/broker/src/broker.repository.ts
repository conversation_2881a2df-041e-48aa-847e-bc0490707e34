import { Injectable } from '@nestjs/common';
import { eq, and, type InferSelectModel } from 'drizzle-orm';
import { BaseRepository } from '@app/common/repository';
import { IRepositoryWithUser } from '@app/common/repository';
import { DrizzleService } from '@app/core/drizzle';
import { UtilsService, DbUtilsService } from '@app/utils';
import { BrokerAccountTable } from './broker.model';
import { EncryptionService } from '@app/common/encryption';
import { BrokerError, BrokerErrorEnum } from './broker.error';
import { BrokerType, BrokerConnectionStatus } from '@app/common/constants';
import { ErrorDomainEnum } from '@app/common/errors';
import type { BrokerAccountFilters } from './broker.types';

// ==================== TYPE DEFINITIONS ====================

/**
 * Insert data type for broker account table
 */
type BrokerAccountInsert = typeof BrokerAccountTable.$inferInsert;

// ==================== TYPE DEFINITIONS ====================

/**
 * Broker account entity type (decrypted)
 */
export type BrokerAccount = InferSelectModel<typeof BrokerAccountTable> & {
  // Decrypted credential fields
  apiKey: string;
  apiSecret: string;
  accessToken?: string;
  refreshToken?: string;
};

/**
 * Raw broker account from database (encrypted)
 */
export type RawBrokerAccount = InferSelectModel<typeof BrokerAccountTable>;

/**
 * Data for creating a new broker account
 */
export interface CreateBrokerAccountData {
  userId: string;
  brokerType: BrokerType;
  accountName: string;
  isAdminAccount?: boolean;
  apiKey: string;
  apiSecret: string;
  accessToken?: string;
  refreshToken?: string;
}

/**
 * Data for updating a broker account
 */
export interface UpdateBrokerAccountData {
  accountName?: string;
  apiKey?: string;
  apiSecret?: string;
  accessToken?: string;
  refreshToken?: string;
  connectionStatus?: BrokerConnectionStatus;
  lastConnectedAt?: Date;
  lastError?: string;
}

/**
 * Broker account repository with automatic encryption/decryption
 *
 * Requirements:
 * - 1.3: Encrypted credential storage with automatic encryption/decryption
 * - 1.5: CRUD operations with proper error handling
 * - 7.4: Drizzle ORM with proper repository patterns
 */
@Injectable()
export class BrokerRepository
  extends BaseRepository<BrokerAccount, CreateBrokerAccountData, UpdateBrokerAccountData, number>
  implements IRepositoryWithUser<BrokerAccount, CreateBrokerAccountData, UpdateBrokerAccountData, number>
{
  constructor(
    drizzleService: DrizzleService,
    utilsService: UtilsService,
    dbUtils: DbUtilsService,
    private readonly encryptionService: EncryptionService,
  ) {
    super(drizzleService, utilsService, dbUtils, BrokerRepository.name);
  }

  // ==================== ABSTRACT METHOD IMPLEMENTATIONS ====================

  protected getTable() {
    return BrokerAccountTable;
  }

  protected getTableName(): string {
    return 'broker_accounts';
  }

  // ==================== CRUD OPERATIONS WITH ENCRYPTION ====================

  /**
   * Create a new broker account with encrypted credentials
   * @param data - Broker account creation data
   * @returns Promise<BrokerAccount> - Created broker account (decrypted)
   */
  async create(data: CreateBrokerAccountData): Promise<BrokerAccount> {
    return this.executeWithErrorHandling('create', async () => {
      this.logOperation('create', { userId: data.userId, brokerType: data.brokerType });

      // Encrypt sensitive credentials
      const encryptedData = this.encryptCredentials(data);

      // Add audit fields
      const auditedData = this.addCreateAuditFields(encryptedData);

      // Insert into database
      const [created] = await this.getDb()
        .insert(BrokerAccountTable)
        .values(auditedData as BrokerAccountInsert)
        .returning();

      this.logger.log(`Created broker account with ID: ${created.id} for user: ${data.userId}`);

      // Return decrypted account
      return this.decryptAccount(created);
    });
  }

  /**
   * Find broker account by ID with decrypted credentials
   * Enhanced with caching for performance optimization
   * @param id - Account ID
   * @returns Promise<BrokerAccount | null> - Found account or null
   */
  async findById(id: number): Promise<BrokerAccount | null> {
    return this.executeWithErrorHandling('findById', async () => {
      this.logOperation('findById', { id });

      const [rawAccount] = await this.getDb().select().from(BrokerAccountTable).where(eq(BrokerAccountTable.id, id));

      if (!rawAccount) {
        return null;
      }

      return this.decryptAccount(rawAccount);
    });
  }

  /**
   * Update broker account with encrypted credentials
   * @param id - Account ID
   * @param updates - Update data
   * @returns Promise<BrokerAccount> - Updated account (decrypted)
   */
  async update(id: number, updates: UpdateBrokerAccountData): Promise<BrokerAccount> {
    return this.executeWithErrorHandling('update', async () => {
      this.logOperation('update', { id, updates: { ...updates, apiKey: '[REDACTED]', apiSecret: '[REDACTED]' } });

      // Validate entity exists
      await this.validateEntityExists(id);

      // Encrypt credentials if provided
      const encryptedUpdates = this.encryptCredentialUpdates(updates);

      // Add audit fields
      const auditedUpdates = this.addUpdateAuditFields(encryptedUpdates);

      // Update in database
      const [updated] = await this.getDb()
        .update(BrokerAccountTable)
        .set(auditedUpdates)
        .where(eq(BrokerAccountTable.id, id))
        .returning();

      this.logger.log(`Updated broker account with ID: ${id}`);

      // Return decrypted account
      return this.decryptAccount(updated);
    });
  }

  /**
   * Find broker accounts by user ID
   * Enhanced with caching and optimized query
   * @param userId - User ID
   * @returns Promise<BrokerAccount[]> - User's broker accounts (decrypted)
   */
  async findByUserId(userId: string): Promise<BrokerAccount[]> {
    return this.executeWithErrorHandling('findByUserId', async () => {
      this.logOperation('findByUserId', { userId });

      const rawAccounts = await this.getDb()
        .select()
        .from(BrokerAccountTable)
        .where(eq(BrokerAccountTable.userId, userId));

      // Decrypt all accounts
      const decryptedAccounts = rawAccounts.map((account) => this.decryptAccount(account));

      return decryptedAccounts;
    });
  }

  /**
   * Find broker account by user ID and broker type
   * @param userId - User ID
   * @param brokerType - Broker type
   * @returns Promise<BrokerAccount | null> - Found account or null
   */
  async findByUserIdAndBrokerType(userId: string, brokerType: BrokerType): Promise<BrokerAccount | null> {
    return this.executeWithErrorHandling('findByUserIdAndBrokerType', async () => {
      this.logOperation('findByUserIdAndBrokerType', { userId, brokerType });

      const [rawAccount] = await this.getDb()
        .select()
        .from(BrokerAccountTable)
        .where(and(eq(BrokerAccountTable.userId, userId), eq(BrokerAccountTable.brokerType, brokerType)))
        .limit(1);

      if (!rawAccount) {
        return null;
      }

      return this.decryptAccount(rawAccount);
    });
  }

  /**
   * Find all admin broker accounts
   * Enhanced with caching and optimized query
   * @returns Promise<BrokerAccount[]> - Admin broker accounts (decrypted)
   */
  async findAdminAccounts(): Promise<BrokerAccount[]> {
    return this.executeWithErrorHandling('findAdminAccounts', async () => {
      this.logOperation('findAdminAccounts');

      const rawAccounts = await this.getDb()
        .select()
        .from(BrokerAccountTable)
        .where(eq(BrokerAccountTable.isAdminAccount, true));

      // Decrypt all accounts
      const decryptedAccounts = rawAccounts.map((account) => this.decryptAccount(account));

      return decryptedAccounts;
    });
  }

  /**
   * Find broker accounts by broker type
   * @param brokerType - Broker type
   * @returns Promise<BrokerAccount[]> - Broker accounts (decrypted)
   */
  async findByBrokerType(brokerType: BrokerType): Promise<BrokerAccount[]> {
    return this.executeWithErrorHandling('findByBrokerType', async () => {
      this.logOperation('findByBrokerType', { brokerType });

      const rawAccounts = await this.getDb()
        .select()
        .from(BrokerAccountTable)
        .where(eq(BrokerAccountTable.brokerType, brokerType));

      // Decrypt all accounts
      const decryptedAccounts = rawAccounts.map((account) => this.decryptAccount(account));

      return decryptedAccounts;
    });
  }

  /**
   * Delete all broker accounts for a user
   * @param userId - User ID
   * @returns Promise<void>
   */
  async deleteByUserId(userId: string): Promise<void> {
    return this.executeWithErrorHandling('deleteByUserId', async () => {
      this.logOperation('deleteByUserId', { userId });

      await this.getDb().delete(BrokerAccountTable).where(eq(BrokerAccountTable.userId, userId));

      this.logger.log(`Deleted all broker accounts for user: ${userId}`);
    });
  }

  /**
   * Find broker accounts with filters
   * @param filters - Filter criteria
   * @returns Promise<BrokerAccount[]> - Filtered broker accounts (decrypted)
   */
  async findWithFilters(filters: BrokerAccountFilters): Promise<BrokerAccount[]> {
    return this.executeWithErrorHandling('findWithFilters', async () => {
      this.logOperation('findWithFilters', { ...filters });

      const db = this.getDb();
      let query = db.select().from(BrokerAccountTable);

      // Apply filters
      const conditions: unknown[] = [];

      if (filters.userId) {
        conditions.push(eq(BrokerAccountTable.userId, filters.userId));
      }

      if (filters.brokerType) {
        conditions.push(eq(BrokerAccountTable.brokerType, filters.brokerType));
      }

      if (filters.connectionStatus) {
        conditions.push(eq(BrokerAccountTable.connectionStatus, filters.connectionStatus));
      }

      if (filters.isAdminAccount !== undefined) {
        conditions.push(eq(BrokerAccountTable.isAdminAccount, filters.isAdminAccount));
      }

      if (conditions.length > 0) {
        query = query.where(and(...(conditions as Parameters<typeof and>))) as typeof query;
      }

      if (filters.limit) {
        query = query.limit(filters.limit) as typeof query;
      }

      if (filters.offset) {
        query = query.offset(filters.offset) as typeof query;
      }

      const rawAccounts = await query;

      // Decrypt all accounts
      const decryptedAccounts = rawAccounts.map((account) => this.decryptAccount(account));

      return decryptedAccounts;
    });
  }

  /**
   * Find all active broker accounts (connected status)
   * Enhanced with caching and optimized query
   * @returns Promise<BrokerAccount[]> - Active broker accounts (decrypted)
   */
  async findActiveAccounts(): Promise<BrokerAccount[]> {
    return this.executeWithErrorHandling('findActiveAccounts', async () => {
      this.logOperation('findActiveAccounts');

      const rawAccounts = await this.getDb()
        .select()
        .from(BrokerAccountTable)
        .where(eq(BrokerAccountTable.connectionStatus, 'CONNECTED'));

      // Decrypt all accounts
      const decryptedAccounts = rawAccounts.map((account) => this.decryptAccount(account));

      return decryptedAccounts;
    });
  }

  // ==================== PRIVATE ENCRYPTION METHODS ====================

  /**
   * Encrypt credentials in broker account data
   * @param data - Broker account data with plain credentials
   * @returns Promise<object> - Data with encrypted credentials
   * @private
   */
  private encryptCredentials(data: CreateBrokerAccountData): Record<string, unknown> {
    try {
      const encryptedApiKey = this.encryptionService.encrypt(data.apiKey);
      const encryptedApiSecret = this.encryptionService.encrypt(data.apiSecret);
      const encryptedAccessToken = data.accessToken ? this.encryptionService.encrypt(data.accessToken) : null;
      const encryptedRefreshToken = data.refreshToken ? this.encryptionService.encrypt(data.refreshToken) : null;

      return {
        userId: data.userId,
        brokerType: data.brokerType,
        accountName: data.accountName,
        isAdminAccount: data.isAdminAccount || false,
        encryptedApiKey,
        encryptedApiSecret,
        encryptedAccessToken,
        encryptedRefreshToken,
        connectionStatus: 'DISCONNECTED',
      };
    } catch (error) {
      this.logger.error('Failed to encrypt broker credentials:', error);
      throw new BrokerError(BrokerErrorEnum.enum.ENCRYPTION_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: 'Failed to encrypt broker credentials',
        cause: error,
      });
    }
  }

  /**
   * Encrypt credential updates
   * @param updates - Update data with potential plain credentials
   * @returns Promise<object> - Updates with encrypted credentials
   * @private
   */
  private encryptCredentialUpdates(updates: UpdateBrokerAccountData): Record<string, unknown> {
    try {
      const encryptedUpdates: Record<string, unknown> = { ...updates };

      if (updates.apiKey) {
        encryptedUpdates.encryptedApiKey = this.encryptionService.encrypt(updates.apiKey);
        delete encryptedUpdates.apiKey;
      }

      if (updates.apiSecret) {
        encryptedUpdates.encryptedApiSecret = this.encryptionService.encrypt(updates.apiSecret);
        delete encryptedUpdates.apiSecret;
      }

      if (updates.accessToken) {
        encryptedUpdates.encryptedAccessToken = this.encryptionService.encrypt(updates.accessToken);
        delete encryptedUpdates.accessToken;
      }

      if (updates.refreshToken) {
        encryptedUpdates.encryptedRefreshToken = this.encryptionService.encrypt(updates.refreshToken);
        delete encryptedUpdates.refreshToken;
      }

      return encryptedUpdates;
    } catch (error) {
      this.logger.error('Failed to encrypt credential updates:', error);
      throw new BrokerError(BrokerErrorEnum.enum.ENCRYPTION_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: 'Failed to encrypt credential updates',
        cause: error,
      });
    }
  }

  /**
   * Decrypt broker account credentials
   * @param rawAccount - Raw account from database with encrypted credentials
   * @returns Promise<BrokerAccount> - Account with decrypted credentials
   * @private
   */
  private decryptAccount(rawAccount: RawBrokerAccount): BrokerAccount {
    try {
      const apiKey = this.encryptionService.decrypt(rawAccount.encryptedApiKey);
      const apiSecret = this.encryptionService.decrypt(rawAccount.encryptedApiSecret);
      const accessToken = rawAccount.encryptedAccessToken
        ? this.encryptionService.decrypt(rawAccount.encryptedAccessToken)
        : undefined;
      const refreshToken = rawAccount.encryptedRefreshToken
        ? this.encryptionService.decrypt(rawAccount.encryptedRefreshToken)
        : undefined;

      return {
        ...rawAccount,
        apiKey,
        apiSecret,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      this.logger.error(`Failed to decrypt broker account ${rawAccount.id}:`, error);
      throw new BrokerError(BrokerErrorEnum.enum.DECRYPTION_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: 'Failed to decrypt broker account credentials',
        cause: error,
      });
    }
  }
}
