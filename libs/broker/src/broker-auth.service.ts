import { Injectable, Logger } from '@nestjs/common';
import { EnvService, ConfigService } from '@app/core';
import { BrokerError, BrokerErrorEnum } from './broker.error';
import { BrokerRepository, type UpdateBrokerAccountData } from './broker.repository';
import { AuditLoggingService } from '@app/common/security';
import type { OAuthInitiationResponse, OAuthCallbackResponse, OAuthTokens } from './broker.types';
import type { BrokerType } from '@app/common/constants';
import { KiteConnect } from 'kiteconnect';
import { randomBytes } from 'crypto';
import { ErrorDomainEnum } from '@app/common';
import { DateTimeUtilsService } from '@app/utils';

/**
 * BrokerAuthService handles OAuth2 authentication flows for supported brokers
 *
 * Requirements:
 * - 2.1: OAuth2 flow initiation with proper authorization URL
 * - 2.2: Token exchange from authorization code
 * - 2.3: Secure token storage with encryption
 * - 2.4: Automatic token refresh functionality
 * - 2.5: CSRF protection with state parameter validation
 * - 2.6: Token validation and expiration handling
 */
@Injectable()
export class BrokerAuthService {
  private readonly logger = new Logger(BrokerAuthService.name);
  private readonly oauthStates = new Map<
    string,
    { userId: string; expiresAt: Date; brokerType: BrokerType; accountId: number }
  >();

  constructor(
    private readonly envService: EnvService,
    private readonly configService: ConfigService,
    private readonly brokerRepository: BrokerRepository,
    private readonly auditService: AuditLoggingService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    // Start periodic cleanup of expired OAuth states
    this.startOAuthStateCleanup();
  }

  /**
   * Initiate OAuth2 flow for supported brokers
   * Requirement 2.1: OAuth2 flow initiation with proper authorization URL
   * Requirement 2.5: CSRF protection with state parameter validation
   */
  async initiateOAuth(accountId: number, userId: string): Promise<OAuthInitiationResponse> {
    this.logger.log(`Initiating OAuth flow for account: ${accountId}, user: ${userId}`);

    try {
      // Get broker account with credentials
      const account = await this.brokerRepository.findById(accountId);
      if (!account) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account not found: ${accountId}`,
        });
      }

      // Verify account belongs to user
      if (account.userId !== userId) {
        throw new BrokerError(BrokerErrorEnum.enum.INSUFFICIENT_PERMISSIONS, ErrorDomainEnum.enum.BROKER, {
          message: 'Access denied to broker account',
        });
      }

      // Generate secure state parameter for CSRF protection
      const state = this.generateSecureState();
      const expiresAt = this.dateTimeUtils.getNewDate(this.dateTimeUtils.getTime() + 10 * 60 * 1000); // 10 minutes

      // Store state for validation during callback
      this.oauthStates.set(state, { userId, expiresAt, brokerType: account.brokerType as BrokerType, accountId });

      let authorizationUrl: string;

      switch (account.brokerType) {
        case 'ZERODHA':
          authorizationUrl = await this.initiateZerodhaOAuth(state, account);
          break;
        default:
          throw new BrokerError(BrokerErrorEnum.enum.UNSUPPORTED_OPERATION, ErrorDomainEnum.enum.BROKER, {
            message: `OAuth not supported for broker: ${account.brokerType}`,
          });
      }

      this.logger.log(`OAuth initiation successful for ${account.brokerType}, state: ${state}`);

      // Log OAuth initiation audit event
      this.auditService.logAuditEvent({
        eventType: 'OAUTH_INITIATED',
        userId,
        accountId: accountId.toString(),
        details: {
          brokerType: account.brokerType,
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        correlationId: this.auditService.generateCorrelationId(),
        timestamp: this.dateTimeUtils.getNewDate(),
        severity: 'LOW',
        category: 'SECURITY',
        module: 'BROKER',
      });

      return {
        authorizationUrl,
        state,
        expiresAt,
      };
    } catch (error) {
      this.logger.error(`OAuth initiation failed for account ${accountId}:`, error);
      throw new BrokerError(BrokerErrorEnum.enum.OAUTH_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: `Failed to initiate OAuth for account ${accountId}`,
        cause: error,
      });
    }
  }

  /**
   * Handle OAuth2 callback and exchange authorization code for tokens
   */
  async handleOAuthCallback(code: string, state: string, _correlationId?: string): Promise<OAuthCallbackResponse> {
    this.logger.log(`Processing OAuth callback with state: ${state}`);

    try {
      // Validate state parameter for CSRF protection
      const stateData = this.validateOAuthState(state);
      const { brokerType, accountId } = stateData;

      let tokens: OAuthTokens;

      switch (brokerType) {
        case 'ZERODHA':
          tokens = await this.handleZerodhaCallback(code, accountId);
          break;
        default:
          throw new BrokerError(BrokerErrorEnum.enum.UNSUPPORTED_OPERATION, ErrorDomainEnum.enum.BROKER, {
            message: `OAuth callback not supported for broker: ${brokerType}`,
          });
      }

      // Update the existing account with OAuth tokens
      const updateData: UpdateBrokerAccountData = {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        connectionStatus: 'CONNECTED',
        lastConnectedAt: this.dateTimeUtils.getNewDate(),
        lastError: undefined,
      };

      await this.brokerRepository.update(accountId, updateData);

      this.logger.log(`OAuth callback completed successfully for account ${accountId}`);

      // Log OAuth completion audit event
      this.auditService.logAuditEvent({
        eventType: 'OAUTH_COMPLETED',
        userId: stateData.userId,
        accountId: accountId.toString(),
        details: {
          brokerType,
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        correlationId: _correlationId || this.auditService.generateCorrelationId(),
        timestamp: this.dateTimeUtils.getNewDate(),
        severity: 'LOW',
        category: 'SECURITY',
        module: 'BROKER',
      });

      return {
        success: true,
        accountId: accountId.toString(),
        message: `OAuth authentication completed successfully! Your ${brokerType} account has been updated with new credentials and is ready for trading.`,
        tokens: {
          expiresAt: tokens.expiresAt,
          tokenType: tokens.tokenType,
          scope: tokens.scope,
        },
      };
    } catch (error) {
      this.logger.error(`OAuth callback failed:`, error);
      throw error;
    }
  }

  /**
   * Refresh access token for a broker account
   */
  async refreshAccessToken(accountId: string): Promise<OAuthTokens> {
    this.logger.log(`Refreshing access token for account: ${accountId}`);

    try {
      const account = await this.brokerRepository.findById(parseInt(accountId));
      if (!account) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account not found: ${accountId}`,
        });
      }

      const credentials = {
        apiKey: account.apiKey,
        apiSecret: account.apiSecret,
        accessToken: account.accessToken,
        refreshToken: account.refreshToken,
      };

      if (!credentials.refreshToken) {
        throw new BrokerError(BrokerErrorEnum.enum.TOKEN_REFRESH_FAILED, ErrorDomainEnum.enum.BROKER, {
          message: 'No refresh token available for account',
        });
      }

      let tokens: OAuthTokens;

      switch (account.brokerType) {
        case 'ZERODHA':
          tokens = await this.refreshZerodhaToken(credentials);
          break;
        default:
          throw new BrokerError(BrokerErrorEnum.enum.UNSUPPORTED_OPERATION, ErrorDomainEnum.enum.BROKER, {
            message: `Token refresh not supported for broker: ${account.brokerType}`,
          });
      }

      // Update account with new tokens
      const updateData: UpdateBrokerAccountData = {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        lastConnectedAt: this.dateTimeUtils.getNewDate(),
      };

      await this.brokerRepository.update(parseInt(accountId), updateData);

      // Log token refresh audit event
      this.auditService.logAuditEvent({
        eventType: 'TOKEN_REFRESHED',
        userId: account.userId,
        accountId,
        details: {
          brokerType: account.brokerType,
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        correlationId: this.auditService.generateCorrelationId(),
        timestamp: this.dateTimeUtils.getNewDate(),
        severity: 'LOW',
        category: 'SECURITY',
        module: 'BROKER',
      });

      return tokens;
    } catch (error) {
      this.logger.error(`Token refresh failed for account ${accountId}:`, error);
      throw error;
    }
  }

  /**
   * Validate tokens for a broker account
   */
  async validateTokens(accountId: string): Promise<boolean> {
    this.logger.log(`Validating tokens for account: ${accountId}`);

    try {
      const account = await this.brokerRepository.findById(parseInt(accountId));
      if (!account) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account not found: ${accountId}`,
        });
      }

      const credentials = {
        apiKey: account.apiKey,
        apiSecret: account.apiSecret,
        accessToken: account.accessToken,
        refreshToken: account.refreshToken,
      };

      if (!credentials.accessToken) {
        return false;
      }

      let isValid: boolean;

      switch (account.brokerType) {
        case 'ZERODHA':
          isValid = await this.validateZerodhaTokens(credentials);
          break;
        default:
          throw new BrokerError(BrokerErrorEnum.enum.UNSUPPORTED_OPERATION, ErrorDomainEnum.enum.BROKER, {
            message: `Token validation not supported for broker: ${account.brokerType}`,
          });
      }

      return isValid;
    } catch (error) {
      this.logger.error(`Token validation failed for account ${accountId}:`, error);
      return false;
    }
  }

  /**
   * Revoke tokens for a broker account
   */
  async revokeTokens(accountId: string): Promise<void> {
    this.logger.log(`Revoking tokens for account: ${accountId}`);

    try {
      const account = await this.brokerRepository.findById(parseInt(accountId));
      if (!account) {
        throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
          message: `Broker account not found: ${accountId}`,
        });
      }

      const credentials = {
        apiKey: account.apiKey,
        apiSecret: account.apiSecret,
        accessToken: account.accessToken,
        refreshToken: account.refreshToken,
      };

      switch (account.brokerType) {
        case 'ZERODHA':
          await this.revokeZerodhaTokens(credentials);
          break;
        default:
          this.logger.warn(`Token revocation not implemented for broker: ${account.brokerType}`);
      }

      // Clear tokens from database
      const updateData: UpdateBrokerAccountData = {
        accessToken: undefined,
        refreshToken: undefined,
        connectionStatus: 'DISCONNECTED',
        lastError: undefined,
      };

      await this.brokerRepository.update(parseInt(accountId), updateData);

      // Log token revocation audit event
      this.auditService.logAuditEvent({
        eventType: 'TOKEN_REVOKED',
        userId: account.userId,
        accountId,
        details: {
          brokerType: account.brokerType,
          timestamp: this.dateTimeUtils.getUtcNow(),
        },
        correlationId: this.auditService.generateCorrelationId(),
        timestamp: this.dateTimeUtils.getNewDate(),
        severity: 'MEDIUM',
        category: 'SECURITY',
        module: 'BROKER',
      });

      this.logger.log(`Tokens revoked successfully for account: ${accountId}`);
    } catch (error) {
      this.logger.error(`Token revocation failed for account ${accountId}:`, error);
      throw error;
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Generate secure state parameter for CSRF protection
   */
  private generateSecureState(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Validate OAuth state parameter for CSRF protection
   */
  private validateOAuthState(state: string): {
    userId: string;
    expiresAt: Date;
    brokerType: BrokerType;
    accountId: number;
  } {
    const stateData = this.oauthStates.get(state);

    if (!stateData) {
      throw new BrokerError(BrokerErrorEnum.enum.INVALID_OAUTH_STATE, ErrorDomainEnum.enum.BROKER, {
        message: 'Invalid or expired OAuth state parameter',
      });
    }

    if (stateData.expiresAt < this.dateTimeUtils.getNewDate()) {
      this.oauthStates.delete(state);
      throw new BrokerError(BrokerErrorEnum.enum.INVALID_OAUTH_STATE, ErrorDomainEnum.enum.BROKER, {
        message: 'OAuth state parameter has expired',
      });
    }

    // Clean up used state
    this.oauthStates.delete(state);

    return stateData;
  }

  /**
   * Start periodic cleanup of expired OAuth states
   */
  private startOAuthStateCleanup(): void {
    setInterval(
      () => {
        const now = this.dateTimeUtils.getNewDate();
        for (const [state, data] of this.oauthStates.entries()) {
          if (data.expiresAt < now) {
            this.oauthStates.delete(state);
          }
        }
      },
      5 * 60 * 1000,
    ); // Clean up every 5 minutes
  }

  // ==================== ZERODHA-SPECIFIC METHODS ====================

  /**
   * Initiate Zerodha OAuth flow
   */
  private async initiateZerodhaOAuth(state: string, account: { apiKey: string }): Promise<string> {
    if (!account.apiKey) {
      throw new BrokerError(BrokerErrorEnum.enum.INVALID_CREDENTIALS, ErrorDomainEnum.enum.BROKER, {
        message: 'Zerodha API key not found in account',
      });
    }

    const redirectUri = await this.getZerodhaRedirectUri();
    const kite = this.getKiteConnectInstance(account.apiKey);

    const loginUrl = kite.getLoginURL();
    const url = new URL(loginUrl);
    url.searchParams.set('state', state);
    url.searchParams.set('redirect_params', redirectUri);

    return url.toString();
  }

  /**
   * Handle Zerodha OAuth callback
   */
  private async handleZerodhaCallback(code: string, accountId: number): Promise<OAuthTokens> {
    const account = await this.brokerRepository.findById(accountId);
    if (!account) {
      throw new BrokerError(BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND, ErrorDomainEnum.enum.BROKER, {
        message: `Broker account not found: ${accountId}`,
      });
    }

    if (!account.apiKey || !account.apiSecret) {
      throw new BrokerError(BrokerErrorEnum.enum.INVALID_CREDENTIALS, ErrorDomainEnum.enum.BROKER, {
        message: 'Zerodha API credentials not found in account',
      });
    }

    const kite = this.getKiteConnectInstance(account.apiKey);
    const session = await kite.generateSession(code, account.apiSecret);

    if (!session.access_token) {
      throw new BrokerError(BrokerErrorEnum.enum.OAUTH_FAILED, ErrorDomainEnum.enum.BROKER, {
        message: 'Zerodha authentication failed: No access token received',
      });
    }

    return {
      accessToken: session.access_token,
      refreshToken: session.refresh_token,
      expiresAt: this.dateTimeUtils.getNewDate(this.dateTimeUtils.getTime() + 24 * 60 * 60 * 1000), // 24 hours
      tokenType: 'Bearer',
      scope: 'read write',
    };
  }

  /**
   * Refresh Zerodha access token
   */
  private async refreshZerodhaToken(credentials: {
    apiKey: string;
    apiSecret: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<OAuthTokens> {
    const kite = this.getKiteConnectInstance(credentials.apiKey);
    const session = await kite.renewAccessToken(credentials.refreshToken!, credentials.apiSecret);

    return {
      accessToken: session.access_token,
      refreshToken: session.refresh_token || credentials.refreshToken,
      expiresAt: this.dateTimeUtils.getNewDate(this.dateTimeUtils.getTime() + 24 * 60 * 60 * 1000), // 24 hours
      tokenType: 'Bearer',
      scope: 'read write',
    };
  }

  /**
   * Validate Zerodha tokens
   */
  private async validateZerodhaTokens(credentials: {
    apiKey: string;
    apiSecret: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<boolean> {
    const kite = this.getKiteConnectInstance(credentials.apiKey, credentials.accessToken);

    try {
      await kite.getProfile();
      return true;
    } catch (error) {
      this.logger.debug('Zerodha token validation failed:', error);
      return false;
    }
  }

  /**
   * Revoke Zerodha tokens
   */
  private async revokeZerodhaTokens(credentials: {
    apiKey: string;
    apiSecret: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<void> {
    const kite = this.getKiteConnectInstance(credentials.apiKey, credentials.accessToken);

    try {
      await kite.invalidateAccessToken(credentials.accessToken!);
    } catch (error) {
      this.logger.warn('Failed to revoke Zerodha tokens:', error);
      // Don't throw error as token might already be invalid
    }
  }

  /**
   * Get KiteConnect instance for Zerodha operations
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getKiteConnectInstance(apiKey: string, accessToken?: string): any {
    const kite = new KiteConnect({
      api_key: apiKey,
    });

    if (accessToken) {
      kite.setAccessToken(accessToken);
    }

    return kite;
  }

  /**
   * Get Zerodha redirect URI from configuration
   */
  private async getZerodhaRedirectUri(): Promise<string> {
    const baseUrl = await this.configService.get<string>('API_BASE_URL').catch(() => 'http://localhost:3000');
    return `${baseUrl}/api/broker/callback`;
  }
}
