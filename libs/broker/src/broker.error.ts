import { BaseError, type ErrorDomainType } from '@app/common/errors';
import { z } from 'zod/v4';

export const BrokerErrorEnum = z.enum([
  // Connection and authentication errors
  'CONNECTION_FAILED',
  'AUTHENTICATION_FAILED',
  'INVALID_CREDENTIALS',
  'SESSION_EXPIRED',
  'API_RATE_LIMIT_EXCEEDED',
  'BROKER_SERVICE_UNAVAILABLE',
  'INVALID_BROKER_RESPONSE',
  'WEBSOCKET_CONNECTION_FAILED',
  'WEBSOCKET_DISCONNECTED',
  'SUBSCRIPTION_FAILED',
  'UNSUBSCRIPTION_FAILED',
  'SYMBOL_NOT_FOUND',
  'INVALID_SYMBOL_FORMAT',
  'UNSUPPORTED_OPERATION',
  'BROKER_MAINTENANCE',
  'NETWORK_ERROR',
  'TIMEOUT_ERROR',
  'UNKNOWN_BROKER_ERROR',

  // Encryption and security errors
  'ENCR<PERSON>TION_FAILED',
  'DECRYPTION_FAILED',
  'KEY_ROTATION_FAILED',
  'INVALID_ENCRYPTED_DATA',

  // Account management errors
  'ACCOUNT_NOT_FOUND',
  'DUPLICATE_ACCOUNT',
  'ACCOUNT_CREATION_FAILED',
  'ACCOUNT_UPDATE_FAILED',
  'ACCOUNT_DELETION_FAILED',
  'INSUFFICIENT_PERMISSIONS',

  // OAuth and token errors
  'OAUTH_FAILED',
  'TOKEN_EXPIRED',
  'TOKEN_REFRESH_FAILED',
  'INVALID_OAUTH_STATE',
  'OAUTH_CALLBACK_FAILED',

  // System errors
  'DATABASE_ERROR',
  'SYSTEM_ERROR',

  // Retry and circuit breaker errors
  'MAX_RETRIES_EXCEEDED',
  'CIRCUIT_BREAKER_OPEN',
  'OPERATION_TIMEOUT',
  'RETRY_EXHAUSTED',
]);

export const BrokerErrorMessages: Record<BrokerErrorEnumType, string> = {
  // Connection and authentication errors
  CONNECTION_FAILED: 'Failed to establish connection with broker',
  AUTHENTICATION_FAILED: 'Authentication with broker failed',
  INVALID_CREDENTIALS: 'Invalid broker credentials provided',
  SESSION_EXPIRED: 'Broker session has expired',
  API_RATE_LIMIT_EXCEEDED: 'API rate limit exceeded for broker requests',
  BROKER_SERVICE_UNAVAILABLE: 'Broker service is currently unavailable',
  INVALID_BROKER_RESPONSE: 'Received invalid response from broker',
  WEBSOCKET_CONNECTION_FAILED: 'Failed to establish WebSocket connection with broker',
  WEBSOCKET_DISCONNECTED: 'WebSocket connection with broker was disconnected',
  SUBSCRIPTION_FAILED: 'Failed to subscribe to market data',
  UNSUBSCRIPTION_FAILED: 'Failed to unsubscribe from market data',
  SYMBOL_NOT_FOUND: 'Symbol not found in broker symbol master',
  INVALID_SYMBOL_FORMAT: 'Invalid symbol format for broker',
  UNSUPPORTED_OPERATION: 'Operation not supported by broker',
  BROKER_MAINTENANCE: 'Broker is under maintenance',
  NETWORK_ERROR: 'Network error occurred while communicating with broker',
  TIMEOUT_ERROR: 'Request to broker timed out',
  UNKNOWN_BROKER_ERROR: 'Unknown error occurred with broker',

  // Encryption and security errors
  ENCRYPTION_FAILED: 'Failed to encrypt sensitive data',
  DECRYPTION_FAILED: 'Failed to decrypt sensitive data',
  KEY_ROTATION_FAILED: 'Failed to rotate encryption keys',
  INVALID_ENCRYPTED_DATA: 'Invalid or corrupted encrypted data',

  // Account management errors
  ACCOUNT_NOT_FOUND: 'Broker account not found',
  DUPLICATE_ACCOUNT: 'Broker account already exists for this user',
  ACCOUNT_CREATION_FAILED: 'Failed to create broker account',
  ACCOUNT_UPDATE_FAILED: 'Failed to update broker account',
  ACCOUNT_DELETION_FAILED: 'Failed to delete broker account',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions for broker operation',

  // OAuth and token errors
  OAUTH_FAILED: 'OAuth authentication failed',
  TOKEN_EXPIRED: 'Authentication token has expired',
  TOKEN_REFRESH_FAILED: 'Failed to refresh authentication token',
  INVALID_OAUTH_STATE: 'Invalid OAuth state parameter',
  OAUTH_CALLBACK_FAILED: 'OAuth callback processing failed',

  // System errors
  DATABASE_ERROR: 'Database operation failed',
  SYSTEM_ERROR: 'System error occurred',

  // Retry and circuit breaker errors
  MAX_RETRIES_EXCEEDED: 'Maximum retry attempts exceeded',
  CIRCUIT_BREAKER_OPEN: 'Circuit breaker is open, operation blocked',
  OPERATION_TIMEOUT: 'Operation timed out after maximum duration',
  RETRY_EXHAUSTED: 'All retry attempts have been exhausted',
};

export type BrokerErrorEnumType = z.output<typeof BrokerErrorEnum>;

export interface BrokerErrorContext {
  correlationId?: string;
  userId?: string;
  accountId?: string;
  brokerType?: string;
  operation?: string;
  retryAttempt?: number;
  maxRetries?: number;
  timestamp?: Date;
  metadata?: Record<string, unknown>;
}

export class BrokerError extends BaseError<BrokerErrorEnumType> {
  public readonly context: BrokerErrorContext;
  public readonly isRetryable: boolean;
  public readonly severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  constructor(
    name: BrokerErrorEnumType,
    domain: ErrorDomainType,
    details?: {
      message?: string;
      cause?: unknown;
      context?: BrokerErrorContext;
      isRetryable?: boolean;
      severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    },
  ) {
    super({
      name,
      domain,
      message: details?.message ? details.message : BrokerErrorMessages[name],
      cause: details?.cause,
    });

    this.context = {
      timestamp: this.dateTimeUtils.getNewDate(),
      ...details?.context,
    };

    this.isRetryable = details?.isRetryable ?? this.determineRetryability(name);
    this.severity = details?.severity ?? this.determineSeverity(name);

    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }

  /**
   * Create a new error with additional context
   */
  withContext(context: Partial<BrokerErrorContext>): BrokerError {
    return new BrokerError(this.name, this.domain as ErrorDomainType, {
      message: this.message,
      cause: this.cause,
      context: { ...this.context, ...context },
      isRetryable: this.isRetryable,
      severity: this.severity,
    });
  }

  /**
   * Create a new error for retry attempt
   */
  withRetryAttempt(attempt: number, maxRetries: number): BrokerError {
    return new BrokerError(this.name, this.domain as ErrorDomainType, {
      message: this.message,
      cause: this.cause,
      context: { ...this.context, retryAttempt: attempt, maxRetries },
      isRetryable: this.isRetryable,
      severity: this.severity,
    });
  }

  /**
   * Get structured error data for logging
   */
  toLogData(): Record<string, unknown> {
    return {
      error: this.name,
      message: this.message,
      domain: this.domain,
      severity: this.severity,
      isRetryable: this.isRetryable,
      context: this.context,
      stack: this.stack,
      cause: this.cause instanceof Error ? this.cause.message : this.cause,
    };
  }

  /**
   * Get sanitized error data for API responses (removes sensitive information)
   */
  toApiResponse(): Record<string, unknown> {
    return {
      error: this.name,
      message: this.message,
      domain: this.domain,
      correlationId: this.context.correlationId,
      timestamp: this.context.timestamp,
      retryable: this.isRetryable,
    };
  }

  /**
   * Determine if error is retryable based on error type
   */
  private determineRetryability(errorType: BrokerErrorEnumType): boolean {
    const retryableErrors: BrokerErrorEnumType[] = [
      BrokerErrorEnum.enum.CONNECTION_FAILED,
      BrokerErrorEnum.enum.NETWORK_ERROR,
      BrokerErrorEnum.enum.TIMEOUT_ERROR,
      BrokerErrorEnum.enum.BROKER_SERVICE_UNAVAILABLE,
      BrokerErrorEnum.enum.API_RATE_LIMIT_EXCEEDED,
      BrokerErrorEnum.enum.WEBSOCKET_CONNECTION_FAILED,
      BrokerErrorEnum.enum.WEBSOCKET_DISCONNECTED,
      BrokerErrorEnum.enum.TOKEN_REFRESH_FAILED,
      BrokerErrorEnum.enum.BROKER_MAINTENANCE,
      BrokerErrorEnum.enum.UNKNOWN_BROKER_ERROR,
    ];

    return retryableErrors.includes(errorType);
  }

  /**
   * Determine error severity based on error type
   */
  private determineSeverity(errorType: BrokerErrorEnumType): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const severityMap: Record<BrokerErrorEnumType, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {
      // Critical errors - system security or data integrity
      [BrokerErrorEnum.enum.ENCRYPTION_FAILED]: 'CRITICAL',
      [BrokerErrorEnum.enum.DECRYPTION_FAILED]: 'CRITICAL',
      [BrokerErrorEnum.enum.KEY_ROTATION_FAILED]: 'CRITICAL',
      [BrokerErrorEnum.enum.INVALID_ENCRYPTED_DATA]: 'CRITICAL',
      [BrokerErrorEnum.enum.DATABASE_ERROR]: 'CRITICAL',

      // High severity - authentication and authorization
      [BrokerErrorEnum.enum.AUTHENTICATION_FAILED]: 'HIGH',
      [BrokerErrorEnum.enum.INVALID_CREDENTIALS]: 'HIGH',
      [BrokerErrorEnum.enum.INSUFFICIENT_PERMISSIONS]: 'HIGH',
      [BrokerErrorEnum.enum.OAUTH_FAILED]: 'HIGH',
      [BrokerErrorEnum.enum.INVALID_OAUTH_STATE]: 'HIGH',

      // Medium severity - connection and operational issues
      [BrokerErrorEnum.enum.CONNECTION_FAILED]: 'MEDIUM',
      [BrokerErrorEnum.enum.SESSION_EXPIRED]: 'MEDIUM',
      [BrokerErrorEnum.enum.TOKEN_EXPIRED]: 'MEDIUM',
      [BrokerErrorEnum.enum.TOKEN_REFRESH_FAILED]: 'MEDIUM',
      [BrokerErrorEnum.enum.WEBSOCKET_CONNECTION_FAILED]: 'MEDIUM',
      [BrokerErrorEnum.enum.WEBSOCKET_DISCONNECTED]: 'MEDIUM',
      [BrokerErrorEnum.enum.BROKER_SERVICE_UNAVAILABLE]: 'MEDIUM',
      [BrokerErrorEnum.enum.ACCOUNT_CREATION_FAILED]: 'MEDIUM',
      [BrokerErrorEnum.enum.ACCOUNT_UPDATE_FAILED]: 'MEDIUM',
      [BrokerErrorEnum.enum.ACCOUNT_DELETION_FAILED]: 'MEDIUM',

      // Low severity - validation and user errors
      [BrokerErrorEnum.enum.ACCOUNT_NOT_FOUND]: 'LOW',
      [BrokerErrorEnum.enum.DUPLICATE_ACCOUNT]: 'LOW',
      [BrokerErrorEnum.enum.SYMBOL_NOT_FOUND]: 'LOW',
      [BrokerErrorEnum.enum.INVALID_SYMBOL_FORMAT]: 'LOW',
      [BrokerErrorEnum.enum.INVALID_BROKER_RESPONSE]: 'LOW',
      [BrokerErrorEnum.enum.UNSUPPORTED_OPERATION]: 'LOW',
      [BrokerErrorEnum.enum.SUBSCRIPTION_FAILED]: 'LOW',
      [BrokerErrorEnum.enum.UNSUBSCRIPTION_FAILED]: 'LOW',
      [BrokerErrorEnum.enum.OAUTH_CALLBACK_FAILED]: 'LOW',

      // Variable severity based on context
      [BrokerErrorEnum.enum.API_RATE_LIMIT_EXCEEDED]: 'MEDIUM',
      [BrokerErrorEnum.enum.NETWORK_ERROR]: 'MEDIUM',
      [BrokerErrorEnum.enum.TIMEOUT_ERROR]: 'MEDIUM',
      [BrokerErrorEnum.enum.BROKER_MAINTENANCE]: 'LOW',
      [BrokerErrorEnum.enum.UNKNOWN_BROKER_ERROR]: 'MEDIUM',
      [BrokerErrorEnum.enum.SYSTEM_ERROR]: 'HIGH',
      [BrokerErrorEnum.enum.MAX_RETRIES_EXCEEDED]: 'HIGH',
      [BrokerErrorEnum.enum.CIRCUIT_BREAKER_OPEN]: 'MEDIUM',
      [BrokerErrorEnum.enum.OPERATION_TIMEOUT]: 'MEDIUM',
      [BrokerErrorEnum.enum.RETRY_EXHAUSTED]: 'HIGH',
    };

    return severityMap[errorType] || 'MEDIUM';
  }
}
