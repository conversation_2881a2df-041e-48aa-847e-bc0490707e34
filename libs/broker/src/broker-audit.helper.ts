import { Injectable } from '@nestjs/common';
import { AuditLoggingService } from '@app/common/security';
import type { BrokerType } from '@app/common/constants';
import { DateTimeUtilsService } from '@app/utils';

/**
 * Helper service for broker-specific audit logging
 * Provides convenience methods for common broker audit events
 *
 * Requirements:
 * - 6.6: Audit trails for security and compliance
 * - 4.3: Proper access controls and audit logging
 */
@Injectable()
export class BrokerAuditHelper {
  constructor(
    private readonly auditService: AuditLoggingService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  /**
   * Log broker account creation event
   */
  logAccountCreated(
    userId: string,
    accountId: string,
    brokerType: BrokerType,
    accountName: string,
    isAdminAccount: boolean,
    correlationId: string,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'ACCOUNT_CREATED',
      userId,
      accountId,
      details: {
        brokerType,
        accountName,
        isAdminAccount,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: 'MEDIUM',
      category: 'OPERATION',
      module: 'BROKER',
    });
  }

  /**
   * Log broker account update event
   */
  logAccountUpdated(
    userId: string,
    accountId: string,
    brokerType: BrokerType,
    changes: Record<string, unknown>,
    correlationId: string,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'ACCOUNT_UPDATED',
      userId,
      accountId,
      details: {
        brokerType,
        changes,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: 'LOW',
      category: 'OPERATION',
      module: 'BROKER',
    });
  }

  /**
   * Log broker account deletion event
   */
  logAccountDeleted(
    userId: string,
    accountId: string,
    brokerType: BrokerType,
    correlationId: string,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'ACCOUNT_DELETED',
      userId,
      accountId,
      details: {
        brokerType,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: 'HIGH',
      category: 'OPERATION',
      module: 'BROKER',
    });
  }

  /**
   * Log credentials update event
   */
  logCredentialsUpdated(
    userId: string,
    accountId: string,
    brokerType: BrokerType,
    correlationId: string,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'CREDENTIALS_UPDATED',
      userId,
      accountId,
      details: {
        brokerType,
        action: 'credentials_updated',
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: 'HIGH',
      category: 'SECURITY',
      module: 'BROKER',
    });
  }

  /**
   * Log OAuth initiation event
   */
  logOAuthInitiated(
    userId: string,
    brokerType: BrokerType,
    correlationId: string,
    accountId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'OAUTH_INITIATED',
      userId,
      accountId,
      details: {
        brokerType,
        oauthFlow: brokerType,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: 'LOW',
      category: 'SECURITY',
      module: 'BROKER',
    });
  }

  /**
   * Log OAuth completion event
   */
  logOAuthCompleted(
    userId: string,
    brokerType: BrokerType,
    correlationId: string,
    accountId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'OAUTH_COMPLETED',
      userId,
      accountId,
      details: {
        brokerType,
        oauthFlow: brokerType,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: 'LOW',
      category: 'SECURITY',
      module: 'BROKER',
    });
  }

  /**
   * Log token refresh event
   */
  logTokenRefreshed(userId: string, accountId: string, brokerType: BrokerType, correlationId: string): void {
    this.auditService.logAuditEvent({
      eventType: 'TOKEN_REFRESHED',
      userId,
      accountId,
      details: {
        brokerType,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      severity: 'LOW',
      category: 'SECURITY',
      module: 'BROKER',
    });
  }

  /**
   * Log token revocation event
   */
  logTokenRevoked(userId: string, accountId: string, brokerType: BrokerType, correlationId: string): void {
    this.auditService.logAuditEvent({
      eventType: 'TOKEN_REVOKED',
      userId,
      accountId,
      details: {
        brokerType,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      severity: 'MEDIUM',
      category: 'SECURITY',
      module: 'BROKER',
    });
  }

  /**
   * Log connection established event
   */
  logConnectionEstablished(
    userId: string,
    accountId: string,
    brokerType: BrokerType,
    correlationId: string,
    details?: Record<string, unknown>,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'CONNECTION_ESTABLISHED',
      userId,
      accountId,
      details: {
        brokerType,
        connectionEvent: 'CONNECTION_ESTABLISHED',
        timestamp: this.dateTimeUtils.getUtcNow(),
        ...details,
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      severity: 'LOW',
      category: 'OPERATION',
      module: 'BROKER',
    });
  }

  /**
   * Log connection lost event
   */
  logConnectionLost(
    userId: string,
    accountId: string,
    brokerType: BrokerType,
    correlationId: string,
    errorMessage?: string,
    details?: Record<string, unknown>,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'CONNECTION_LOST',
      userId,
      accountId,
      details: {
        brokerType,
        connectionEvent: 'CONNECTION_LOST',
        errorMessage,
        timestamp: this.dateTimeUtils.getUtcNow(),
        ...details,
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      severity: 'MEDIUM',
      category: 'OPERATION',
      module: 'BROKER',
    });
  }

  /**
   * Log security violation event
   */
  logSecurityViolation(
    userId: string,
    operation: string,
    reason: string,
    correlationId: string,
    accountId?: string,
    brokerType?: BrokerType,
    ipAddress?: string,
    userAgent?: string,
  ): void {
    this.auditService.logSecurityEvent(
      'SECURITY_VIOLATION',
      userId,
      operation,
      reason,
      correlationId,
      'BROKER',
      ipAddress,
      userAgent,
      {
        accountId,
        brokerType,
      },
    );
  }

  /**
   * Log authentication failure event
   */
  logAuthenticationFailed(
    userId: string,
    operation: string,
    reason: string,
    correlationId: string,
    ipAddress?: string,
    userAgent?: string,
    brokerType?: BrokerType,
  ): void {
    this.auditService.logSecurityEvent(
      'AUTHENTICATION_FAILED',
      userId,
      operation,
      reason,
      correlationId,
      'BROKER',
      ipAddress,
      userAgent,
      {
        brokerType,
      },
    );
  }

  /**
   * Log rate limit exceeded event
   */
  logRateLimitExceeded(
    userId: string,
    operation: string,
    correlationId: string,
    ipAddress?: string,
    userAgent?: string,
    retryAfter?: number,
    brokerType?: BrokerType,
  ): void {
    this.auditService.logAuditEvent({
      eventType: 'RATE_LIMIT_EXCEEDED',
      userId,
      details: {
        operation,
        brokerType,
        retryAfter,
        timestamp: this.dateTimeUtils.getUtcNow(),
      },
      correlationId,
      timestamp: this.dateTimeUtils.getNewDate(),
      ipAddress,
      userAgent,
      severity: 'MEDIUM',
      category: 'SECURITY',
      module: 'BROKER',
    });
  }

  /**
   * Log admin action event
   */
  logAdminAction(
    adminUserId: string,
    action: string,
    targetUserId?: string,
    targetAccountId?: string,
    correlationId?: string,
    ipAddress?: string,
    userAgent?: string,
    brokerType?: BrokerType,
  ): void {
    this.auditService.logAdminAction(
      action,
      adminUserId,
      targetUserId,
      targetAccountId,
      correlationId,
      ipAddress,
      userAgent,
      {
        module: 'BROKER',
        brokerType,
      },
    );
  }
}
