// ==================== BROKER TYPES AND INTERFACES ====================

import type { BrokerType, BrokerConnectionStatus } from '@app/common/constants';

// ==================== CORE INTERFACES ====================

export interface BrokerCredentials {
  apiKey: string;
  apiSecret: string;
  accessToken?: string;
  refreshToken?: string;
}

export interface EncryptedBrokerCredentials {
  encryptedApiKey: string;
  encryptedApiSecret: string;
  encryptedAccessToken?: string;
  encryptedRefreshToken?: string;
}

export interface ConnectionHealth {
  accountId: string;
  status: BrokerConnectionStatus;
  lastCheckedAt: Date;
  responseTime?: number;
  errorMessage?: string;
  uptime: number;
  failureCount: number;
}

export interface ConnectionStatistics {
  accountId: string;
  totalConnections: number;
  successfulConnections: number;
  failedConnections: number;
  currentUptimeSeconds: number;
  totalDowntimeSeconds: number;
  averageResponseTimeMs?: number;
  lastHeartbeatAt?: Date;
  lastErrorAt?: Date;
  lastErrorMessage?: string;
  successRate: number;
  uptimePercentage: number;
}

export interface OAuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt: Date;
  tokenType: string;
  scope?: string;
}

// ==================== REQUEST/RESPONSE TYPES ====================

export interface CreateBrokerAccountData {
  userId: string;
  brokerType: BrokerType;
  accountName: string;
  isAdminAccount?: boolean;
  credentials: BrokerCredentials;
}

export interface UpdateBrokerAccountData {
  accountName?: string;
  credentials?: Partial<BrokerCredentials>;
  connectionStatus?: BrokerConnectionStatus;
  lastConnectedAt?: Date;
  lastError?: string;
}

export interface BrokerAccountFilters {
  userId?: string;
  brokerType?: BrokerType;
  connectionStatus?: BrokerConnectionStatus;
  isAdminAccount?: boolean;
  accountName?: string;
  limit?: number;
  offset?: number;
}

// ==================== API RESPONSE TYPES ====================

export interface BrokerAccountResponse {
  id: number;
  userId: string;
  brokerType: BrokerType;
  accountName: string;
  isAdminAccount: boolean;
  connectionStatus: BrokerConnectionStatus;
  lastConnectedAt?: string;
  lastError?: string;
  createdAt: string;
  updatedAt: string;
  // Note: Encrypted credentials are never returned in API responses
}

export interface BrokerHealthResponse {
  accountId: string;
  brokerType: BrokerType;
  accountName: string;
  connectionHealth: ConnectionHealth;
  statistics: ConnectionStatistics;
}

export interface OAuthInitiationResponse {
  authorizationUrl: string;
  state: string;
  expiresAt: Date;
}

export interface OAuthCallbackResponse {
  success: boolean;
  accountId?: string;
  message: string;
  tokens?: {
    expiresAt: Date;
    tokenType: string;
    scope?: string;
  };
}

export interface ReconnectionResponse {
  success: boolean;
  accountId: string;
  message: string;
  connectionStatus: BrokerConnectionStatus;
  timestamp: Date;
}

export interface ReconnectionResult {
  success: boolean;
  accountId: string;
  previousStatus: BrokerConnectionStatus;
  newStatus: BrokerConnectionStatus;
  errorMessage?: string;
  timestamp: Date;
}

// ==================== BROKER-SPECIFIC TYPES ====================

export interface ZerodhaConfig {
  apiKey: string;
  apiSecret: string;
  redirectUri: string;
  baseUrl: string;
}

export interface BrokerConfig {
  zerodha: ZerodhaConfig;
  // Future broker configurations can be added here
}

// ==================== ERROR TYPES ====================

export interface BrokerErrorInfo {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: Date;
  accountId?: string;
  brokerType?: BrokerType;
}

// ==================== UTILITY TYPES ====================

export type BrokerAccountWithCredentials = BrokerAccountResponse & {
  credentials: BrokerCredentials;
};

export type BrokerAccountCreateRequest = Omit<CreateBrokerAccountData, 'userId'>;
export type BrokerAccountUpdateRequest = UpdateBrokerAccountData;

// ==================== CONSTANTS ====================

export const BROKER_CONNECTION_TIMEOUT = 10000; // 10 seconds
export const BROKER_HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
export const BROKER_MAX_RETRY_ATTEMPTS = 3;
export const BROKER_RETRY_BACKOFF_BASE = 1000; // 1 second

export const SUPPORTED_BROKERS = ['ZERODHA', 'ALICE_BLUE', 'FINVASIA', 'UPSTOX', 'DHAN', 'GROW', 'ANGEL_ONE'] as const;

export const CONNECTION_STATUS_PRIORITY = {
  CONNECTED: 1,
  CONNECTING: 2,
  RECONNECTING: 3,
  DISCONNECTED: 4,
  EXPIRED: 5,
  ERROR: 6,
} as const;

// ==================== MONITORING AND ALERTING TYPES ====================

export interface BrokerMonitoringMetrics {
  timestamp: Date;
  totalAccounts: number;
  accountsByStatus: Record<BrokerConnectionStatus, number>;
  accountsByBroker: Record<string, number>;
  averageResponseTime: number;
  totalUptime: number;
  totalFailures: number;
  alertsLast24Hours: number;
}

export interface AlertRule {
  type: 'CONNECTION_FAILURE' | 'CONNECTION_RECOVERY' | 'HIGH_FAILURE_RATE' | 'RESPONSE_TIME_HIGH' | 'UPTIME_LOW';
  severity: 'CRITICAL' | 'WARNING' | 'INFO';
  enabled: boolean;
  threshold?: number;
}

export interface AlertEvent {
  id: string;
  type: AlertRule['type'];
  severity: AlertRule['severity'];
  accountId: string;
  brokerType: BrokerType;
  accountName: string;
  message: string;
  details: {
    connectionHealth: ConnectionHealth;
    rule: AlertRule;
    account: {
      id: number;
      brokerType: BrokerType;
      accountName: string;
    };
  };
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

export interface MonitoringDashboardData {
  metrics: BrokerMonitoringMetrics;
  recentAlerts: AlertEvent[];
  connectionHealth: ConnectionHealth[];
  performanceMetrics: {
    averageResponseTime: number;
    uptimePercentage: number;
    errorRate: number;
    throughput: number;
  };
}

export interface ConnectionStatusChangeEvent {
  accountId: string;
  brokerType: BrokerType;
  accountName: string;
  previousStatus: BrokerConnectionStatus;
  currentStatus: BrokerConnectionStatus;
  timestamp: Date;
}

export interface PerformanceDegradationEvent {
  accountId: string;
  brokerType: BrokerType;
  previousResponseTime: number;
  currentResponseTime: number;
  degradationRatio: number;
  timestamp: Date;
}
