import { Module } from '@nestjs/common';
import { CoreModule } from '@app/core';
import { UtilsModule } from '@app/utils';
import { AuthModule } from '@app/auth';
import { CommonModule } from '@app/common';
import { EncryptionModule } from '@app/common/encryption';
import { BrokerRepository } from './broker.repository';
import { BrokerService } from './broker.service';
import { BrokerAuthService } from './broker-auth.service';
import { BrokerAuditHelper } from './broker-audit.helper';

/**
 * Broker library module providing broker authentication and management services
 *
 * Requirements:
 * - 7.1: NestJS framework with dependency injection
 * - 7.3: Module organization with proper imports and exports
 * - 7.4: Integration with existing core services
 */
@Module({
  imports: [
    CoreModule, // Provides DrizzleService, EnvService, etc.
    UtilsModule, // Provides utility services
    AuthModule, // Provides AuthGuard, RolesGuard, etc.
    CommonModule, // Provides security services
    EncryptionModule, // Provides EncryptionService from common library
  ],
  providers: [
    // Core services
    BrokerRepository,
    BrokerService,
    BrokerAuthService,
    // Audit helper
    BrokerAuditHelper,
  ],
  exports: [
    // Export services for use in other modules
    BrokerRepository,
    BrokerService,
    BrokerAuthService,
    BrokerAuditHelper,
  ],
})
export class BrokerLibModule {}
