import { z } from 'zod/v4';
import * as dayjs from 'dayjs';
import { BrokerTypeEnum, BrokerConnectionStatusEnum } from '@app/common/constants';

// ==================== BROKER CREDENTIALS SCHEMAS ====================

/**
 * Broker credentials schema for API requests
 * Requirements: 7.2 - Zod schemas for validation
 */
export const BrokerCredentialsSchema = z.object({
  apiKey: z.string().min(1, 'API key is required').max(255, 'API key too long'),
  apiSecret: z.string().min(1, 'API secret is required').max(255, 'API secret too long'),
  accessToken: z.string().max(1000, 'Access token too long').optional(),
  refreshToken: z.string().max(1000, 'Refresh token too long').optional(),
});

/**
 * Partial broker credentials schema for updates
 */
export const PartialBrokerCredentialsSchema = BrokerCredentialsSchema.partial();

// ==================== BROKER ACCOUNT SCHEMAS ====================

/**
 * Create broker account request schema
 * Requirements: 5.1 - POST /api/broker/accounts validation
 */
export const CreateBrokerAccountSchema = z.object({
  brokerType: BrokerTypeEnum,
  accountName: z.string().min(1, 'Account name is required').max(255, 'Account name too long'),
  isAdminAccount: z.boolean().default(false),
  credentials: BrokerCredentialsSchema,
});

/**
 * Update broker account request schema
 * Requirements: 5.4 - PUT /api/broker/accounts/:id validation
 */
export const UpdateBrokerAccountSchema = z.object({
  accountName: z.string().min(1, 'Account name is required').max(255, 'Account name too long').optional(),
  credentials: PartialBrokerCredentialsSchema.optional(),
  connectionStatus: BrokerConnectionStatusEnum.optional(),
  lastError: z.string().max(1000, 'Error message too long').optional(),
});

/**
 * Broker account filters schema for query parameters
 * Requirements: 5.2 - GET /api/broker/accounts validation
 */
export const BrokerAccountFiltersSchema = z.object({
  brokerType: BrokerTypeEnum.optional(),
  connectionStatus: BrokerConnectionStatusEnum.optional(),
  isAdminAccount: z.coerce.boolean().optional(),
  accountName: z.string().max(255, 'Account name too long').optional(),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
});

/**
 * Broker account response schema
 * Requirements: 5.1, 5.2, 5.3, 5.4 - API response validation
 */
export const BrokerAccountResponseSchema = z.object({
  id: z.number().int().positive(),
  userId: z.string(),
  brokerType: BrokerTypeEnum,
  accountName: z.string(),
  isAdminAccount: z.boolean(),
  connectionStatus: BrokerConnectionStatusEnum,
  lastConnectedAt: z.string().datetime().optional(),
  lastError: z.string().optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// ==================== OAUTH SCHEMAS ====================

/**
 * OAuth initiation request schema
 */
export const OAuthInitiationSchema = z.object({
  brokerType: BrokerTypeEnum,
});

/**
 * OAuth callback request schema
 * Requirements: 5.6 - POST /api/broker/callback validation
 * Enhanced validation for OAuth callback parameters
 */
export const OAuthCallbackSchema = z.object({
  code: z
    .string()
    .min(1, 'Authorization code is required')
    .max(500, 'Authorization code too long')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Authorization code contains invalid characters'),
  state: z
    .string()
    .min(1, 'State parameter is required')
    .max(255, 'State parameter too long')
    .regex(/^[a-fA-F0-9]+$/, 'State parameter must be a valid hexadecimal string'),
  error: z.string().optional(), // Optional error parameter from OAuth provider
  error_description: z.string().optional(), // Optional error description from OAuth provider
});

/**
 * OAuth initiation response schema
 */
export const OAuthInitiationResponseSchema = z.object({
  authorizationUrl: z.string().url('Invalid authorization URL'),
  state: z.string().min(1, 'State is required'),
  expiresAt: z.date(),
});

/**
 * OAuth callback response schema
 */
export const OAuthCallbackResponseSchema = z.object({
  success: z.boolean(),
  accountId: z.string().optional(),
  message: z.string().min(1, 'Message is required'),
  tokens: z
    .object({
      expiresAt: z.date(),
      tokenType: z.string().min(1, 'Token type is required'),
      scope: z.string().optional(),
    })
    .optional(),
});

/**
 * Token validation response schema
 * Requirements: 2.6 - Token validation functionality
 */
export const TokenValidationResponseSchema = z.object({
  valid: z.boolean(),
  message: z.string().min(1, 'Message is required'),
  timestamp: z.string().datetime(),
});

// ==================== HEALTH MONITORING SCHEMAS ====================

/**
 * Connection health schema
 * Requirements: 5.7 - GET /api/broker/health validation
 */
export const ConnectionHealthSchema = z.object({
  accountId: z.string(),
  status: BrokerConnectionStatusEnum,
  lastCheckedAt: z.date(),
  responseTime: z.number().optional(),
  errorMessage: z.string().optional(),
  uptime: z.number().int().min(0),
  failureCount: z.number().int().min(0),
});

/**
 * Connection statistics schema
 */
export const ConnectionStatisticsSchema = z.object({
  accountId: z.string(),
  totalConnections: z.number().int().min(0),
  successfulConnections: z.number().int().min(0),
  failedConnections: z.number().int().min(0),
  currentUptimeSeconds: z.number().int().min(0),
  totalDowntimeSeconds: z.number().int().min(0),
  averageResponseTimeMs: z.number().optional(),
  lastHeartbeatAt: z.date().optional(),
  lastErrorAt: z.date().optional(),
  lastErrorMessage: z.string().optional(),
  successRate: z.number().min(0).max(100),
  uptimePercentage: z.number().min(0).max(100),
});

/**
 * Broker health response schema
 */
export const BrokerHealthResponseSchema = z.object({
  accountId: z.string(),
  brokerType: BrokerTypeEnum,
  accountName: z.string(),
  connectionHealth: ConnectionHealthSchema,
  statistics: ConnectionStatisticsSchema,
});

/**
 * Reconnection response schema
 * Requirements: 5.8 - POST /api/broker/reconnect/:id validation
 */
export const ReconnectionResponseSchema = z.object({
  success: z.boolean(),
  accountId: z.string(),
  message: z.string(),
  connectionStatus: BrokerConnectionStatusEnum,
  timestamp: z.date(),
});

// ==================== PARAMETER SCHEMAS ====================

/**
 * Account ID parameter schema
 */
export const AccountIdParamSchema = z.object({
  id: z.coerce.number().int().positive('Account ID must be a positive integer'),
});

// ==================== ERROR RESPONSE SCHEMAS ====================

/**
 * Error response schema
 * Requirements: 6.2 - Proper error handling with descriptive messages
 */
export const ErrorResponseSchema = z.object({
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.record(z.string(), z.unknown()).optional(),
    timestamp: z.string().datetime(),
    path: z.string().optional(),
  }),
});

/**
 * Validation error response schema
 */
export const ValidationErrorResponseSchema = z.object({
  error: z.object({
    code: z.literal('VALIDATION_ERROR'),
    message: z.string(),
    details: z.object({
      issues: z.array(
        z.object({
          path: z.array(z.union([z.string(), z.number()])),
          message: z.string(),
          code: z.string(),
        }),
      ),
    }),
    timestamp: z.string().datetime(),
    path: z.string().optional(),
  }),
});

// ==================== SUCCESS RESPONSE SCHEMAS ====================

/**
 * Generic success response schema
 */
export const SuccessResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().min(1, 'Message is required'),
  timestamp: z.string().datetime(),
});

/**
 * Delete success response schema
 */
export const DeleteSuccessResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().min(1, 'Message is required'),
  timestamp: z.string().datetime(),
});

// ==================== UTILITY ENDPOINT SCHEMAS ====================

/**
 * Supported brokers response schema
 */
export const SupportedBrokersResponseSchema = z.object({
  brokers: z.array(BrokerTypeEnum),
  timestamp: z.string().datetime(),
});

/**
 * Broker statistics response schema
 */
export const BrokerStatisticsResponseSchema = z.object({
  totalAccounts: z.number().int().min(0),
  activeAccounts: z.number().int().min(0),
  connectedAccounts: z.number().int().min(0),
  errorAccounts: z.number().int().min(0),
  brokerBreakdown: z.record(BrokerTypeEnum, z.number().int().min(0)),
  timestamp: z.string().datetime(),
});

/**
 * Broker configuration schema for environment validation
 * Requirements: 7.3 - Environment configuration validation
 */
export const BrokerConfigSchema = z.object({
  zerodha: z.object({
    apiKey: z.string().min(1, 'Zerodha API key is required'),
    apiSecret: z.string().min(1, 'Zerodha API secret is required'),
    redirectUri: z.string().url('Invalid redirect URI'),
    baseUrl: z.string().url('Invalid base URL').default('https://api.kite.trade'),
  }),
  // Future broker configurations can be added here
});

/**
 * Broker limits schema for validation
 */
export const BrokerLimitsSchema = z.object({
  maxOrdersPerSecond: z.number().int().min(1).max(100),
  maxSubscriptions: z.number().int().min(1).max(10000),
  maxHistoricalDays: z.number().int().min(1).max(5000),
  connectionTimeout: z.number().int().min(1000).max(120000), // 1 second to 2 minutes
  requestTimeout: z.number().int().min(1000).max(60000), // 1 second to 1 minute
});

/**
 * Retry configuration schema
 */
export const RetryConfigSchema = z.object({
  maxRetries: z.number().int().min(0).max(10),
  baseDelay: z.number().int().min(100).max(10000), // 100ms to 10 seconds
  maxDelay: z.number().int().min(1000).max(300000), // 1 second to 5 minutes
  backoffMultiplier: z.number().min(1).max(5),
});

// ==================== TYPE EXPORTS ====================

export type BrokerCredentials = z.output<typeof BrokerCredentialsSchema>;
export type PartialBrokerCredentials = z.output<typeof PartialBrokerCredentialsSchema>;
export type CreateBrokerAccountRequest = z.output<typeof CreateBrokerAccountSchema>;
export type UpdateBrokerAccountRequest = z.output<typeof UpdateBrokerAccountSchema>;
export type BrokerAccountFilters = z.output<typeof BrokerAccountFiltersSchema>;
export type BrokerAccountResponse = z.output<typeof BrokerAccountResponseSchema>;
export type OAuthInitiationRequest = z.output<typeof OAuthInitiationSchema>;
export type OAuthCallbackRequest = z.output<typeof OAuthCallbackSchema>;
export type OAuthInitiationResponse = z.output<typeof OAuthInitiationResponseSchema>;
export type OAuthCallbackResponse = z.output<typeof OAuthCallbackResponseSchema>;
export type TokenValidationResponse = z.output<typeof TokenValidationResponseSchema>;
export type ConnectionHealth = z.output<typeof ConnectionHealthSchema>;
export type ConnectionStatistics = z.output<typeof ConnectionStatisticsSchema>;
export type BrokerHealthResponse = z.output<typeof BrokerHealthResponseSchema>;
export type ReconnectionResponse = z.output<typeof ReconnectionResponseSchema>;
export type AccountIdParam = z.output<typeof AccountIdParamSchema>;
export type ErrorResponse = z.output<typeof ErrorResponseSchema>;
export type ValidationErrorResponse = z.output<typeof ValidationErrorResponseSchema>;
export type SuccessResponse = z.output<typeof SuccessResponseSchema>;
export type DeleteSuccessResponse = z.output<typeof DeleteSuccessResponseSchema>;
export type SupportedBrokersResponse = z.output<typeof SupportedBrokersResponseSchema>;
export type BrokerStatisticsResponse = z.output<typeof BrokerStatisticsResponseSchema>;
export type BrokerConfig = z.output<typeof BrokerConfigSchema>;
export type BrokerLimits = z.output<typeof BrokerLimitsSchema>;
export type RetryConfig = z.output<typeof RetryConfigSchema>;
export type BrokerTypeParam = z.output<typeof BrokerTypeParamSchema>;
export type Pagination = z.output<typeof PaginationSchema>;
export type DateRangeFilter = z.output<typeof DateRangeFilterSchema>;
export type BulkOperation = z.output<typeof BulkOperationSchema>;
export type ConnectionStatusUpdate = z.output<typeof ConnectionStatusUpdateSchema>;
export type HealthCheckConfig = z.output<typeof HealthCheckConfigSchema>;

// ==================== ADDITIONAL VALIDATION SCHEMAS ====================

/**
 * Broker type parameter schema for OAuth initiation
 */
export const BrokerTypeParamSchema = z.object({
  brokerType: BrokerTypeEnum,
});

/**
 * Pagination schema for list endpoints
 */
export const PaginationSchema = z.object({
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['createdAt', 'updatedAt', 'accountName', 'brokerType']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Date range filter schema for historical data queries
 */
export const DateRangeFilterSchema = z
  .object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
  })
  .refine(
    (data) => {
      if (data.startDate && data.endDate) {
        return dayjs(data.startDate).isBefore(dayjs(data.endDate)) || dayjs(data.startDate).isSame(dayjs(data.endDate));
      }
      return true;
    },
    {
      message: 'Start date must be before or equal to end date',
      path: ['startDate'],
    },
  );

/**
 * Bulk operation schema for multiple account operations
 */
export const BulkOperationSchema = z.object({
  accountIds: z
    .array(z.coerce.number().int().positive())
    .min(1, 'At least one account ID is required')
    .max(50, 'Maximum 50 accounts allowed'),
  operation: z.enum(['reconnect', 'refresh_tokens', 'validate_tokens', 'update_status']),
});

/**
 * Connection status update schema
 */
export const ConnectionStatusUpdateSchema = z.object({
  status: BrokerConnectionStatusEnum,
  errorMessage: z.string().max(1000, 'Error message too long').optional(),
  timestamp: z.string().datetime().optional(),
});

/**
 * Health check configuration schema
 */
export const HealthCheckConfigSchema = z.object({
  enabled: z.boolean().default(true),
  interval: z.number().int().min(5000).max(300000).default(30000), // 5 seconds to 5 minutes
  timeout: z.number().int().min(1000).max(60000).default(10000), // 1 second to 1 minute
  retryAttempts: z.number().int().min(0).max(10).default(3),
  alertThreshold: z.number().int().min(1).max(100).default(5), // Number of consecutive failures before alert
});

// ==================== VALIDATION HELPERS ====================

/**
 * Validate and parse request body with proper error handling
 * Requirements: 6.2, 6.4 - Proper error handling with descriptive messages
 */
export const validateRequestBody = <T extends z.ZodSchema>(schema: T, data: unknown): z.output<T> => {
  const result = schema.safeParse(data);
  if (!result.success) {
    const errorMessages = result.error.issues.map((issue) => `${issue.path.join('.')}: ${issue.message}`);
    throw new Error(`Validation failed: ${errorMessages.join(', ')}`);
  }
  return result.data;
};

/**
 * Validate and parse query parameters with proper error handling
 * Requirements: 6.2, 6.4 - Proper error handling with descriptive messages
 */
export const validateQueryParams = <T extends z.ZodSchema>(schema: T, data: unknown): z.output<T> => {
  const result = schema.safeParse(data);
  if (!result.success) {
    const errorMessages = result.error.issues.map((issue) => `${issue.path.join('.')}: ${issue.message}`);
    throw new Error(`Query parameter validation failed: ${errorMessages.join(', ')}`);
  }
  return result.data;
};

/**
 * Validate and parse path parameters with proper error handling
 * Requirements: 6.2, 6.4 - Proper error handling with descriptive messages
 */
export const validatePathParams = <T extends z.ZodSchema>(schema: T, data: unknown): z.output<T> => {
  const result = schema.safeParse(data);
  if (!result.success) {
    const errorMessages = result.error.issues.map((issue) => `${issue.path.join('.')}: ${issue.message}`);
    throw new Error(`Path parameter validation failed: ${errorMessages.join(', ')}`);
  }
  return result.data;
};

/**
 * Create a validation pipe factory for NestJS controllers
 * Requirements: 7.2 - Zod schemas for validation
 */
export const createValidationPipe = <T extends z.ZodSchema>(schema: T) => {
  return class ValidationPipe {
    transform(value: unknown): z.output<T> {
      return validateRequestBody(schema, value);
    }
  };
};

/**
 * Validate environment configuration
 * Requirements: 7.3 - Environment configuration validation
 */
export const validateEnvironmentConfig = (config: unknown): BrokerConfig => {
  return validateRequestBody(BrokerConfigSchema, config);
};

/**
 * Sanitize sensitive data from objects for logging
 * Requirements: 6.6 - Security considerations for logging
 */
export const sanitizeForLogging = <T extends Record<string, unknown>>(obj: T): Partial<T> => {
  const sensitiveFields = [
    'apiKey',
    'apiSecret',
    'accessToken',
    'refreshToken',
    'encryptedApiKey',
    'encryptedApiSecret',
    'encryptedAccessToken',
    'encryptedRefreshToken',
  ];
  const sanitized = { ...obj };

  sensitiveFields.forEach((field) => {
    if (field in sanitized) {
      (sanitized as Record<string, unknown>)[field] = '[REDACTED]';
    }
  });

  return sanitized;
};

// ==================== SCHEMA COLLECTIONS ====================

/**
 * Collection of all broker-related request schemas
 * Requirements: 7.2 - Comprehensive Zod schemas for validation
 */
export const BrokerRequestSchemas = {
  // Account management
  CreateBrokerAccount: CreateBrokerAccountSchema,
  UpdateBrokerAccount: UpdateBrokerAccountSchema,
  BrokerAccountFilters: BrokerAccountFiltersSchema,

  // OAuth
  OAuthInitiation: OAuthInitiationSchema,
  OAuthCallback: OAuthCallbackSchema,

  // Parameters
  AccountIdParam: AccountIdParamSchema,
  BrokerTypeParam: BrokerTypeParamSchema,

  // Utility
  Pagination: PaginationSchema,
  DateRangeFilter: DateRangeFilterSchema,
  BulkOperation: BulkOperationSchema,
  ConnectionStatusUpdate: ConnectionStatusUpdateSchema,
  HealthCheckConfig: HealthCheckConfigSchema,
} as const;

/**
 * Collection of all broker-related response schemas
 * Requirements: 7.2 - Comprehensive Zod schemas for validation
 */
export const BrokerResponseSchemas = {
  // Account responses
  BrokerAccount: BrokerAccountResponseSchema,

  // OAuth responses
  OAuthInitiation: OAuthInitiationResponseSchema,
  OAuthCallback: OAuthCallbackResponseSchema,
  TokenValidation: TokenValidationResponseSchema,

  // Health responses
  ConnectionHealth: ConnectionHealthSchema,
  ConnectionStatistics: ConnectionStatisticsSchema,
  BrokerHealth: BrokerHealthResponseSchema,
  Reconnection: ReconnectionResponseSchema,

  // Utility responses
  Success: SuccessResponseSchema,
  DeleteSuccess: DeleteSuccessResponseSchema,
  SupportedBrokers: SupportedBrokersResponseSchema,
  BrokerStatistics: BrokerStatisticsResponseSchema,

  // Error responses
  Error: ErrorResponseSchema,
  ValidationError: ValidationErrorResponseSchema,
} as const;

/**
 * Collection of all broker-related configuration schemas
 * Requirements: 7.3 - Environment configuration validation
 */
export const BrokerConfigSchemas = {
  BrokerConfig: BrokerConfigSchema,
  BrokerLimits: BrokerLimitsSchema,
  RetryConfig: RetryConfigSchema,
  HealthCheckConfig: HealthCheckConfigSchema,
} as const;

/**
 * Collection of all broker-related credential schemas
 * Requirements: 4.1, 4.2 - Secure credential validation
 */
export const BrokerCredentialSchemas = {
  BrokerCredentials: BrokerCredentialsSchema,
  PartialBrokerCredentials: PartialBrokerCredentialsSchema,
} as const;

/**
 * Master collection of all broker schemas
 * Requirements: 7.2 - Comprehensive Zod schemas for all broker-related data structures
 */
export const BrokerSchemas = {
  Request: BrokerRequestSchemas,
  Response: BrokerResponseSchemas,
  Config: BrokerConfigSchemas,
  Credentials: BrokerCredentialSchemas,
} as const;
