# PatternTrade API Queue System

This document provides comprehensive documentation for the BullMQ-based background job processing system implemented in PatternTrade API.

## Overview

The queue system provides robust background job processing capabilities using BullMQ with Redis as the backend. It includes:

- **BaseQueueService**: Abstract class for creating queue services
- **BaseWorkerService**: Abstract class for creating job workers
- **QueueService**: Central queue management service
- **QueueHealthService**: Health monitoring for queues and workers
- **BullBoardService**: Web dashboard for queue monitoring
- **QueueHealthMonitorService**: Automated health monitoring integration

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Queue Module  │    │  Health Module  │    │ BullBoard UI    │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │QueueService │ │    │ │HealthCheck  │ │    │ │ Dashboard   │ │
│ │             │ │    │ │Service      │ │    │ │ Auth        │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │QueueHealth  │ │────│ │HealthMonitor│ │    │                 │
│ │Service      │ │    │ │Service      │ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Redis       │
                    │   (DragonflyDB) │
                    └─────────────────┘
```

## Core Components

### BaseQueueService

Abstract base class for creating queue services with common operations:

```typescript
import { BaseQueueService, QueuePriorityType } from '@app/core/queue';

@Injectable()
export class MyQueueService extends BaseQueueService<MyJobDataType> {
  constructor(
    queueService: QueueService,
    envService: EnvService,
    dateTimeUtils: DateTimeUtilsService,
  ) {
    const connectionConfig = createQueueConnection(envService);
    super('my-queue', connectionConfig, DEFAULT_QUEUE_OPTIONS, dateTimeUtils);
  }

  async addMyJob(data: MyJobDataType, priority?: QueuePriorityType): Promise<string> {
    const job = await this.addJobWithPriority('my-job', data, priority || 'NORMAL');
    return job.id || 'unknown';
  }
}
```

### BaseWorkerService

Abstract base class for creating job workers with standardized processing:

```typescript
import { BaseWorkerService, type JobResult } from '@app/core/queue';

@Injectable()
export class MyWorkerService extends BaseWorkerService<MyJobDataType> {
  constructor(
    queueService: QueueService,
    queueHealthService: QueueHealthService,
    envService: EnvService,
    dateTimeUtils: DateTimeUtilsService,
  ) {
    const connectionConfig = createQueueConnection(envService);
    super('my-queue', connectionConfig, DEFAULT_WORKER_OPTIONS, dateTimeUtils);

    // Register health check
    this.queueHealthService.registerWorkerHealthCheck(
      MyWorkerService.name,
      () => this.getHealthStatus(),
    );
  }

  protected async processJob(job: Job<MyJobDataType>): Promise<JobResult<MyResultType>> {
    // Your job processing logic here
    const result = await this.doWork(job.data);
    
    return {
      success: true,
      data: result,
      jobId: job.id || 'unknown',
      queueName: this.queueName,
      processedAt: this.dateTimeUtils.getCurrentUtcDateTime(),
      duration: Date.now() - startTime,
      attempts: job.attemptsMade,
    };
  }

  private async doWork(data: MyJobDataType): Promise<MyResultType> {
    // Implement your business logic
    return { processed: true };
  }
}
```

## Configuration

### Queue Configuration

```typescript
// Queue connection configuration
export const queueConnectionConfig = {
  host: 'localhost',
  port: 6379,
  db: 0, // Separate DB for queues
  keyPrefix: 'bull:',
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
};

// Default queue options
export const queueOptions = {
  defaultJobOptions: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
  settings: {
    stalledInterval: 30000,
    maxStalledCount: 1,
    retryProcessDelay: 5000,
  },
};

// Worker options
export const workerOptions = {
  concurrency: 1,
  settings: {
    stalledInterval: 30000,
    maxStalledCount: 1,
  },
};
```

### Environment Variables

Required environment variables for queue system:

```bash
# Redis Configuration (required)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=optional
REDIS_PASSWORD=optional

# Application Configuration
NODE_ENV=local|development|staging|production
PORT=3000
```

## Usage Examples

### Creating a Queue Service

```typescript
// symbol-download.queue.ts
import { Injectable } from '@nestjs/common';
import { BaseQueueService, QueueNameEnum } from '@app/core/queue';

export const SymbolDownloadJobDataSchema = z.object({
  exchange: z.string().min(1),
  segment: z.string().min(1),
  forceRefresh: z.boolean().default(false),
});

export type SymbolDownloadJobDataType = z.output<typeof SymbolDownloadJobDataSchema>;

@Injectable()
export class SymbolDownloadQueueService extends BaseQueueService<SymbolDownloadJobDataType> {
  constructor(
    queueService: QueueService,
    envService: EnvService,
    dateTimeUtils: DateTimeUtilsService,
  ) {
    const connectionConfig = createQueueConnection(envService);
    super(QueueNameEnum.enum.SYMBOL_DOWNLOAD, connectionConfig, DEFAULT_QUEUE_OPTIONS, dateTimeUtils);
  }

  async addSymbolDownloadJob(exchange: string, segment: string): Promise<string> {
    const jobData = { exchange, segment, forceRefresh: false };
    const job = await this.addJob('download-symbols', jobData);
    return job.id || 'unknown';
  }

  async scheduleDaily(): Promise<string> {
    const job = await this.addRepeatingJob(
      'daily-symbol-download',
      { exchange: 'ALL', segment: 'ALL', forceRefresh: true },
      { pattern: '0 6 * * *', tz: 'Asia/Kolkata' }
    );
    return job.id || 'unknown';
  }
}
```

### Creating a Worker Service

```typescript
// symbol-download.worker.ts
import { Injectable } from '@nestjs/common';
import { BaseWorkerService, type JobResult } from '@app/core/queue';

@Injectable()
export class SymbolDownloadWorkerService extends BaseWorkerService<SymbolDownloadJobDataType> {
  constructor(
    queueService: QueueService,
    queueHealthService: QueueHealthService,
    symbolService: SymbolService,
    envService: EnvService,
    dateTimeUtils: DateTimeUtilsService,
  ) {
    const connectionConfig = createQueueConnection(envService);
    super(QueueNameEnum.enum.SYMBOL_DOWNLOAD, connectionConfig, { concurrency: 2 }, dateTimeUtils);

    this.queueHealthService.registerWorkerHealthCheck(
      SymbolDownloadWorkerService.name,
      () => this.getHealthStatus(),
    );
  }

  protected async processJob(job: Job<SymbolDownloadJobDataType>): Promise<JobResult<any>> {
    const { exchange, segment, forceRefresh } = job.data;
    
    // Update progress
    await job.updateProgress(10);
    
    // Fetch symbols
    const symbols = await this.symbolService.fetchSymbols(exchange, segment);
    await job.updateProgress(50);
    
    // Process symbols
    const result = await this.symbolService.processSymbols(symbols, forceRefresh);
    await job.updateProgress(100);
    
    return {
      success: true,
      data: result,
      jobId: job.id || 'unknown',
      queueName: this.queueName,
      processedAt: this.dateTimeUtils.getCurrentUtcDateTime(),
      duration: Date.now() - startTime,
      attempts: job.attemptsMade,
    };
  }
}
```

### Module Integration

```typescript
// symbol.module.ts
import { Module } from '@nestjs/common';
import { SymbolService } from './symbol.service';
import { SymbolDownloadQueueService } from './symbol-download.queue';
import { SymbolDownloadWorkerService } from './symbol-download.worker';

@Module({
  providers: [
    SymbolService,
    SymbolDownloadQueueService,
    SymbolDownloadWorkerService,
  ],
  exports: [
    SymbolService,
    SymbolDownloadQueueService,
    SymbolDownloadWorkerService,
  ],
})
export class SymbolModule {}
```

## Queue Types and Priorities

### Predefined Queue Names

```typescript
export const QueueNameEnum = z.enum([
  'SYMBOL_DOWNLOAD',
  'MARKET_DATA_SYNC',
  'ORDER_PROCESSING',
  'NOTIFICATION',
  'ANALYTICS',
  'CLEANUP',
]);
```

### Priority Levels

```typescript
export const QueuePriorityEnum = z.enum([
  'CRITICAL',   // Priority 1
  'HIGH',       // Priority 2
  'NORMAL',     // Priority 5
  'LOW',        // Priority 7
  'BACKGROUND', // Priority 10
]);
```

## Health Monitoring

### Health Check Integration

The queue system integrates with the main health check system:

```typescript
// Health check response includes queue status
{
  "appStatus": true,
  "postgresStatus": true,
  "questdbStatus": true,
  "queueStatus": true,
  "services": {
    "queues": {
      "status": true,
      "summary": {
        "totalQueues": 3,
        "healthyQueues": 3,
        "totalWorkers": 5,
        "runningWorkers": 5
      },
      "queues": [...],
      "workers": [...]
    }
  }
}
```

### Manual Health Checks

```typescript
// Force a health check
const healthStatus = await queueHealthMonitorService.forceHealthCheck();

// Get specific queue health
const queueHealth = await queueHealthService.getQueueHealthStatus('SYMBOL_DOWNLOAD');

// Get worker health
const workerHealth = await queueHealthService.getWorkerHealthStatus('SymbolDownloadWorkerService');
```

## BullBoard Dashboard

### Access

- **URL**: `http://localhost:3000/admin/queues`
- **Authentication**: Required (integrates with app auth system)
- **Features**:
  - Queue monitoring and management
  - Job inspection and retry
  - Real-time statistics
  - Worker status monitoring

### Authentication

The dashboard requires authentication and admin privileges:

```typescript
// Development: Automatically allowed in local environment
// Production: Requires valid session with admin role
const isAuthenticated = session?.user?.id && session?.user?.role === 'admin';
```

## Error Handling

### Queue Errors

```typescript
export const QueueErrorEnum = z.enum([
  'QUEUE_CONNECTION_FAILED',
  'JOB_ADD_FAILED',
  'WORKER_START_FAILED',
  'WORKER_PROCESS_FAILED',
  // ... more error types
]);

// Usage
try {
  await queueService.addJob('my-job', data);
} catch (error) {
  if (error instanceof QueueError) {
    logger.error('Queue operation failed:', {
      errorType: error.name,
      message: error.message,
      context: error.context,
    });
  }
}
```

### Retry Logic

Jobs automatically retry with exponential backoff:

```typescript
const jobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 2000, // Start with 2 seconds
  },
};
```

## Best Practices

### 1. Queue Organization

- Keep queues focused on specific domains (e.g., symbol, order, notification)
- Use descriptive queue names from the predefined enum
- Implement queue services in their respective feature modules

### 2. Job Design

- Keep job data serializable (JSON-compatible)
- Validate job data with Zod schemas
- Include request IDs for traceability
- Use appropriate priorities

### 3. Worker Implementation

- Implement idempotent job processing
- Use proper error handling and logging
- Update job progress for long-running tasks
- Register health checks for monitoring

### 4. Error Handling

- Use domain-specific error types
- Log errors with context
- Implement proper retry strategies
- Monitor failed job rates

### 5. Monitoring

- Register worker health checks
- Monitor queue backlogs
- Set up alerts for high failure rates
- Use the BullBoard dashboard for debugging

## Testing

### Unit Tests

```typescript
// Example test for queue service
describe('SymbolDownloadQueueService', () => {
  let service: SymbolDownloadQueueService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        SymbolDownloadQueueService,
        // Mock dependencies
      ],
    }).compile();

    service = module.get<SymbolDownloadQueueService>(SymbolDownloadQueueService);
  });

  it('should add symbol download job', async () => {
    const jobId = await service.addSymbolDownloadJob('NSE', 'EQ');
    expect(jobId).toBeDefined();
  });
});
```

### Integration Tests

```typescript
// Example integration test
describe('Queue System Integration', () => {
  it('should process symbol download job end-to-end', async () => {
    // Add job to queue
    const jobId = await symbolDownloadQueue.addSymbolDownloadJob('NSE', 'EQ');
    
    // Wait for processing
    await waitForJobCompletion(jobId);
    
    // Verify results
    const symbols = await symbolService.getSymbols('NSE', 'EQ');
    expect(symbols.length).toBeGreaterThan(0);
  });
});
```

## Troubleshooting

### Common Issues

1. **Redis Connection Issues**
   - Check Redis server status
   - Verify connection configuration
   - Check network connectivity

2. **Job Processing Failures**
   - Check worker logs
   - Verify job data format
   - Check for resource constraints

3. **High Queue Backlogs**
   - Increase worker concurrency
   - Optimize job processing logic
   - Check for bottlenecks

4. **Dashboard Access Issues**
   - Verify authentication
   - Check admin privileges
   - Ensure correct URL path

### Debugging

```typescript
// Enable debug logging
const logger = new Logger('QueueDebug');
logger.debug('Queue status:', await queueService.getAllQueuesHealthStatus());

// Check specific job status
const job = await queueService.getJob(jobId);
logger.debug('Job details:', {
  id: job?.id,
  state: await job?.getState(),
  progress: job?.progress,
  attempts: job?.attemptsMade,
});
```

## Performance Considerations

### Scaling

- Use multiple worker instances for high throughput
- Implement proper connection pooling
- Monitor Redis memory usage
- Consider queue partitioning for very high loads

### Optimization

- Batch similar operations
- Use appropriate job priorities
- Implement efficient cleanup strategies
- Monitor and optimize job processing times

## Quick Start Guide

### 1. Install Dependencies

Dependencies are already installed in the project:
- `bullmq`: Core queue functionality
- `@bull-board/api`, `@bull-board/express`, `@bull-board/ui`: Dashboard UI

### 2. Create Your Queue Service

```bash
# Create your queue service file
touch libs/your-module/src/your-feature.queue.ts
```

### 3. Create Your Worker Service

```bash
# Create your worker service file
touch libs/your-module/src/your-feature.worker.ts
```

### 4. Update Your Module

Add the queue and worker services to your module providers and exports.

### 5. Use in Controllers or Services

```typescript
@Injectable()
export class YourService {
  constructor(private readonly yourQueueService: YourQueueService) {}

  async triggerBackgroundJob(data: any): Promise<string> {
    return await this.yourQueueService.addJob('process-data', data);
  }
}
```

### 6. Monitor via Dashboard

Visit `http://localhost:3000/admin/queues` to monitor your queues.

## Migration Guide

### From Manual Background Processing

If you're currently using manual background processing:

1. **Identify Background Tasks**: List all current background operations
2. **Create Queue Services**: Convert each background task to a queue service
3. **Create Workers**: Implement workers for each queue
4. **Update Callers**: Replace direct calls with queue job additions
5. **Add Monitoring**: Implement health checks and monitoring

### Example Migration

**Before:**
```typescript
// Direct background processing
async processSymbols(exchange: string): Promise<void> {
  // This blocks the request
  const symbols = await this.fetchSymbols(exchange);
  await this.saveSymbols(symbols);
}
```

**After:**
```typescript
// Queue-based processing
async processSymbols(exchange: string): Promise<string> {
  // This returns immediately with job ID
  return await this.symbolDownloadQueue.addSymbolDownloadJob(exchange, 'EQ');
}
```

---

For more information, see the individual service documentation and the BullMQ official documentation.
