import { createZodDto } from 'nestjs-zod';
import {
  CreateBrokerAccountSchema,
  UpdateBrokerAccountSchema,
  BrokerAccountFiltersSchema,
  OAuthCallbackSchema,
  AccountIdParamSchema,
} from '@app/broker';

/**
 * Broker DTOs for API validation using nestjs-zod
 *
 * These DTOs replace the custom ZodValidationPipe pattern with nestjs-zod's createZodDto
 * approach, providing better integration with NestJS validation pipeline and consistent
 * error handling across the application.
 *
 * Requirements:
 * - 3.1: Replace custom ZodValidationPipe with nestjs-zod DTOs
 * - 3.2: Create DTOs for all broker request schemas
 */

/**
 * DTO for creating a new broker account
 * Used in POST /api/broker/accounts endpoint
 *
 * Requirements: 3.1 - Replace custom ZodValidationPipe with nestjs-zod DTOs
 */
export class CreateBrokerAccountDto extends createZodDto(CreateBrokerAccountSchema) {}

/**
 * DTO for updating an existing broker account
 * Used in PUT /api/broker/accounts/:id endpoint
 *
 * Requirements: 3.1 - Replace custom ZodValidationPipe with nestjs-zod DTOs
 */
export class UpdateBrokerAccountDto extends createZodDto(UpdateBrokerAccountSchema) {}

/**
 * DTO for broker account query filters
 * Used in GET /api/broker/accounts endpoint for filtering and pagination
 *
 * Requirements: 3.1 - Replace custom ZodValidationPipe with nestjs-zod DTOs
 */
export class BrokerAccountFiltersDto extends createZodDto(BrokerAccountFiltersSchema) {}

/**
 * DTO for OAuth callback request
 * Used in POST /api/broker/callback endpoint for OAuth flow completion
 *
 * Requirements: 3.1 - Replace custom ZodValidationPipe with nestjs-zod DTOs
 */
export class OAuthCallbackDto extends createZodDto(OAuthCallbackSchema) {}

/**
 * DTO for account ID parameter (numeric)
 * Used in endpoints that require numeric account ID in path parameters
 *
 * Requirements: 3.1 - Replace custom ZodValidationPipe with nestjs-zod DTOs
 */
export class AccountIdParamDto extends createZodDto(AccountIdParamSchema) {}
