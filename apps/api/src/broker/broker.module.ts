import { Modu<PERSON> } from '@nestjs/common';
import { BrokerLibModule } from '@app/broker';
import { EncryptionModule } from '@app/common/encryption';
import { AuditLoggingService } from '@app/common/security';
import { <PERSON>roker<PERSON>ontroller } from './broker.controller';

/**
 * Broker module for API application
 *
 * This module provides the broker controller endpoints for the API application,
 * importing necessary services from the broker library and common modules.
 *
 * Requirements:
 * - 2.1: Create broker module structure in API application
 * - 4.2: Proper module organization and imports
 * - 4.4: Clean dependencies and NestJS best practices
 */
@Module({
  imports: [
    BrokerLibModule, // Import broker services from library
    EncryptionModule, // Import encryption service from common library
  ],
  controllers: [BrokerController],
  providers: [
    AuditLoggingService, // Provide audit logging service for controller
  ],
  exports: [BrokerController],
})
export class BrokerModule {}
