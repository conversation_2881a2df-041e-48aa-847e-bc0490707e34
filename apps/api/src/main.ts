declare const module: { hot: { accept: () => void; dispose: (callback: () => Promise<void>) => void } };

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { LoggerErrorInterceptor, Logger as PinoLogger } from 'nestjs-pino';
import type { NestExpressApplication } from '@nestjs/platform-express';
import helmet from 'helmet';
import { SessionService } from '@app/core/session';
import type { MicroserviceOptions } from '@nestjs/microservices';
import { constant } from '@app/common/constants';
import { Logger } from '@nestjs/common';
// import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
// import { ValidationPipe } from '@nestjs/common';
import { AllExceptionsFilter } from '@app/common/filters';
import { ZodValidationPipe } from 'nestjs-zod';
import { EnvService } from '@app/core/env';
import { getRedisTransportOption } from '@app/core/transport';
import { SecurityService } from '@app/common/security';

async function bootstrap() {
  try {
    const app = await NestFactory.create<NestExpressApplication>(AppModule, {
      bufferLogs: true,
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Get security service for enhanced configuration
    const securityService = app.get(SecurityService);

    // Apply enhanced CORS configuration
    app.enableCors(securityService.getCorsConfig());

    // Apply enhanced Helmet security headers
    app.use(helmet(securityService.getHelmetConfig()));
    app.useLogger(app.get(PinoLogger));
    app.useGlobalInterceptors(new LoggerErrorInterceptor());
    // Configure global ZodValidationPipe for automatic validation across all endpoints
    app.useGlobalPipes(new ZodValidationPipe());
    app.useGlobalFilters(new AllExceptionsFilter());

    // const config = new DocumentBuilder()
    //   .setTitle('api')
    //   .setDescription('The api description')
    //   .setVersion('1.0')
    //   .build();
    // const document = SwaggerModule.createDocument(app, config);
    // SwaggerModule.setup('api', app, document);

    const envService = app.get(EnvService);

    const sessionService = app.get(SessionService);
    sessionService.setup(app);

    app.connectMicroservice<MicroserviceOptions>(getRedisTransportOption(envService), { inheritAppConfig: true });

    const globalPrefix = constant.API;
    const port = envService.get('PORT') || 3001;

    // Set global prefix without problematic exclude patterns
    app.setGlobalPrefix(globalPrefix);

    await app.startAllMicroservices();
    await app.listen(port);

    // const socketService = app.get(SocketService);
    // socketService.setup(app);

    Logger.log({
      context: 'Bootstrap',
      msg: `🚀 Application is running on: http://localhost:${port}/${globalPrefix} successfully`,
    });

    app.enableShutdownHooks();

    if (module.hot) {
      module.hot.accept();
      module.hot.dispose(() => app.close());
    }
  } catch (error) {
    Logger.error('Bootstrap failed:', JSON.stringify(error));
    process.exit(1);
  }
}

const start = performance.now();
bootstrap()
  .then(() => {
    const end = performance.now();
    Logger.log(`Bootstrap took ${end - start}ms`);
  })
  .catch((error) => {
    Logger.error('Bootstrap failed:', JSON.stringify(error));
    process.exit(1);
  });
