#!/usr/bin/env node

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

async function applyPerformanceIndexes() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    user: process.env.DB_USER || 'admin',
    password: process.env.DB_PASSWORD || 'admin',
    database: process.env.DB_NAME || 'patterntrade',
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected successfully!');

    // Read the performance indexes SQL file
    const sqlFilePath = path.join(__dirname, '..', 'drizzle', 'performance-indexes-migration.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('Applying performance indexes...');
    
    // Split the SQL content by statements and execute them one by one
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('/*'));

    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    for (const statement of statements) {
      try {
        // Skip comments and empty statements
        if (statement.startsWith('--') || statement.startsWith('/*') || statement.trim() === '') {
          continue;
        }

        console.log(`Executing: ${statement.substring(0, 100)}...`);
        await client.query(statement);
        successCount++;
        console.log('✓ Success');
      } catch (error) {
        if (error.code === '42P07' || error.message.includes('already exists')) {
          console.log('⚠ Already exists, skipping');
          skipCount++;
        } else {
          console.error('✗ Error:', error.message);
          errorCount++;
        }
      }
    }

    console.log('\n=== Performance Indexes Application Summary ===');
    console.log(`✓ Successfully applied: ${successCount}`);
    console.log(`⚠ Skipped (already exists): ${skipCount}`);
    console.log(`✗ Errors: ${errorCount}`);

    if (errorCount === 0) {
      console.log('\n🎉 All performance indexes applied successfully!');
    } else {
      console.log('\n⚠️  Some indexes failed to apply. Check the errors above.');
    }

  } catch (error) {
    console.error('Failed to apply performance indexes:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('Database connection closed.');
  }
}

// Run the script
applyPerformanceIndexes().catch(console.error);