#!/usr/bin/env node

const { Client } = require('pg');

// Load environment variables
require('dotenv').config();

const performanceIndexes = [
  // Broker accounts performance indexes
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_user_status_idx 
   ON broker_accounts (user_id, connection_status)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_type_status_idx 
   ON broker_accounts (broker_type, connection_status)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_admin_status_idx 
   ON broker_accounts (is_admin_account, connection_status)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_last_connected_idx 
   ON broker_accounts (last_connected_at)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_active_accounts_idx 
   ON broker_accounts (user_id, connection_status, broker_type)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_account_name_idx 
   ON broker_accounts (account_name)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_audit_idx 
   ON broker_accounts (created_at, user_id)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_error_tracking_idx 
   ON broker_accounts (connection_status, last_error)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_connected_only_idx 
   ON broker_accounts (user_id, broker_type, last_connected_at) 
   WHERE connection_status = 'CONNECTED'`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_admin_only_idx 
   ON broker_accounts (broker_type, connection_status, last_connected_at) 
   WHERE is_admin_account = true`,

  // Connection status performance indexes
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_monitoring_idx 
   ON broker_connection_status (broker_account_id, status, status_changed_at)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_retry_schedule_idx 
   ON broker_connection_status (next_retry_at, retry_count)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_error_analysis_idx 
   ON broker_connection_status (status, error_message, status_changed_at)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_recent_idx 
   ON broker_connection_status (status_changed_at, status)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_failed_only_idx 
   ON broker_connection_status (broker_account_id, status_changed_at, retry_count) 
   WHERE status IN ('FAILED', 'ERROR', 'DISCONNECTED')`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_pending_retry_idx 
   ON broker_connection_status (next_retry_at, broker_account_id) 
   WHERE next_retry_at IS NOT NULL`,

  // Health stats performance indexes
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_date_range_idx 
   ON broker_health_stats (stats_date, broker_account_id, updated_at)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_performance_idx 
   ON broker_health_stats (average_response_time_ms, stats_date)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_uptime_idx 
   ON broker_health_stats (current_uptime_seconds, total_downtime_seconds, stats_date)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_error_tracking_idx 
   ON broker_health_stats (last_error_at, broker_account_id)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_heartbeat_idx 
   ON broker_health_stats (last_heartbeat_at, broker_account_id)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_success_rate_idx 
   ON broker_health_stats (successful_connections, total_connections, stats_date)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_recent_idx 
   ON broker_health_stats (stats_date, updated_at)`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_with_errors_idx 
   ON broker_health_stats (broker_account_id, last_error_at, stats_date) 
   WHERE last_error_at IS NOT NULL`,
  
  `CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_high_performance_idx 
   ON broker_health_stats (broker_account_id, average_response_time_ms, stats_date) 
   WHERE average_response_time_ms < 1000`,
];

const performanceViews = [
  // Active broker accounts summary view
  `CREATE OR REPLACE VIEW active_broker_accounts_summary AS
   SELECT 
     ba.broker_type,
     COUNT(*) as total_accounts,
     COUNT(CASE WHEN ba.connection_status = 'CONNECTED' THEN 1 END) as connected_accounts,
     COUNT(CASE WHEN ba.connection_status = 'FAILED' THEN 1 END) as failed_accounts,
     COUNT(CASE WHEN ba.is_admin_account = true THEN 1 END) as admin_accounts,
     AVG(EXTRACT(EPOCH FROM (NOW() - ba.last_connected_at))/3600) as avg_hours_since_connection
   FROM broker_accounts ba
   WHERE ba.deleted_at IS NULL
   GROUP BY ba.broker_type`,

  // Daily health statistics summary view
  `CREATE OR REPLACE VIEW daily_health_stats_summary AS
   SELECT 
     bhs.stats_date,
     COUNT(DISTINCT bhs.broker_account_id) as accounts_tracked,
     AVG(bhs.average_response_time_ms) as avg_response_time,
     SUM(bhs.total_connections) as total_connections,
     SUM(bhs.successful_connections) as successful_connections,
     SUM(bhs.failed_connections) as failed_connections,
     AVG(bhs.current_uptime_seconds) as avg_uptime_seconds,
     COUNT(CASE WHEN bhs.last_error_at IS NOT NULL THEN 1 END) as accounts_with_errors
   FROM broker_health_stats bhs
   GROUP BY bhs.stats_date
   ORDER BY bhs.stats_date DESC`,
];

async function applyPerformanceOptimizations() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    user: process.env.DB_USER || 'admin',
    password: process.env.DB_PASSWORD || 'admin',
    database: process.env.DB_NAME || 'patterntrade',
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected successfully!');

    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    console.log('\n=== Applying Performance Indexes ===');
    for (const [index, indexSQL] of performanceIndexes.entries()) {
      try {
        const indexName = indexSQL.match(/IF NOT EXISTS (\w+)/)?.[1] || `index_${index + 1}`;
        console.log(`Creating index: ${indexName}...`);
        
        await client.query(indexSQL);
        successCount++;
        console.log('✓ Success');
      } catch (error) {
        if (error.code === '42P07' || error.message.includes('already exists')) {
          console.log('⚠ Already exists, skipping');
          skipCount++;
        } else {
          console.error('✗ Error:', error.message);
          errorCount++;
        }
      }
    }

    console.log('\n=== Applying Performance Views ===');
    for (const [index, viewSQL] of performanceViews.entries()) {
      try {
        const viewName = viewSQL.match(/VIEW (\w+)/)?.[1] || `view_${index + 1}`;
        console.log(`Creating view: ${viewName}...`);
        
        await client.query(viewSQL);
        successCount++;
        console.log('✓ Success');
      } catch (error) {
        console.error('✗ Error:', error.message);
        errorCount++;
      }
    }

    console.log('\n=== Analyzing Tables for Query Optimization ===');
    const tables = ['broker_accounts', 'broker_connection_status', 'broker_health_stats'];
    for (const table of tables) {
      try {
        console.log(`Analyzing table: ${table}...`);
        await client.query(`ANALYZE ${table}`);
        successCount++;
        console.log('✓ Success');
      } catch (error) {
        console.error('✗ Error:', error.message);
        errorCount++;
      }
    }

    console.log('\n=== Performance Optimization Summary ===');
    console.log(`✓ Successfully applied: ${successCount}`);
    console.log(`⚠ Skipped (already exists): ${skipCount}`);
    console.log(`✗ Errors: ${errorCount}`);

    if (errorCount === 0) {
      console.log('\n🎉 All performance optimizations applied successfully!');
    } else {
      console.log('\n⚠️  Some optimizations failed to apply. Check the errors above.');
    }

    // Verify indexes were created
    console.log('\n=== Verifying Indexes ===');
    const indexQuery = `
      SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE tablename IN ('broker_accounts', 'broker_connection_status', 'broker_health_stats')
        AND indexname LIKE '%_idx'
      ORDER BY tablename, indexname
    `;
    
    const result = await client.query(indexQuery);
    console.log(`Found ${result.rows.length} performance indexes:`);
    result.rows.forEach(row => {
      console.log(`  - ${row.tablename}.${row.indexname}`);
    });

  } catch (error) {
    console.error('Failed to apply performance optimizations:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('\nDatabase connection closed.');
  }
}

// Run the script
applyPerformanceOptimizations().catch(console.error);