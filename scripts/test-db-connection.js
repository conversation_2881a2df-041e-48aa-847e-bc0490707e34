import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
import postgres from 'postgres';
import { config } from 'dotenv';

// Load environment variables
config();

const connection = postgres({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  user: process.env.DB_USER || 'admin',
  password: process.env.DB_PASSWORD || 'admin',
  database: process.env.DB_NAME || 'patterntrade',
  ssl: false,
});

const db = drizzle(connection);

async function testDatabaseConnection() {
  try {
    console.log('🔍 Testing database connection...');

    // Test basic connectivity
    const result = await db.execute(sql`SELECT NOW() as current_time, 'Hello PostgreSQL!' as message`);
    console.log('✅ Database connection successful!');
    console.log('📅 Current time:', result[0].current_time);
    console.log('💬 Message:', result[0].message);

    // Check if our tables exist
    console.log('\n🔍 Checking table existence...');

    const tables = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `);

    console.log('📋 Tables found:');
    tables.forEach((table, index) => {
      console.log(`  ${index + 1}. ${table.table_name}`);
    });

    // Check broker_credentials table structure
    if (tables.some((t) => t.table_name === 'broker_credentials')) {
      console.log('\n🔍 Checking broker_credentials table structure...');
      const brokerColumns = await db.execute(sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'broker_credentials' 
        ORDER BY ordinal_position;
      `);

      console.log('📋 broker_credentials columns:');
      brokerColumns.forEach((col, index) => {
        console.log(`  ${index + 1}. ${col.column_name} (${col.data_type}) - Nullable: ${col.is_nullable}`);
      });
    }

    // Check symbol_master table structure
    if (tables.some((t) => t.table_name === 'symbol_master')) {
      console.log('\n🔍 Checking symbol_master table structure...');
      const symbolColumns = await db.execute(sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'symbol_master' 
        ORDER BY ordinal_position;
      `);

      console.log('📋 symbol_master columns:');
      symbolColumns.forEach((col, index) => {
        console.log(`  ${index + 1}. ${col.column_name} (${col.data_type}) - Nullable: ${col.is_nullable}`);
      });
    }

    console.log('\n🎉 Database verification completed successfully!');
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await connection.end();
    console.log('🔌 Database connection closed.');
  }
}

testDatabaseConnection();
