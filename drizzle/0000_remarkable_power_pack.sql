CREATE TABLE "password_reset_tokens" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" varchar(32) NOT NULL,
	"created_by" varchar(255) NOT NULL,
	"updated_at" varchar(32) NOT NULL,
	"updated_by" varchar(255) NOT NULL,
	"deleted_at" varchar(32),
	"deleted_by" varchar(255),
	"user_id" integer NOT NULL,
	"token" varchar(255) NOT NULL,
	"expires_at" varchar(32) NOT NULL,
	"is_used" boolean DEFAULT false NOT NULL,
	CONSTRAINT "password_reset_tokens_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "broker_accounts" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" varchar(32) NOT NULL,
	"created_by" varchar(255) NOT NULL,
	"updated_at" varchar(32) NOT NULL,
	"updated_by" varchar(255) NOT NULL,
	"deleted_at" varchar(32),
	"deleted_by" varchar(255),
	"broker_name" varchar(256) NOT NULL,
	"user_id" text NOT NULL,
	"is_admin_account" boolean DEFAULT false NOT NULL,
	"api_key" text NOT NULL,
	"api_secret" text NOT NULL,
	"access_token" text,
	"status" varchar(50) DEFAULT 'active' NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" varchar(32) NOT NULL,
	"created_by" varchar(255) NOT NULL,
	"updated_at" varchar(32) NOT NULL,
	"updated_by" varchar(255) NOT NULL,
	"deleted_at" varchar(32),
	"deleted_by" varchar(255),
	"email" varchar(255) NOT NULL,
	"first_name" varchar(100) NOT NULL,
	"last_name" varchar(100) NOT NULL,
	"role" varchar(50) DEFAULT 'user' NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"password_hash" text NOT NULL,
	"last_login_at" varchar(32),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "password_reset_tokens" ADD CONSTRAINT "password_reset_tokens_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "password_reset_tokens_user_id_idx" ON "password_reset_tokens" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "password_reset_tokens_token_idx" ON "password_reset_tokens" USING btree ("token");--> statement-breakpoint
CREATE INDEX "password_reset_tokens_expires_at_idx" ON "password_reset_tokens" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "password_reset_tokens_user_active_idx" ON "password_reset_tokens" USING btree ("user_id","is_used","expires_at");--> statement-breakpoint
CREATE INDEX "broker_accounts_user_id_idx" ON "broker_accounts" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "broker_accounts_broker_name_idx" ON "broker_accounts" USING btree ("broker_name");--> statement-breakpoint
CREATE INDEX "broker_accounts_status_idx" ON "broker_accounts" USING btree ("status");--> statement-breakpoint
CREATE UNIQUE INDEX "broker_accounts_user_broker_unique_idx" ON "broker_accounts" USING btree ("user_id","broker_name");--> statement-breakpoint
CREATE INDEX "broker_accounts_admin_accounts_idx" ON "broker_accounts" USING btree ("is_admin_account");--> statement-breakpoint
CREATE INDEX "users_email_idx" ON "users" USING btree ("email");--> statement-breakpoint
CREATE INDEX "users_role_idx" ON "users" USING btree ("role");--> statement-breakpoint
CREATE INDEX "users_is_active_idx" ON "users" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "users_active_role_idx" ON "users" USING btree ("is_active","role");