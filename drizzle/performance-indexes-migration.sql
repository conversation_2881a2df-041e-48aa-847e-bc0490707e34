-- ==================== BROKER PERFORMANCE OPTIMIZATION MIGRATION ====================
-- This migration adds optimized database indexes for common query patterns
-- Requirements: 1.3 - Add optimized database indexes for common query patterns

-- ==================== BROKER ACCOUNTS TABLE PERFORMANCE INDEXES ====================

-- Composite index for filtering by user and connection status (common query pattern)
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_user_status_idx 
ON broker_accounts (user_id, connection_status);

-- Composite index for filtering by broker type and connection status (dashboard queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_type_status_idx 
ON broker_accounts (broker_type, connection_status);

-- Composite index for admin account filtering with connection status
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_admin_status_idx 
ON broker_accounts (is_admin_account, connection_status);

-- Index for last connected timestamp (for connection monitoring queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_last_connected_idx 
ON broker_accounts (last_connected_at);

-- Composite index for active accounts filtering (user + connected status + broker type)
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_active_accounts_idx 
ON broker_accounts (user_id, connection_status, broker_type);

-- Index for account name searches (case-insensitive)
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_account_name_idx 
ON broker_accounts (account_name);

-- Composite index for audit queries (created_at + user_id)
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_audit_idx 
ON broker_accounts (created_at, user_id);

-- Composite index for error tracking (connection_status + last_error presence)
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_error_tracking_idx 
ON broker_accounts (connection_status, last_error);

-- Partial index for connected accounts only (reduces index size)
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_connected_only_idx 
ON broker_accounts (user_id, broker_type, last_connected_at) 
WHERE connection_status = 'CONNECTED';

-- Partial index for admin accounts only
CREATE INDEX CONCURRENTLY IF NOT EXISTS broker_accounts_admin_only_idx 
ON broker_accounts (broker_type, connection_status, last_connected_at) 
WHERE is_admin_account = true;

-- ==================== BROKER CONNECTION STATUS TABLE PERFORMANCE INDEXES ====================

-- Composite index for status monitoring queries (account + status + timestamp)
CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_monitoring_idx 
ON broker_connection_status (broker_account_id, status, status_changed_at);

-- Index for retry scheduling queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_retry_schedule_idx 
ON broker_connection_status (next_retry_at, retry_count);

-- Composite index for error analysis (status + error presence + timestamp)
CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_error_analysis_idx 
ON broker_connection_status (status, error_message, status_changed_at);

-- Index for recent status changes (last 24 hours queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_recent_idx 
ON broker_connection_status (status_changed_at, status);

-- Partial index for failed connections only
CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_failed_only_idx 
ON broker_connection_status (broker_account_id, status_changed_at, retry_count) 
WHERE status IN ('FAILED', 'ERROR', 'DISCONNECTED');

-- Partial index for pending retries
CREATE INDEX CONCURRENTLY IF NOT EXISTS connection_status_pending_retry_idx 
ON broker_connection_status (next_retry_at, broker_account_id) 
WHERE next_retry_at IS NOT NULL;

-- ==================== BROKER HEALTH STATISTICS TABLE PERFORMANCE INDEXES ====================

-- Composite index for date range queries with account filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_date_range_idx 
ON broker_health_stats (stats_date, broker_account_id, updated_at);

-- Index for performance monitoring (response time analysis)
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_performance_idx 
ON broker_health_stats (average_response_time_ms, stats_date);

-- Index for uptime analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_uptime_idx 
ON broker_health_stats (current_uptime_seconds, total_downtime_seconds, stats_date);

-- Index for error tracking and analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_error_tracking_idx 
ON broker_health_stats (last_error_at, broker_account_id);

-- Index for heartbeat monitoring
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_heartbeat_idx 
ON broker_health_stats (last_heartbeat_at, broker_account_id);

-- Composite index for success rate calculations
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_success_rate_idx 
ON broker_health_stats (successful_connections, total_connections, stats_date);

-- Index for recent statistics (last 30 days)
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_recent_idx 
ON broker_health_stats (stats_date, updated_at);

-- Partial index for accounts with errors only
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_with_errors_idx 
ON broker_health_stats (broker_account_id, last_error_at, stats_date) 
WHERE last_error_at IS NOT NULL;

-- Partial index for high-performance accounts (low response time)
CREATE INDEX CONCURRENTLY IF NOT EXISTS health_stats_high_performance_idx 
ON broker_health_stats (broker_account_id, average_response_time_ms, stats_date) 
WHERE average_response_time_ms < 1000; -- Less than 1 second

-- ==================== PERFORMANCE MONITORING VIEWS ====================

-- Active broker accounts summary view
CREATE OR REPLACE VIEW active_broker_accounts_summary AS
SELECT 
  ba.broker_type,
  COUNT(*) as total_accounts,
  COUNT(CASE WHEN ba.connection_status = 'CONNECTED' THEN 1 END) as connected_accounts,
  COUNT(CASE WHEN ba.connection_status = 'FAILED' THEN 1 END) as failed_accounts,
  COUNT(CASE WHEN ba.is_admin_account = true THEN 1 END) as admin_accounts,
  AVG(EXTRACT(EPOCH FROM (NOW() - ba.last_connected_at))/3600) as avg_hours_since_connection
FROM broker_accounts ba
WHERE ba.deleted_at IS NULL
GROUP BY ba.broker_type;

-- Daily health statistics summary view
CREATE OR REPLACE VIEW daily_health_stats_summary AS
SELECT 
  bhs.stats_date,
  COUNT(DISTINCT bhs.broker_account_id) as accounts_tracked,
  AVG(bhs.average_response_time_ms) as avg_response_time,
  SUM(bhs.total_connections) as total_connections,
  SUM(bhs.successful_connections) as successful_connections,
  SUM(bhs.failed_connections) as failed_connections,
  AVG(bhs.current_uptime_seconds) as avg_uptime_seconds,
  COUNT(CASE WHEN bhs.last_error_at IS NOT NULL THEN 1 END) as accounts_with_errors
FROM broker_health_stats bhs
GROUP BY bhs.stats_date
ORDER BY bhs.stats_date DESC;

-- Connection status timeline view
CREATE OR REPLACE VIEW connection_status_timeline AS
SELECT 
  bcs.broker_account_id,
  ba.broker_type,
  ba.account_name,
  bcs.status,
  bcs.status_changed_at,
  bcs.error_message,
  bcs.retry_count,
  LAG(bcs.status) OVER (PARTITION BY bcs.broker_account_id ORDER BY bcs.status_changed_at) as previous_status,
  EXTRACT(EPOCH FROM (bcs.status_changed_at - LAG(bcs.status_changed_at) OVER (PARTITION BY bcs.broker_account_id ORDER BY bcs.status_changed_at)))/60 as minutes_in_previous_status
FROM broker_connection_status bcs
JOIN broker_accounts ba ON bcs.broker_account_id = ba.id
WHERE ba.deleted_at IS NULL
ORDER BY bcs.broker_account_id, bcs.status_changed_at DESC;

-- ==================== QUERY OPTIMIZATION FUNCTIONS ====================

-- Function to get broker performance summary (optimized for dashboard)
CREATE OR REPLACE FUNCTION get_broker_performance_summary()
RETURNS TABLE (
  broker_type varchar(50),
  total_accounts bigint,
  connected_count bigint,
  avg_response_time numeric,
  avg_uptime_hours numeric,
  recent_errors bigint,
  success_rate numeric
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ba.broker_type,
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN ba.connection_status = 'CONNECTED' THEN 1 END) as connected_count,
    ROUND(AVG(bhs.average_response_time_ms), 2) as avg_response_time,
    ROUND(AVG(bhs.current_uptime_seconds)/3600, 2) as avg_uptime_hours,
    COUNT(CASE WHEN bhs.last_error_at > NOW() - INTERVAL '24 hours' THEN 1 END) as recent_errors,
    ROUND(
      AVG(CASE 
        WHEN bhs.total_connections > 0 
        THEN (bhs.successful_connections::float / bhs.total_connections) * 100 
        ELSE 0 
      END), 2
    ) as success_rate
  FROM broker_accounts ba
  LEFT JOIN LATERAL (
    SELECT * FROM broker_health_stats 
    WHERE broker_account_id = ba.id 
    ORDER BY stats_date DESC 
    LIMIT 1
  ) bhs ON true
  WHERE ba.deleted_at IS NULL
  GROUP BY ba.broker_type
  ORDER BY connected_count DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get accounts requiring attention (optimized for monitoring)
CREATE OR REPLACE FUNCTION get_accounts_requiring_attention()
RETURNS TABLE (
  id integer,
  user_id text,
  broker_type varchar(50),
  account_name varchar(255),
  connection_status varchar(50),
  last_connected_at timestamp,
  last_error text,
  retry_count integer,
  next_retry_at timestamp,
  hours_disconnected numeric
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ba.id,
    ba.user_id,
    ba.broker_type,
    ba.account_name,
    ba.connection_status,
    ba.last_connected_at,
    ba.last_error,
    bcs.retry_count,
    bcs.next_retry_at,
    EXTRACT(EPOCH FROM (NOW() - ba.last_connected_at))/3600 as hours_disconnected
  FROM broker_accounts ba
  LEFT JOIN LATERAL (
    SELECT * FROM broker_connection_status 
    WHERE broker_account_id = ba.id 
    ORDER BY status_changed_at DESC 
    LIMIT 1
  ) bcs ON true
  WHERE ba.deleted_at IS NULL 
    AND (
      ba.connection_status IN ('FAILED', 'ERROR', 'DISCONNECTED')
      OR ba.last_connected_at < NOW() - INTERVAL '1 hour'
      OR bcs.retry_count > 3
    )
  ORDER BY ba.last_connected_at ASC NULLS FIRST
  LIMIT 100;
END;
$$ LANGUAGE plpgsql;

-- ==================== INDEX MAINTENANCE PROCEDURES ====================

-- Procedure to analyze table statistics for query optimization
CREATE OR REPLACE FUNCTION analyze_broker_tables()
RETURNS void AS $$
BEGIN
  ANALYZE broker_accounts;
  ANALYZE broker_connection_status;
  ANALYZE broker_health_stats;
  
  -- Log the analysis
  RAISE NOTICE 'Broker table statistics updated at %', NOW();
END;
$$ LANGUAGE plpgsql;

-- Procedure to get index usage statistics
CREATE OR REPLACE FUNCTION get_broker_index_usage()
RETURNS TABLE (
  schemaname text,
  tablename text,
  indexname text,
  idx_scan bigint,
  idx_tup_read bigint,
  idx_tup_fetch bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.schemaname,
    s.tablename,
    s.indexname,
    s.idx_scan,
    s.idx_tup_read,
    s.idx_tup_fetch
  FROM pg_stat_user_indexes s
  WHERE s.tablename IN ('broker_accounts', 'broker_connection_status', 'broker_health_stats')
  ORDER BY s.idx_scan DESC;
END;
$$ LANGUAGE plpgsql;

-- ==================== PERFORMANCE MONITORING SETUP ====================

-- Create a table to track query performance
CREATE TABLE IF NOT EXISTS broker_query_performance (
  id serial PRIMARY KEY,
  query_type varchar(100) NOT NULL,
  execution_time_ms integer NOT NULL,
  row_count integer NOT NULL,
  executed_at timestamp DEFAULT NOW(),
  query_hash varchar(64),
  parameters_hash varchar(64)
);

-- Index for query performance tracking
CREATE INDEX IF NOT EXISTS broker_query_performance_type_time_idx 
ON broker_query_performance (query_type, executed_at);

CREATE INDEX IF NOT EXISTS broker_query_performance_execution_time_idx 
ON broker_query_performance (execution_time_ms, executed_at);

-- Function to log query performance
CREATE OR REPLACE FUNCTION log_broker_query_performance(
  p_query_type varchar(100),
  p_execution_time_ms integer,
  p_row_count integer,
  p_query_hash varchar(64) DEFAULT NULL,
  p_parameters_hash varchar(64) DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  INSERT INTO broker_query_performance (
    query_type, 
    execution_time_ms, 
    row_count, 
    query_hash, 
    parameters_hash
  ) VALUES (
    p_query_type, 
    p_execution_time_ms, 
    p_row_count, 
    p_query_hash, 
    p_parameters_hash
  );
END;
$$ LANGUAGE plpgsql;

-- ==================== CLEANUP AND MAINTENANCE ====================

-- Function to clean up old performance logs (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_broker_query_performance()
RETURNS integer AS $$
DECLARE
  deleted_count integer;
BEGIN
  DELETE FROM broker_query_performance 
  WHERE executed_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RAISE NOTICE 'Cleaned up % old query performance records', deleted_count;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Schedule automatic cleanup (if pg_cron is available)
-- SELECT cron.schedule('cleanup-broker-performance', '0 2 * * *', 'SELECT cleanup_broker_query_performance();');

COMMIT;

-- ==================== VERIFICATION QUERIES ====================

-- Verify indexes were created successfully
SELECT 
  schemaname,
  tablename,
  indexname,
  indexdef
FROM pg_indexes 
WHERE tablename IN ('broker_accounts', 'broker_connection_status', 'broker_health_stats')
  AND indexname LIKE '%_idx'
ORDER BY tablename, indexname;

-- Check table sizes and index usage
SELECT 
  t.schemaname,
  t.tablename,
  pg_size_pretty(pg_total_relation_size(t.schemaname||'.'||t.tablename)) as table_size,
  pg_size_pretty(pg_indexes_size(t.schemaname||'.'||t.tablename)) as indexes_size
FROM pg_tables t
WHERE t.tablename IN ('broker_accounts', 'broker_connection_status', 'broker_health_stats')
ORDER BY pg_total_relation_size(t.schemaname||'.'||t.tablename) DESC;