CREATE TABLE "broker_connection_status" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"broker_account_id" integer NOT NULL,
	"status" varchar(50) NOT NULL,
	"status_changed_at" timestamp DEFAULT now() NOT NULL,
	"error_message" text,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"next_retry_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "broker_health_stats" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"broker_account_id" integer NOT NULL,
	"total_connections" integer DEFAULT 0 NOT NULL,
	"successful_connections" integer DEFAULT 0 NOT NULL,
	"failed_connections" integer DEFAULT 0 NOT NULL,
	"current_uptime_seconds" integer DEFAULT 0 NOT NULL,
	"total_downtime_seconds" integer DEFAULT 0 NOT NULL,
	"average_response_time_ms" numeric(10, 2),
	"last_heartbeat_at" timestamp,
	"last_error_at" timestamp,
	"last_error_message" text,
	"stats_date" date DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DROP INDEX "broker_accounts_broker_name_idx";--> statement-breakpoint
DROP INDEX "broker_accounts_status_idx";--> statement-breakpoint
DROP INDEX "broker_accounts_user_broker_unique_idx";--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "broker_type" varchar(50) NOT NULL;--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "account_name" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "encrypted_api_key" text NOT NULL;--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "encrypted_api_secret" text NOT NULL;--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "encrypted_access_token" text;--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "encrypted_refresh_token" text;--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "connection_status" varchar(50) DEFAULT 'DISCONNECTED' NOT NULL;--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "last_connected_at" timestamp;--> statement-breakpoint
ALTER TABLE "broker_accounts" ADD COLUMN "last_error" text;--> statement-breakpoint
ALTER TABLE "broker_connection_status" ADD CONSTRAINT "broker_connection_status_broker_account_id_broker_accounts_id_fk" FOREIGN KEY ("broker_account_id") REFERENCES "public"."broker_accounts"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "broker_health_stats" ADD CONSTRAINT "broker_health_stats_broker_account_id_broker_accounts_id_fk" FOREIGN KEY ("broker_account_id") REFERENCES "public"."broker_accounts"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "connection_status_account_idx" ON "broker_connection_status" USING btree ("broker_account_id");--> statement-breakpoint
CREATE INDEX "connection_status_timestamp_idx" ON "broker_connection_status" USING btree ("status_changed_at");--> statement-breakpoint
CREATE INDEX "health_stats_account_idx" ON "broker_health_stats" USING btree ("broker_account_id");--> statement-breakpoint
CREATE INDEX "health_stats_date_idx" ON "broker_health_stats" USING btree ("stats_date");--> statement-breakpoint
CREATE UNIQUE INDEX "health_stats_account_date_unique_idx" ON "broker_health_stats" USING btree ("broker_account_id","stats_date");--> statement-breakpoint
CREATE INDEX "broker_accounts_broker_type_idx" ON "broker_accounts" USING btree ("broker_type");--> statement-breakpoint
CREATE INDEX "broker_accounts_connection_status_idx" ON "broker_accounts" USING btree ("connection_status");--> statement-breakpoint
CREATE UNIQUE INDEX "broker_accounts_user_broker_unique_idx" ON "broker_accounts" USING btree ("user_id","broker_type");--> statement-breakpoint
ALTER TABLE "broker_accounts" DROP COLUMN "broker_name";--> statement-breakpoint
ALTER TABLE "broker_accounts" DROP COLUMN "api_key";--> statement-breakpoint
ALTER TABLE "broker_accounts" DROP COLUMN "api_secret";--> statement-breakpoint
ALTER TABLE "broker_accounts" DROP COLUMN "access_token";--> statement-breakpoint
ALTER TABLE "broker_accounts" DROP COLUMN "status";