import 'dotenv/config';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { ConfigTable } from '@app/core/config/config.model';
import { DEFAULT_CONFIGS, CONFIG_DESCRIPTIONS } from '@app/core/config/config.schema';
import { DateTimeUtilsService } from '@app/utils';

// Database connection
const connectionString = `postgresql://${process.env.DB_USER || 'admin'}:${process.env.DB_PASSWORD || 'admin'}@${process.env.DB_HOST || 'localhost'}:${process.env.DB_PORT || '5432'}/${process.env.DB_NAME || 'patterntrade'}`;

const client = postgres(connectionString);
const db = drizzle(client);

// Initialize utilities
const dateTimeUtils = new DateTimeUtilsService();

/**
 * Configuration seed data for all environments
 */
const CONFIGURATION_SEEDS = [
  // Service Configuration
  {
    key: 'API_BASE_URL',
    value: 'http://localhost:3000',
    description: CONFIG_DESCRIPTIONS.API_BASE_URL,
    category: 'service',
    environment: 'local',
    dataType: 'url',
    isActive: true,
  },
  {
    key: 'API_BASE_URL',
    value: 'http://api:3000',
    description: CONFIG_DESCRIPTIONS.API_BASE_URL,
    category: 'service',
    environment: 'docker',
    dataType: 'url',
    isActive: true,
  },
  {
    key: 'API_BASE_URL',
    value: 'https://dev-api.patterntrade.in',
    description: CONFIG_DESCRIPTIONS.API_BASE_URL,
    category: 'service',
    environment: 'development',
    dataType: 'url',
    isActive: true,
  },
  {
    key: 'API_BASE_URL',
    value: 'https://staging-api.patterntrade.in',
    description: CONFIG_DESCRIPTIONS.API_BASE_URL,
    category: 'service',
    environment: 'staging',
    dataType: 'url',
    isActive: true,
  },
  {
    key: 'API_BASE_URL',
    value: 'https://api.patterntrade.in',
    description: CONFIG_DESCRIPTIONS.API_BASE_URL,
    category: 'service',
    environment: 'production',
    dataType: 'url',
    isActive: true,
  },

  // Service URLs (optional - will be set when services are deployed)
  {
    key: 'ANALYSER_SERVICE_URL',
    value: 'http://localhost:3002',
    description: CONFIG_DESCRIPTIONS.ANALYSER_SERVICE_URL,
    category: 'service',
    environment: 'local',
    dataType: 'url',
    isActive: false, // Disabled by default until service is available
  },
  {
    key: 'OMS_SERVICE_URL',
    value: 'http://localhost:3003',
    description: CONFIG_DESCRIPTIONS.OMS_SERVICE_URL,
    category: 'service',
    environment: 'local',
    dataType: 'url',
    isActive: false,
  },
  {
    key: 'TICKER_SERVICE_URL',
    value: 'http://localhost:3004',
    description: CONFIG_DESCRIPTIONS.TICKER_SERVICE_URL,
    category: 'service',
    environment: 'local',
    dataType: 'url',
    isActive: false,
  },
  {
    key: 'SIMULATOR_SERVICE_URL',
    value: 'http://localhost:3005',
    description: CONFIG_DESCRIPTIONS.SIMULATOR_SERVICE_URL,
    category: 'service',
    environment: 'local',
    dataType: 'url',
    isActive: false,
  },

  // Broker Configuration (same for all environments initially)
  ...['local', 'docker', 'development', 'staging', 'production'].flatMap(env => [
    {
      key: 'BROKER_KEY_ROTATION_INTERVAL',
      value: String(DEFAULT_CONFIGS.broker.BROKER_KEY_ROTATION_INTERVAL),
      description: CONFIG_DESCRIPTIONS.BROKER_KEY_ROTATION_INTERVAL,
      category: 'broker',
      environment: env,
      dataType: 'number',
      isActive: true,
    },
    {
      key: 'BROKER_HEALTH_CHECK_INTERVAL',
      value: String(DEFAULT_CONFIGS.broker.BROKER_HEALTH_CHECK_INTERVAL),
      description: CONFIG_DESCRIPTIONS.BROKER_HEALTH_CHECK_INTERVAL,
      category: 'broker',
      environment: env,
      dataType: 'number',
      isActive: true,
    },
    {
      key: 'BROKER_CONNECTION_TIMEOUT',
      value: String(DEFAULT_CONFIGS.broker.BROKER_CONNECTION_TIMEOUT),
      description: CONFIG_DESCRIPTIONS.BROKER_CONNECTION_TIMEOUT,
      category: 'broker',
      environment: env,
      dataType: 'number',
      isActive: true,
    },
    {
      key: 'BROKER_MAX_RETRY_ATTEMPTS',
      value: String(DEFAULT_CONFIGS.broker.BROKER_MAX_RETRY_ATTEMPTS),
      description: CONFIG_DESCRIPTIONS.BROKER_MAX_RETRY_ATTEMPTS,
      category: 'broker',
      environment: env,
      dataType: 'number',
      isActive: true,
    },
    {
      key: 'BROKER_SESSION_TIMEOUT',
      value: String(DEFAULT_CONFIGS.broker.BROKER_SESSION_TIMEOUT),
      description: CONFIG_DESCRIPTIONS.BROKER_SESSION_TIMEOUT,
      category: 'broker',
      environment: env,
      dataType: 'number',
      isActive: true,
    },
  ]),

  // Security Configuration
  ...['local', 'docker', 'development', 'staging', 'production'].flatMap(env => [
    {
      key: 'BROKER_RATE_LIMIT_REQUESTS',
      value: String(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_REQUESTS),
      description: CONFIG_DESCRIPTIONS.BROKER_RATE_LIMIT_REQUESTS,
      category: 'security',
      environment: env,
      dataType: 'number',
      isActive: true,
    },
    {
      key: 'BROKER_RATE_LIMIT_WINDOW',
      value: String(DEFAULT_CONFIGS.security.BROKER_RATE_LIMIT_WINDOW),
      description: CONFIG_DESCRIPTIONS.BROKER_RATE_LIMIT_WINDOW,
      category: 'security',
      environment: env,
      dataType: 'number',
      isActive: true,
    },
  ]),

  // Database Configuration
  ...['local', 'docker', 'development', 'staging', 'production'].flatMap(env => [
    {
      key: 'QUESTDB_MAX_CONNECTIONS',
      value: String(DEFAULT_CONFIGS.database.QUESTDB_MAX_CONNECTIONS),
      description: CONFIG_DESCRIPTIONS.QUESTDB_MAX_CONNECTIONS,
      category: 'database',
      environment: env,
      dataType: 'number',
      isActive: true,
    },
  ]),
];

/**
 * Seed configuration data
 */
async function seedConfigurations() {
  console.log('🌱 Starting configuration seeding...');

  try {
    // Add audit fields to all configurations
    const currentTime = dateTimeUtils.getCurrentUtcDateTime();
    const configsWithAudit = CONFIGURATION_SEEDS.map(config => ({
      ...config,
      createdAt: currentTime,
      createdBy: 'system',
      updatedAt: currentTime,
      updatedBy: 'system',
    }));

    // Insert configurations in batches to avoid conflicts
    const batchSize = 50;
    let insertedCount = 0;

    for (let i = 0; i < configsWithAudit.length; i += batchSize) {
      const batch = configsWithAudit.slice(i, i + batchSize);
      
      try {
        await db.insert(ConfigTable).values(batch).onConflictDoNothing();
        insertedCount += batch.length;
        console.log(`✅ Inserted batch ${Math.floor(i / batchSize) + 1} (${batch.length} configurations)`);
      } catch (error) {
        console.warn(`⚠️  Batch ${Math.floor(i / batchSize) + 1} had conflicts (likely duplicates):`, error);
      }
    }

    console.log(`🎉 Configuration seeding completed! Processed ${insertedCount} configurations.`);
    
    // Display summary by environment
    const environments = [...new Set(CONFIGURATION_SEEDS.map(c => c.environment))];
    console.log('\n📊 Configuration Summary:');
    for (const env of environments) {
      const count = CONFIGURATION_SEEDS.filter(c => c.environment === env).length;
      console.log(`  ${env}: ${count} configurations`);
    }

  } catch (error) {
    console.error('❌ Configuration seeding failed:', error);
    throw error;
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    await seedConfigurations();
  } catch (error) {
    console.error('❌ Seeding process failed:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('🔌 Database connection closed.');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { seedConfigurations };
