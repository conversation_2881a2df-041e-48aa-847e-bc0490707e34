CREATE TABLE "configurations" (
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" varchar(32) NOT NULL,
	"created_by" varchar(255) NOT NULL,
	"updated_at" varchar(32) NOT NULL,
	"updated_by" varchar(255) NOT NULL,
	"deleted_at" varchar(32),
	"deleted_by" varchar(255),
	"key" varchar(255) NOT NULL,
	"value" text NOT NULL,
	"description" varchar(500) NOT NULL,
	"category" varchar(50) NOT NULL,
	"environment" varchar(20) NOT NULL,
	"data_type" varchar(20) NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	CONSTRAINT "unique_key_environment" UNIQUE("key","environment")
);
--> statement-breakpoint
CREATE INDEX "idx_config_key" ON "configurations" USING btree ("key");--> statement-breakpoint
CREATE INDEX "idx_config_environment" ON "configurations" USING btree ("environment");--> statement-breakpoint
CREATE INDEX "idx_config_category" ON "configurations" USING btree ("category");--> statement-breakpoint
CREATE INDEX "idx_config_active" ON "configurations" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "idx_config_key_environment" ON "configurations" USING btree ("key","environment");--> statement-breakpoint
CREATE INDEX "idx_config_category_environment" ON "configurations" USING btree ("category","environment");--> statement-breakpoint
CREATE INDEX "idx_config_created_at" ON "configurations" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "idx_config_updated_at" ON "configurations" USING btree ("updated_at");--> statement-breakpoint
CREATE INDEX "broker_accounts_user_status_idx" ON "broker_accounts" USING btree ("user_id","connection_status");--> statement-breakpoint
CREATE INDEX "broker_accounts_type_status_idx" ON "broker_accounts" USING btree ("broker_type","connection_status");--> statement-breakpoint
CREATE INDEX "broker_accounts_admin_status_idx" ON "broker_accounts" USING btree ("is_admin_account","connection_status");--> statement-breakpoint
CREATE INDEX "broker_accounts_last_connected_idx" ON "broker_accounts" USING btree ("last_connected_at");--> statement-breakpoint
CREATE INDEX "broker_accounts_active_accounts_idx" ON "broker_accounts" USING btree ("user_id","connection_status","broker_type");--> statement-breakpoint
CREATE INDEX "broker_accounts_account_name_idx" ON "broker_accounts" USING btree ("account_name");--> statement-breakpoint
CREATE INDEX "broker_accounts_audit_idx" ON "broker_accounts" USING btree ("created_at","user_id");--> statement-breakpoint
CREATE INDEX "broker_accounts_error_tracking_idx" ON "broker_accounts" USING btree ("connection_status","last_error");--> statement-breakpoint
CREATE INDEX "broker_accounts_connected_only_idx" ON "broker_accounts" USING btree ("user_id","broker_type","last_connected_at") WHERE "broker_accounts"."connection_status" = 'CONNECTED';--> statement-breakpoint
CREATE INDEX "broker_accounts_admin_only_idx" ON "broker_accounts" USING btree ("broker_type","connection_status","last_connected_at") WHERE "broker_accounts"."is_admin_account" = true;--> statement-breakpoint
CREATE INDEX "connection_status_monitoring_idx" ON "broker_connection_status" USING btree ("broker_account_id","status","status_changed_at");--> statement-breakpoint
CREATE INDEX "connection_status_retry_schedule_idx" ON "broker_connection_status" USING btree ("next_retry_at","retry_count");--> statement-breakpoint
CREATE INDEX "connection_status_error_analysis_idx" ON "broker_connection_status" USING btree ("status","error_message","status_changed_at");--> statement-breakpoint
CREATE INDEX "connection_status_recent_idx" ON "broker_connection_status" USING btree ("status_changed_at","status");--> statement-breakpoint
CREATE INDEX "connection_status_failed_only_idx" ON "broker_connection_status" USING btree ("broker_account_id","status_changed_at","retry_count") WHERE "broker_connection_status"."status" IN ('FAILED', 'ERROR', 'DISCONNECTED');--> statement-breakpoint
CREATE INDEX "connection_status_pending_retry_idx" ON "broker_connection_status" USING btree ("next_retry_at","broker_account_id") WHERE "broker_connection_status"."next_retry_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "health_stats_date_range_idx" ON "broker_health_stats" USING btree ("stats_date","broker_account_id","updated_at");--> statement-breakpoint
CREATE INDEX "health_stats_performance_idx" ON "broker_health_stats" USING btree ("average_response_time_ms","stats_date");--> statement-breakpoint
CREATE INDEX "health_stats_uptime_idx" ON "broker_health_stats" USING btree ("current_uptime_seconds","total_downtime_seconds","stats_date");--> statement-breakpoint
CREATE INDEX "health_stats_error_tracking_idx" ON "broker_health_stats" USING btree ("last_error_at","broker_account_id");--> statement-breakpoint
CREATE INDEX "health_stats_heartbeat_idx" ON "broker_health_stats" USING btree ("last_heartbeat_at","broker_account_id");--> statement-breakpoint
CREATE INDEX "health_stats_success_rate_idx" ON "broker_health_stats" USING btree ("successful_connections","total_connections","stats_date");--> statement-breakpoint
CREATE INDEX "health_stats_recent_idx" ON "broker_health_stats" USING btree ("stats_date","updated_at");--> statement-breakpoint
CREATE INDEX "health_stats_with_errors_idx" ON "broker_health_stats" USING btree ("broker_account_id","last_error_at","stats_date") WHERE "broker_health_stats"."last_error_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "health_stats_high_performance_idx" ON "broker_health_stats" USING btree ("broker_account_id","average_response_time_ms","stats_date") WHERE "broker_health_stats"."average_response_time_ms" < 1000;