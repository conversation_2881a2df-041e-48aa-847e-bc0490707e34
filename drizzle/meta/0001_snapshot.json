{"id": "32c2dece-f0a3-4132-84dd-c54aeeca295e", "prevId": "2b2107d4-caf5-40a4-965f-be5db28999e6", "version": "7", "dialect": "postgresql", "tables": {"public.password_reset_tokens": {"name": "password_reset_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"password_reset_tokens_user_id_idx": {"name": "password_reset_tokens_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "password_reset_tokens_token_idx": {"name": "password_reset_tokens_token_idx", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "password_reset_tokens_expires_at_idx": {"name": "password_reset_tokens_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "password_reset_tokens_user_active_idx": {"name": "password_reset_tokens_user_active_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_used", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"password_reset_tokens_user_id_users_id_fk": {"name": "password_reset_tokens_user_id_users_id_fk", "tableFrom": "password_reset_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"password_reset_tokens_token_unique": {"name": "password_reset_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.broker_accounts": {"name": "broker_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "broker_type": {"name": "broker_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "account_name": {"name": "account_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_admin_account": {"name": "is_admin_account", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "encrypted_api_key": {"name": "encrypted_api_key", "type": "text", "primaryKey": false, "notNull": true}, "encrypted_api_secret": {"name": "encrypted_api_secret", "type": "text", "primaryKey": false, "notNull": true}, "encrypted_access_token": {"name": "encrypted_access_token", "type": "text", "primaryKey": false, "notNull": false}, "encrypted_refresh_token": {"name": "encrypted_refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "connection_status": {"name": "connection_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'DISCONNECTED'"}, "last_connected_at": {"name": "last_connected_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_error": {"name": "last_error", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"broker_accounts_user_id_idx": {"name": "broker_accounts_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "broker_accounts_broker_type_idx": {"name": "broker_accounts_broker_type_idx", "columns": [{"expression": "broker_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "broker_accounts_connection_status_idx": {"name": "broker_accounts_connection_status_idx", "columns": [{"expression": "connection_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "broker_accounts_user_broker_unique_idx": {"name": "broker_accounts_user_broker_unique_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "broker_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "broker_accounts_admin_accounts_idx": {"name": "broker_accounts_admin_accounts_idx", "columns": [{"expression": "is_admin_account", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.broker_connection_status": {"name": "broker_connection_status", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "broker_account_id": {"name": "broker_account_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status_changed_at": {"name": "status_changed_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "next_retry_at": {"name": "next_retry_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"connection_status_account_idx": {"name": "connection_status_account_idx", "columns": [{"expression": "broker_account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "connection_status_timestamp_idx": {"name": "connection_status_timestamp_idx", "columns": [{"expression": "status_changed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"broker_connection_status_broker_account_id_broker_accounts_id_fk": {"name": "broker_connection_status_broker_account_id_broker_accounts_id_fk", "tableFrom": "broker_connection_status", "tableTo": "broker_accounts", "columnsFrom": ["broker_account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.broker_health_stats": {"name": "broker_health_stats", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "broker_account_id": {"name": "broker_account_id", "type": "integer", "primaryKey": false, "notNull": true}, "total_connections": {"name": "total_connections", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "successful_connections": {"name": "successful_connections", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "failed_connections": {"name": "failed_connections", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "current_uptime_seconds": {"name": "current_uptime_seconds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_downtime_seconds": {"name": "total_downtime_seconds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "average_response_time_ms": {"name": "average_response_time_ms", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "last_heartbeat_at": {"name": "last_heartbeat_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_error_at": {"name": "last_error_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_error_message": {"name": "last_error_message", "type": "text", "primaryKey": false, "notNull": false}, "stats_date": {"name": "stats_date", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"health_stats_account_idx": {"name": "health_stats_account_idx", "columns": [{"expression": "broker_account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "health_stats_date_idx": {"name": "health_stats_date_idx", "columns": [{"expression": "stats_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "health_stats_account_date_unique_idx": {"name": "health_stats_account_date_unique_idx", "columns": [{"expression": "broker_account_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "stats_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"broker_health_stats_broker_account_id_broker_accounts_id_fk": {"name": "broker_health_stats_broker_account_id_broker_accounts_id_fk", "tableFrom": "broker_health_stats", "tableTo": "broker_accounts", "columnsFrom": ["broker_account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'user'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true}, "last_login_at": {"name": "last_login_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_idx": {"name": "users_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_is_active_idx": {"name": "users_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_active_role_idx": {"name": "users_active_role_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}