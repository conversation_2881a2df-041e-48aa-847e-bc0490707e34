{"id": "2b2107d4-caf5-40a4-965f-be5db28999e6", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.password_reset_tokens": {"name": "password_reset_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"password_reset_tokens_user_id_idx": {"name": "password_reset_tokens_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "password_reset_tokens_token_idx": {"name": "password_reset_tokens_token_idx", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "password_reset_tokens_expires_at_idx": {"name": "password_reset_tokens_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "password_reset_tokens_user_active_idx": {"name": "password_reset_tokens_user_active_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_used", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"password_reset_tokens_user_id_users_id_fk": {"name": "password_reset_tokens_user_id_users_id_fk", "tableFrom": "password_reset_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"password_reset_tokens_token_unique": {"name": "password_reset_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.broker_accounts": {"name": "broker_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "broker_name": {"name": "broker_name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "is_admin_account": {"name": "is_admin_account", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "api_key": {"name": "api_key", "type": "text", "primaryKey": false, "notNull": true}, "api_secret": {"name": "api_secret", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'active'"}}, "indexes": {"broker_accounts_user_id_idx": {"name": "broker_accounts_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "broker_accounts_broker_name_idx": {"name": "broker_accounts_broker_name_idx", "columns": [{"expression": "broker_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "broker_accounts_status_idx": {"name": "broker_accounts_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "broker_accounts_user_broker_unique_idx": {"name": "broker_accounts_user_broker_unique_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "broker_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "broker_accounts_admin_accounts_idx": {"name": "broker_accounts_admin_accounts_idx", "columns": [{"expression": "is_admin_account", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'user'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true}, "last_login_at": {"name": "last_login_at", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_idx": {"name": "users_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_is_active_idx": {"name": "users_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_active_role_idx": {"name": "users_active_role_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}